import type { Metada<PERSON> } from "next";
import { Geist, <PERSON>ei<PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Sistema de Validação de Residência",
  description: "Sistema integrador para validação automática de comprovantes de residência usando dados do Google Maps Timeline",
  keywords: "comprovante residência, validação automática, google maps, timeline, lgpd",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="pt-BR">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-gray-50 min-h-screen`}
      >
        <div className="min-h-screen">
          {children}
        </div>
      </body>
    </html>
  );
}
