// Utilitários para validação de residência
const { LocationData, NightlyPresenceRecord, ValidationResult, ValidationConfig } = require('../types/residence');

// Configuração padrão de validação
const DEFAULT_CONFIG = {
  VALIDATION: {
    MAX_DISTANCE_FROM_HOME: 100, // metros
    MIN_PRESENCE_HOURS: 8,
    NIGHT_START_HOUR: 18,
    NIGHT_END_HOUR: 6,
    MIN_DAYS: 15,
    CONFIDENCE_THRESHOLD: 0.7,
  },
};

/**
 * Calcula a distância entre duas coordenadas usando a fórmula de Haversine
 */
function calculateDistance(lat1, lon1, lat2, lon2) {
  const R = 6371000; // Raio da Terra em metros
  const φ1 = (lat1 * Math.PI) / 180;
  const φ2 = (lat2 * Math.PI) / 180;
  const Δφ = ((lat2 - lat1) * Math.PI) / 180;
  const Δλ = ((lon2 - lon1) * Math.PI) / 180;

  const a =
    Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
    Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return R * c; // Distância em metros
}

/**
 * Verifica se um ponto está dentro do raio de residência
 */
function isWithinHomeRadius(pointLat, pointLon, homeLat, homeLon, maxDistance = DEFAULT_CONFIG.VALIDATION.MAX_DISTANCE_FROM_HOME) {
  const distance = calculateDistance(pointLat, pointLon, homeLat, homeLon);
  return distance <= maxDistance;
}

/**
 * Converte timestamp para horário local
 */
function formatTimestamp(timestamp) {
  return new Date(timestamp);
}

/**
 * Verifica se um horário está dentro do período noturno (18h às 6h)
 */
function isNightTime(date) {
  const hour = date.getHours();
  return hour >= DEFAULT_CONFIG.VALIDATION.NIGHT_START_HOUR || hour < DEFAULT_CONFIG.VALIDATION.NIGHT_END_HOUR;
}

/**
 * Agrupa dados de localização por dia
 */
function groupLocationsByDay(locations) {
  return locations.reduce((groups, location) => {
    const date = formatTimestamp(location.timestamp).toISOString().split('T')[0];
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(location);
    return groups;
  }, {});
}

/**
 * Analisa a presença noturna em um dia específico
 */
function analyzeNightlyPresence(dayLocations, homeLat, homeLon, date) {
  // Filtra apenas os pontos do período noturno (18h às 6h)
  const nightlyPoints = dayLocations.filter(location => {
    const locationDate = formatTimestamp(location.timestamp);
    return isNightTime(locationDate);
  });

  // Conta quantos pontos estão dentro do raio de casa
  const homePoints = nightlyPoints.filter(location =>
    isWithinHomeRadius(location.latitude, location.longitude, homeLat, homeLon)
  );

  // Calcula estatísticas
  const totalPoints = nightlyPoints.length;
  const homePointsCount = homePoints.length;
  const presenceRatio = totalPoints > 0 ? homePointsCount / totalPoints : 0;

  // Calcula distância média de casa
  const distances = nightlyPoints.map(location =>
    calculateDistance(location.latitude, location.longitude, homeLat, homeLon)
  );
  const averageDistance = distances.length > 0 
    ? distances.reduce((sum, dist) => sum + dist, 0) / distances.length 
    : 0;

  // Determina se estava presente em casa
  const isPresent = presenceRatio >= DEFAULT_CONFIG.VALIDATION.CONFIDENCE_THRESHOLD;

  // Calcula confiança baseada na quantidade de pontos e proximidade
  let confidence = 0;
  if (totalPoints > 0) {
    confidence = Math.min(1, (homePointsCount / totalPoints) * (totalPoints / 10));
  }

  return {
    date,
    startTime: `${DEFAULT_CONFIG.VALIDATION.NIGHT_START_HOUR}:00`,
    endTime: `${DEFAULT_CONFIG.VALIDATION.NIGHT_END_HOUR}:00`,
    isPresent,
    confidence,
    locationPoints: nightlyPoints,
    distanceFromHome: averageDistance,
  };
}

/**
 * Valida residência baseada nos dados de localização
 */
function validateResidence(locations, homeLat, homeLon, startDate, endDate) {
  try {
    // Agrupa localizações por dia
    const locationsByDay = groupLocationsByDay(locations);

    // Analisa cada dia
    const nightlyPresence = [];
    const dateRange = getDateRange(startDate, endDate);

    for (const date of dateRange) {
      const dayLocations = locationsByDay[date] || [];
      const presence = analyzeNightlyPresence(dayLocations, homeLat, homeLon, date);
      nightlyPresence.push(presence);
    }

    // Calcula estatísticas gerais
    const totalNights = nightlyPresence.length;
    const presentNights = nightlyPresence.filter(p => p.isPresent).length;
    const presencePercentage = totalNights > 0 ? (presentNights / totalNights) * 100 : 0;

    // Calcula horas médias de presença
    const averagePresenceHours = nightlyPresence.reduce((sum, p) => {
      if (p.isPresent) {
        return sum + DEFAULT_CONFIG.VALIDATION.MIN_PRESENCE_HOURS;
      }
      return sum;
    }, 0) / Math.max(presentNights, 1);

    // Calcula distância média de casa
    const allDistances = nightlyPresence.flatMap(p => 
      p.locationPoints.map(loc => 
        calculateDistance(loc.latitude, loc.longitude, homeLat, homeLon)
      )
    );
    const averageDistanceFromHome = allDistances.length > 0 
      ? allDistances.reduce((sum, dist) => sum + dist, 0) / allDistances.length 
      : 0;

    // Calcula score de validação
    const score = calculateValidationScore(presencePercentage, averageDistanceFromHome, totalNights);

    // Determina se é válido
    const isValid = score >= DEFAULT_CONFIG.VALIDATION.CONFIDENCE_THRESHOLD && 
                    totalNights >= DEFAULT_CONFIG.VALIDATION.MIN_DAYS &&
                    presencePercentage >= 70;

    const result = {
      isValid,
      score,
      totalDays: totalNights,
      validDays: presentNights,
      averageDistanceFromHome,
      nightlyPresence,
      summary: {
        totalNights,
        presentNights,
        presencePercentage,
        averagePresenceHours,
      },
      recommendations: generateRecommendations(presencePercentage, averageDistanceFromHome, totalNights),
      warnings: generateWarnings(presencePercentage, averageDistanceFromHome, totalNights),
    };

    return result;
  } catch (error) {
    console.error('Erro na validação de residência:', error);
    throw error;
  }
}

/**
 * Calcula score de validação baseado em múltiplos fatores
 */
function calculateValidationScore(presencePercentage, averageDistance, totalDays) {
  // Score baseado na presença (peso 50%)
  const presenceScore = Math.min(1, presencePercentage / 100);

  // Score baseado na distância (peso 30%)
  const distanceScore = Math.max(0, 1 - (averageDistance / 500)); // Penaliza distâncias > 500m

  // Score baseado na quantidade de dias (peso 20%)
  const daysScore = Math.min(1, totalDays / DEFAULT_CONFIG.VALIDATION.MIN_DAYS);

  // Score final ponderado
  const finalScore = (presenceScore * 0.5) + (distanceScore * 0.3) + (daysScore * 0.2);

  return Math.round(finalScore * 100) / 100; // Arredonda para 2 casas decimais
}

/**
 * Gera recomendações baseadas na análise
 */
function generateRecommendations(presencePercentage, averageDistance, totalDays) {
  const recommendations = [];

  if (presencePercentage < 70) {
    recommendations.push('Aumente o período de análise para obter mais dados de presença');
  }

  if (averageDistance > 200) {
    recommendations.push('Verifique se o endereço informado está correto');
  }

  if (totalDays < DEFAULT_CONFIG.VALIDATION.MIN_DAYS) {
    recommendations.push(`Colete dados por pelo menos ${DEFAULT_CONFIG.VALIDATION.MIN_DAYS} dias`);
  }

  return recommendations;
}

/**
 * Gera avisos baseados na análise
 */
function generateWarnings(presencePercentage, averageDistance, totalDays) {
  const warnings = [];

  if (presencePercentage < 50) {
    warnings.push('Baixa presença noturna detectada no endereço');
  }

  if (averageDistance > 500) {
    warnings.push('Distância média muito alta do endereço informado');
  }

  if (totalDays < 7) {
    warnings.push('Período de análise muito curto para validação confiável');
  }

  return warnings;
}

/**
 * Gera array de datas entre duas datas
 */
function getDateRange(startDate, endDate) {
  const dates = [];
  const current = new Date(startDate);
  const end = new Date(endDate);

  while (current <= end) {
    dates.push(current.toISOString().split('T')[0]);
    current.setDate(current.getDate() + 1);
  }

  return dates;
}

/**
 * Valida se um endereço está no formato correto
 */
function validateAddress(address) {
  if (!address || typeof address !== 'string') {
    return { isValid: false, error: 'Endereço é obrigatório' };
  }

  if (address.length < 10) {
    return { isValid: false, error: 'Endereço muito curto' };
  }

  if (address.length > 200) {
    return { isValid: false, error: 'Endereço muito longo' };
  }

  return { isValid: true };
}

/**
 * Valida coordenadas geográficas
 */
function validateCoordinates(latitude, longitude) {
  if (typeof latitude !== 'number' || typeof longitude !== 'number') {
    return { isValid: false, error: 'Coordenadas devem ser números' };
  }

  if (latitude < -90 || latitude > 90) {
    return { isValid: false, error: 'Latitude deve estar entre -90 e 90' };
  }

  if (longitude < -180 || longitude > 180) {
    return { isValid: false, error: 'Longitude deve estar entre -180 e 180' };
  }

  return { isValid: true };
}

/**
 * Valida período de datas
 */
function validateDateRange(startDate, endDate) {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const now = new Date();

  if (isNaN(start.getTime()) || isNaN(end.getTime())) {
    return { isValid: false, error: 'Datas inválidas' };
  }

  if (start >= end) {
    return { isValid: false, error: 'Data de início deve ser anterior à data de fim' };
  }

  if (end > now) {
    return { isValid: false, error: 'Data de fim não pode ser no futuro' };
  }

  const diffDays = Math.ceil((end - start) / (1000 * 60 * 60 * 24));
  if (diffDays > 180) {
    return { isValid: false, error: 'Período máximo de análise é 180 dias' };
  }

  return { isValid: true };
}

module.exports = {
  calculateDistance,
  isWithinHomeRadius,
  formatTimestamp,
  isNightTime,
  groupLocationsByDay,
  analyzeNightlyPresence,
  validateResidence,
  calculateValidationScore,
  generateRecommendations,
  generateWarnings,
  getDateRange,
  validateAddress,
  validateCoordinates,
  validateDateRange,
  DEFAULT_CONFIG,
};
