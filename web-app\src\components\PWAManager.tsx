import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ton,
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Box,
  IconButton
} from '@mui/material';
import {
  InstallMobile,
  Notifications,
  Close,
  GetApp
} from '@mui/icons-material';

interface PWAManagerProps {
  children: React.ReactNode;
}

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

const PWAManager: React.FC<PWAManagerProps> = ({ children }) => {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [showInstallPrompt, setShowInstallPrompt] = useState(false);
  const [showUpdatePrompt, setShowUpdatePrompt] = useState(false);
  const [showNotificationPrompt, setShowNotificationPrompt] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  const [swRegistration, setSwRegistration] = useState<ServiceWorkerRegistration | null>(null);

  useEffect(() => {
    // Verificar se já está instalado
    if (window.matchMedia('(display-mode: standalone)').matches) {
      setIsInstalled(true);
    }

    // Registrar Service Worker
    if ('serviceWorker' in navigator) {
      registerServiceWorker();
    }

    // Listener para prompt de instalação
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      setDeferredPrompt(e as BeforeInstallPromptEvent);
      
      // Mostrar prompt após 30 segundos se não estiver instalado
      setTimeout(() => {
        if (!isInstalled) {
          setShowInstallPrompt(true);
        }
      }, 30000);
    };

    // Listener para app instalado
    const handleAppInstalled = () => {
      setIsInstalled(true);
      setShowInstallPrompt(false);
      setDeferredPrompt(null);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    // Verificar permissão de notificação
    if ('Notification' in window && Notification.permission === 'default') {
      setTimeout(() => {
        setShowNotificationPrompt(true);
      }, 10000);
    }

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, [isInstalled]);

  const registerServiceWorker = async () => {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js');
      setSwRegistration(registration);

      // Verificar atualizações
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing;
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              setShowUpdatePrompt(true);
            }
          });
        }
      });

      console.log('[PWA] Service Worker registered successfully');
    } catch (error) {
      console.error('[PWA] Service Worker registration failed:', error);
    }
  };

  const handleInstallApp = async () => {
    if (deferredPrompt) {
      deferredPrompt.prompt();
      const { outcome } = await deferredPrompt.userChoice;
      
      if (outcome === 'accepted') {
        console.log('[PWA] User accepted the install prompt');
      } else {
        console.log('[PWA] User dismissed the install prompt');
      }
      
      setDeferredPrompt(null);
      setShowInstallPrompt(false);
    }
  };

  const handleUpdateApp = () => {
    if (swRegistration && swRegistration.waiting) {
      swRegistration.waiting.postMessage({ type: 'SKIP_WAITING' });
      window.location.reload();
    }
  };

  const handleEnableNotifications = async () => {
    try {
      const permission = await Notification.requestPermission();
      
      if (permission === 'granted' && swRegistration) {
        // Subscrever para push notifications
        const subscription = await swRegistration.pushManager.subscribe({
          userVisibleOnly: true,
          applicationServerKey: urlBase64ToUint8Array(process.env.REACT_APP_VAPID_PUBLIC_KEY || '')
        });

        // Enviar subscription para o servidor
        await fetch('/api/notifications/subscribe', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(subscription),
        });

        console.log('[PWA] Push notifications enabled');
      }
    } catch (error) {
      console.error('[PWA] Error enabling notifications:', error);
    }
    
    setShowNotificationPrompt(false);
  };

  const urlBase64ToUint8Array = (base64String: string) => {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  };

  return (
    <>
      {children}

      {/* Prompt de Instalação */}
      <Dialog
        open={showInstallPrompt}
        onClose={() => setShowInstallPrompt(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <InstallMobile color="primary" />
            Instalar CartórioTech
          </Box>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" paragraph>
            Instale o CartórioTech em seu dispositivo para uma experiência mais rápida e conveniente!
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, mt: 2 }}>
            <Typography variant="body2" color="text.secondary">
              ✓ Acesso rápido pela tela inicial
            </Typography>
            <Typography variant="body2" color="text.secondary">
              ✓ Funciona offline (funcionalidades limitadas)
            </Typography>
            <Typography variant="body2" color="text.secondary">
              ✓ Notificações push para atualizações
            </Typography>
            <Typography variant="body2" color="text.secondary">
              ✓ Interface otimizada para seu dispositivo
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowInstallPrompt(false)}>
            Agora Não
          </Button>
          <Button
            variant="contained"
            onClick={handleInstallApp}
            startIcon={<GetApp />}
          >
            Instalar
          </Button>
        </DialogActions>
      </Dialog>

      {/* Prompt de Atualização */}
      <Snackbar
        open={showUpdatePrompt}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        action={
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              color="inherit"
              size="small"
              onClick={handleUpdateApp}
            >
              Atualizar
            </Button>
            <IconButton
              size="small"
              color="inherit"
              onClick={() => setShowUpdatePrompt(false)}
            >
              <Close fontSize="small" />
            </IconButton>
          </Box>
        }
      >
        <Alert severity="info" sx={{ width: '100%' }}>
          Nova versão disponível! Clique em "Atualizar" para obter as últimas funcionalidades.
        </Alert>
      </Snackbar>

      {/* Prompt de Notificações */}
      <Dialog
        open={showNotificationPrompt}
        onClose={() => setShowNotificationPrompt(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Notifications color="primary" />
            Ativar Notificações
          </Box>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" paragraph>
            Receba notificações importantes sobre suas validações de residência e atualizações do sistema.
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, mt: 2 }}>
            <Typography variant="body2" color="text.secondary">
              ✓ Status de validação de residência
            </Typography>
            <Typography variant="body2" color="text.secondary">
              ✓ Lembretes para ativar Google Timeline
            </Typography>
            <Typography variant="body2" color="text.secondary">
              ✓ Certificados prontos para download
            </Typography>
            <Typography variant="body2" color="text.secondary">
              ✓ Atualizações importantes do sistema
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowNotificationPrompt(false)}>
            Não Agora
          </Button>
          <Button
            variant="contained"
            onClick={handleEnableNotifications}
            startIcon={<Notifications />}
          >
            Ativar
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default PWAManager;
