const crypto = require('crypto');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const pipeline = promisify(require('stream').pipeline);

class EncryptionService {
  constructor() {
    this.algorithm = 'aes-256-gcm';
    this.keyLength = 32; // 256 bits
    this.ivLength = 16;  // 128 bits
    this.tagLength = 16; // 128 bits
    this.saltLength = 32; // 256 bits
    
    // Configuração KMS (simulado para desenvolvimento)
    this.kmsEndpoint = process.env.KMS_ENDPOINT || 'local';
    this.kmsKeyId = process.env.KMS_KEY_ID || 'cartorio-master-key';
    this.masterKey = process.env.MASTER_ENCRYPTION_KEY || 'default-master-key-change-in-production';
    
    // Diretório para chaves criptografadas
    this.keysDirectory = path.join(__dirname, '../keys');
    this.ensureKeysDirectory();
  }

  /**
   * Garantir que o diretório de chaves existe
   */
  ensureKeysDirectory() {
    if (!fs.existsSync(this.keysDirectory)) {
      fs.mkdirSync(this.keysDirectory, { recursive: true, mode: 0o700 });
    }
  }

  /**
   * Gerar chave de criptografia única
   */
  generateEncryptionKey() {
    return crypto.randomBytes(this.keyLength);
  }

  /**
   * Gerar IV (Initialization Vector) único
   */
  generateIV() {
    return crypto.randomBytes(this.ivLength);
  }

  /**
   * Gerar salt único
   */
  generateSalt() {
    return crypto.randomBytes(this.saltLength);
  }

  /**
   * Derivar chave usando PBKDF2
   */
  deriveKey(password, salt, iterations = 100000) {
    return crypto.pbkdf2Sync(password, salt, iterations, this.keyLength, 'sha256');
  }

  /**
   * Criptografar dados usando AES-256-GCM
   */
  encryptData(data, key, iv) {
    const cipher = crypto.createCipher(this.algorithm, key, iv);
    
    let encrypted = cipher.update(data);
    encrypted = Buffer.concat([encrypted, cipher.final()]);
    
    const tag = cipher.getAuthTag();
    
    return {
      encrypted,
      tag,
      iv
    };
  }

  /**
   * Descriptografar dados usando AES-256-GCM
   */
  decryptData(encryptedData, key, iv, tag) {
    const decipher = crypto.createDecipher(this.algorithm, key, iv);
    decipher.setAuthTag(tag);
    
    let decrypted = decipher.update(encryptedData);
    decrypted = Buffer.concat([decrypted, decipher.final()]);
    
    return decrypted;
  }

  /**
   * Criptografar arquivo
   */
  async encryptFile(inputPath, outputPath, recordingId) {
    try {
      console.log(`[Encryption] Iniciando criptografia do arquivo: ${recordingId}`);
      
      // Gerar chave e IV únicos
      const key = this.generateEncryptionKey();
      const iv = this.generateIV();
      
      // Criar cipher
      const cipher = crypto.createCipher(this.algorithm, key, iv);
      
      // Streams para leitura e escrita
      const input = fs.createReadStream(inputPath);
      const output = fs.createWriteStream(outputPath);
      
      // Escrever IV no início do arquivo
      output.write(iv);
      
      // Pipeline de criptografia
      await pipeline(input, cipher, output);
      
      // Obter tag de autenticação
      const tag = cipher.getAuthTag();
      
      // Anexar tag ao final do arquivo
      fs.appendFileSync(outputPath, tag);
      
      // Armazenar chave no KMS
      const keyId = await this.storeKeyInKMS(recordingId, key);
      
      // Calcular hash do arquivo criptografado
      const encryptedHash = this.calculateFileHash(outputPath);
      
      console.log(`[Encryption] Arquivo criptografado com sucesso: ${recordingId}`);
      
      return {
        success: true,
        recordingId,
        inputPath,
        outputPath,
        keyId,
        iv: iv.toString('hex'),
        tag: tag.toString('hex'),
        encryptedHash,
        algorithm: this.algorithm,
        encryptedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('[Encryption] Erro ao criptografar arquivo:', error);
      throw new Error(`Erro na criptografia: ${error.message}`);
    }
  }

  /**
   * Descriptografar arquivo
   */
  async decryptFile(inputPath, outputPath, recordingId) {
    try {
      console.log(`[Encryption] Iniciando descriptografia do arquivo: ${recordingId}`);
      
      // Recuperar chave do KMS
      const key = await this.retrieveKeyFromKMS(recordingId);
      
      // Ler arquivo criptografado
      const encryptedData = fs.readFileSync(inputPath);
      
      // Extrair IV (primeiros 16 bytes)
      const iv = encryptedData.slice(0, this.ivLength);
      
      // Extrair tag (últimos 16 bytes)
      const tag = encryptedData.slice(-this.tagLength);
      
      // Extrair dados criptografados (meio)
      const encrypted = encryptedData.slice(this.ivLength, -this.tagLength);
      
      // Criar decipher
      const decipher = crypto.createDecipher(this.algorithm, key, iv);
      decipher.setAuthTag(tag);
      
      // Descriptografar
      let decrypted = decipher.update(encrypted);
      decrypted = Buffer.concat([decrypted, decipher.final()]);
      
      // Salvar arquivo descriptografado
      fs.writeFileSync(outputPath, decrypted);
      
      console.log(`[Encryption] Arquivo descriptografado com sucesso: ${recordingId}`);
      
      return {
        success: true,
        recordingId,
        inputPath,
        outputPath,
        decryptedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('[Encryption] Erro ao descriptografar arquivo:', error);
      throw new Error(`Erro na descriptografia: ${error.message}`);
    }
  }

  /**
   * Armazenar chave no KMS (simulado)
   */
  async storeKeyInKMS(recordingId, key) {
    try {
      if (this.kmsEndpoint === 'local') {
        // Implementação local para desenvolvimento
        return this.storeKeyLocally(recordingId, key);
      }
      
      // Implementação real do KMS (AWS KMS, Azure Key Vault, etc.)
      // const kmsClient = new AWS.KMS({ region: 'us-east-1' });
      // const result = await kmsClient.encrypt({
      //   KeyId: this.kmsKeyId,
      //   Plaintext: key
      // }).promise();
      // return result.CiphertextBlob.toString('base64');
      
      throw new Error('KMS real não configurado');
    } catch (error) {
      console.error('[Encryption] Erro ao armazenar chave no KMS:', error);
      throw error;
    }
  }

  /**
   * Recuperar chave do KMS (simulado)
   */
  async retrieveKeyFromKMS(recordingId) {
    try {
      if (this.kmsEndpoint === 'local') {
        // Implementação local para desenvolvimento
        return this.retrieveKeyLocally(recordingId);
      }
      
      // Implementação real do KMS
      throw new Error('KMS real não configurado');
    } catch (error) {
      console.error('[Encryption] Erro ao recuperar chave do KMS:', error);
      throw error;
    }
  }

  /**
   * Armazenar chave localmente (desenvolvimento)
   */
  storeKeyLocally(recordingId, key) {
    try {
      // Criptografar chave com master key
      const salt = this.generateSalt();
      const derivedKey = this.deriveKey(this.masterKey, salt);
      const iv = this.generateIV();
      
      const cipher = crypto.createCipher(this.algorithm, derivedKey, iv);
      let encryptedKey = cipher.update(key);
      encryptedKey = Buffer.concat([encryptedKey, cipher.final()]);
      const tag = cipher.getAuthTag();
      
      // Criar estrutura da chave
      const keyData = {
        recordingId,
        encryptedKey: encryptedKey.toString('hex'),
        salt: salt.toString('hex'),
        iv: iv.toString('hex'),
        tag: tag.toString('hex'),
        algorithm: this.algorithm,
        createdAt: new Date().toISOString()
      };
      
      // Salvar em arquivo
      const keyPath = path.join(this.keysDirectory, `${recordingId}.key`);
      fs.writeFileSync(keyPath, JSON.stringify(keyData), { mode: 0o600 });
      
      return `local:${recordingId}`;
    } catch (error) {
      console.error('[Encryption] Erro ao armazenar chave localmente:', error);
      throw error;
    }
  }

  /**
   * Recuperar chave localmente (desenvolvimento)
   */
  retrieveKeyLocally(recordingId) {
    try {
      const keyPath = path.join(this.keysDirectory, `${recordingId}.key`);
      
      if (!fs.existsSync(keyPath)) {
        throw new Error(`Chave não encontrada para gravação: ${recordingId}`);
      }
      
      // Ler dados da chave
      const keyData = JSON.parse(fs.readFileSync(keyPath, 'utf8'));
      
      // Descriptografar chave
      const salt = Buffer.from(keyData.salt, 'hex');
      const iv = Buffer.from(keyData.iv, 'hex');
      const tag = Buffer.from(keyData.tag, 'hex');
      const encryptedKey = Buffer.from(keyData.encryptedKey, 'hex');
      
      const derivedKey = this.deriveKey(this.masterKey, salt);
      
      const decipher = crypto.createDecipher(this.algorithm, derivedKey, iv);
      decipher.setAuthTag(tag);
      
      let key = decipher.update(encryptedKey);
      key = Buffer.concat([key, decipher.final()]);
      
      return key;
    } catch (error) {
      console.error('[Encryption] Erro ao recuperar chave localmente:', error);
      throw error;
    }
  }

  /**
   * Calcular hash de arquivo
   */
  calculateFileHash(filePath) {
    const fileBuffer = fs.readFileSync(filePath);
    return crypto.createHash('sha256').update(fileBuffer).digest('hex');
  }

  /**
   * Verificar integridade de arquivo criptografado
   */
  async verifyEncryptedFile(filePath, expectedHash) {
    try {
      const currentHash = this.calculateFileHash(filePath);
      return {
        valid: currentHash === expectedHash,
        currentHash,
        expectedHash,
        verifiedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('[Encryption] Erro na verificação de integridade:', error);
      return {
        valid: false,
        error: error.message,
        verifiedAt: new Date().toISOString()
      };
    }
  }

  /**
   * Rotacionar chave de criptografia
   */
  async rotateKey(recordingId, oldFilePath, newFilePath) {
    try {
      console.log(`[Encryption] Iniciando rotação de chave: ${recordingId}`);
      
      // Descriptografar com chave antiga
      const tempPath = `${oldFilePath}.temp`;
      await this.decryptFile(oldFilePath, tempPath, recordingId);
      
      // Gerar nova chave e criptografar
      const result = await this.encryptFile(tempPath, newFilePath, `${recordingId}_rotated`);
      
      // Remover arquivo temporário
      fs.unlinkSync(tempPath);
      
      // Remover chave antiga
      this.removeKeyFromKMS(recordingId);
      
      console.log(`[Encryption] Rotação de chave concluída: ${recordingId}`);
      
      return result;
    } catch (error) {
      console.error('[Encryption] Erro na rotação de chave:', error);
      throw error;
    }
  }

  /**
   * Remover chave do KMS
   */
  removeKeyFromKMS(recordingId) {
    try {
      if (this.kmsEndpoint === 'local') {
        const keyPath = path.join(this.keysDirectory, `${recordingId}.key`);
        if (fs.existsSync(keyPath)) {
          fs.unlinkSync(keyPath);
        }
      }
      // Implementar remoção do KMS real se necessário
    } catch (error) {
      console.error('[Encryption] Erro ao remover chave:', error);
    }
  }

  /**
   * Obter estatísticas de criptografia
   */
  getEncryptionStats() {
    try {
      const keyFiles = fs.readdirSync(this.keysDirectory).filter(f => f.endsWith('.key'));
      
      return {
        totalEncryptedFiles: keyFiles.length,
        algorithm: this.algorithm,
        keyLength: this.keyLength * 8, // em bits
        kmsEndpoint: this.kmsEndpoint,
        keysDirectory: this.keysDirectory,
        generatedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('[Encryption] Erro ao obter estatísticas:', error);
      return {
        error: error.message,
        generatedAt: new Date().toISOString()
      };
    }
  }
}

module.exports = EncryptionService;
