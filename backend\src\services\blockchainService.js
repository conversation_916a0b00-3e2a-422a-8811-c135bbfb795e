const { Web3 } = require('web3');
const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

class BlockchainService {
  constructor() {
    // Configuração da blockchain
    this.networkUrl = process.env.BLOCKCHAIN_NETWORK_URL || 'http://localhost:8545'; // Ganache local
    this.contractAddress = process.env.BLOCKCHAIN_CONTRACT_ADDRESS;
    this.privateKey = process.env.BLOCKCHAIN_PRIVATE_KEY;
    this.gasLimit = process.env.BLOCKCHAIN_GAS_LIMIT || 300000;
    this.gasPrice = process.env.BLOCKCHAIN_GAS_PRICE || '20000000000'; // 20 Gwei

    // Inicializar Web3 com tratamento de erro
    try {
      this.web3 = new Web3(this.networkUrl);
      console.log('✅ Web3 inicializado com sucesso');
    } catch (error) {
      console.error('❌ Erro ao inicializar Web3:', error.message);
      this.web3 = null;
    }

    // Carregar ABI do contrato
    try {
      this.contractABI = this.loadContractABI();
    } catch (error) {
      console.error('❌ Erro ao carregar ABI do contrato:', error.message);
      this.contractABI = null;
    }
    
    // Configurar conta
    if (this.privateKey) {
      this.account = this.web3.eth.accounts.privateKeyToAccount(this.privateKey);
      this.web3.eth.accounts.wallet.add(this.account);
    }
    
    // Inicializar contrato
    if (this.contractAddress && this.contractABI) {
      this.contract = new this.web3.eth.Contract(this.contractABI, this.contractAddress);
    }
  }

  /**
   * Carregar ABI do contrato inteligente
   */
  loadContractABI() {
    try {
      const abiPath = path.join(__dirname, '../contracts/IntegrityRegistry.json');
      if (fs.existsSync(abiPath)) {
        const contractData = JSON.parse(fs.readFileSync(abiPath, 'utf8'));
        return contractData.abi;
      }
      
      // ABI simplificada para desenvolvimento
      return [
        {
          "inputs": [
            {"name": "recordingId", "type": "string"},
            {"name": "fileHash", "type": "string"},
            {"name": "metadata", "type": "string"}
          ],
          "name": "storeHash",
          "outputs": [{"name": "", "type": "bool"}],
          "type": "function"
        },
        {
          "inputs": [{"name": "recordingId", "type": "string"}],
          "name": "getRecord",
          "outputs": [
            {"name": "fileHash", "type": "string"},
            {"name": "timestamp", "type": "uint256"},
            {"name": "blockNumber", "type": "uint256"},
            {"name": "metadata", "type": "string"}
          ],
          "type": "function"
        },
        {
          "inputs": [
            {"name": "recordingId", "type": "string"},
            {"name": "fileHash", "type": "string"},
            {"name": "timestamp", "type": "uint256"},
            {"name": "blockNumber", "type": "uint256"},
            {"name": "metadata", "type": "string"}
          ],
          "name": "HashStored",
          "type": "event"
        }
      ];
    } catch (error) {
      console.error('[Blockchain] Erro ao carregar ABI:', error);
      return null;
    }
  }

  /**
   * Verificar se a blockchain está disponível
   */
  async isAvailable() {
    try {
      // Verificar se Web3 foi inicializado
      if (!this.web3) {
        return {
          connected: false,
          contract: false,
          account: false,
          available: false,
          error: 'Web3 não inicializado'
        };
      }

      const isConnected = await this.web3.eth.net.isListening();
      const hasContract = !!this.contract;
      const hasAccount = !!this.account;

      return {
        connected: isConnected,
        contract: hasContract,
        account: hasAccount,
        available: isConnected && hasContract && hasAccount
      };
    } catch (error) {
      console.error('[Blockchain] Erro ao verificar disponibilidade:', error);
      return {
        connected: false,
        contract: false,
        account: false,
        available: false,
        error: error.message
      };
    }
  }

  /**
   * Registrar hash de gravação na blockchain
   */
  async registerHash(recordingId, fileHash, metadata = {}) {
    try {
      const availability = await this.isAvailable();
      if (!availability.available) {
        throw new Error(`Blockchain não disponível: ${JSON.stringify(availability)}`);
      }

      // Preparar metadados
      const metadataString = JSON.stringify({
        ...metadata,
        registeredAt: new Date().toISOString(),
        version: '1.0'
      });

      console.log(`[Blockchain] Registrando hash para gravação ${recordingId}`);

      // Estimar gas
      const gasEstimate = await this.contract.methods
        .storeHash(recordingId, fileHash, metadataString)
        .estimateGas({ from: this.account.address });

      // Executar transação
      const tx = await this.contract.methods
        .storeHash(recordingId, fileHash, metadataString)
        .send({
          from: this.account.address,
          gas: Math.min(gasEstimate * 2, this.gasLimit), // 2x estimativa com limite
          gasPrice: this.gasPrice
        });

      console.log(`[Blockchain] Hash registrado com sucesso. TX: ${tx.transactionHash}`);

      return {
        success: true,
        transactionHash: tx.transactionHash,
        blockNumber: tx.blockNumber,
        gasUsed: tx.gasUsed,
        recordingId,
        fileHash,
        metadata: metadataString,
        registeredAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('[Blockchain] Erro ao registrar hash:', error);
      throw new Error(`Erro ao registrar na blockchain: ${error.message}`);
    }
  }

  /**
   * Recuperar registro da blockchain
   */
  async getRecord(recordingId) {
    try {
      const availability = await this.isAvailable();
      if (!availability.available) {
        throw new Error('Blockchain não disponível');
      }

      console.log(`[Blockchain] Recuperando registro para gravação ${recordingId}`);

      const result = await this.contract.methods
        .getRecord(recordingId)
        .call();

      if (!result.fileHash || result.fileHash === '') {
        return {
          found: false,
          recordingId,
          message: 'Registro não encontrado na blockchain'
        };
      }

      return {
        found: true,
        recordingId,
        fileHash: result.fileHash,
        timestamp: new Date(parseInt(result.timestamp) * 1000).toISOString(),
        blockNumber: parseInt(result.blockNumber),
        metadata: result.metadata ? JSON.parse(result.metadata) : {},
        retrievedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('[Blockchain] Erro ao recuperar registro:', error);
      throw new Error(`Erro ao recuperar da blockchain: ${error.message}`);
    }
  }

  /**
   * Verificar integridade usando blockchain
   */
  async verifyIntegrity(recordingId, currentFileHash) {
    try {
      const blockchainRecord = await this.getRecord(recordingId);
      
      if (!blockchainRecord.found) {
        return {
          verified: false,
          reason: 'RECORD_NOT_FOUND',
          message: 'Registro não encontrado na blockchain',
          recordingId,
          currentHash: currentFileHash
        };
      }

      const hashMatch = blockchainRecord.fileHash === currentFileHash;

      return {
        verified: hashMatch,
        reason: hashMatch ? 'HASH_MATCH' : 'HASH_MISMATCH',
        message: hashMatch ? 
          'Hash verificado com sucesso na blockchain' : 
          'Hash não confere com o registro da blockchain',
        recordingId,
        currentHash: currentFileHash,
        blockchainHash: blockchainRecord.fileHash,
        blockchainRecord,
        verifiedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('[Blockchain] Erro na verificação de integridade:', error);
      return {
        verified: false,
        reason: 'VERIFICATION_ERROR',
        message: `Erro na verificação: ${error.message}`,
        recordingId,
        currentHash: currentFileHash,
        error: error.message
      };
    }
  }

  /**
   * Obter histórico de transações para uma gravação
   */
  async getTransactionHistory(recordingId) {
    try {
      const availability = await this.isAvailable();
      if (!availability.available) {
        throw new Error('Blockchain não disponível');
      }

      // Buscar eventos HashStored para esta gravação
      const events = await this.contract.getPastEvents('HashStored', {
        filter: { recordingId },
        fromBlock: 0,
        toBlock: 'latest'
      });

      const history = await Promise.all(events.map(async (event) => {
        const block = await this.web3.eth.getBlock(event.blockNumber);
        const transaction = await this.web3.eth.getTransaction(event.transactionHash);
        
        return {
          transactionHash: event.transactionHash,
          blockNumber: event.blockNumber,
          blockHash: event.blockHash,
          timestamp: new Date(block.timestamp * 1000).toISOString(),
          fileHash: event.returnValues.fileHash,
          metadata: event.returnValues.metadata ? 
            JSON.parse(event.returnValues.metadata) : {},
          gasUsed: transaction.gas,
          from: transaction.from
        };
      }));

      return {
        recordingId,
        transactionCount: history.length,
        transactions: history.sort((a, b) => 
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
        ),
        retrievedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('[Blockchain] Erro ao obter histórico:', error);
      throw new Error(`Erro ao obter histórico: ${error.message}`);
    }
  }

  /**
   * Obter estatísticas da blockchain
   */
  async getBlockchainStats() {
    try {
      const availability = await this.isAvailable();
      
      if (!availability.available) {
        return {
          available: false,
          error: 'Blockchain não disponível',
          ...availability
        };
      }

      const [
        blockNumber,
        gasPrice,
        balance,
        networkId
      ] = await Promise.all([
        this.web3.eth.getBlockNumber(),
        this.web3.eth.getGasPrice(),
        this.web3.eth.getBalance(this.account.address),
        this.web3.eth.net.getId()
      ]);

      // Contar total de registros (eventos HashStored)
      const totalEvents = await this.contract.getPastEvents('HashStored', {
        fromBlock: 0,
        toBlock: 'latest'
      });

      return {
        available: true,
        network: {
          id: networkId,
          url: this.networkUrl,
          currentBlock: blockNumber,
          gasPrice: this.web3.utils.fromWei(gasPrice, 'gwei') + ' Gwei'
        },
        contract: {
          address: this.contractAddress,
          totalRecords: totalEvents.length
        },
        account: {
          address: this.account.address,
          balance: this.web3.utils.fromWei(balance, 'ether') + ' ETH'
        },
        retrievedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('[Blockchain] Erro ao obter estatísticas:', error);
      return {
        available: false,
        error: error.message,
        retrievedAt: new Date().toISOString()
      };
    }
  }

  /**
   * Registrar múltiplos hashes em lote
   */
  async batchRegisterHashes(records) {
    const results = {
      total: records.length,
      success: 0,
      failed: 0,
      details: []
    };

    for (const record of records) {
      try {
        const result = await this.registerHash(
          record.recordingId,
          record.fileHash,
          record.metadata
        );
        
        results.success++;
        results.details.push({
          recordingId: record.recordingId,
          status: 'SUCCESS',
          transactionHash: result.transactionHash,
          blockNumber: result.blockNumber
        });
      } catch (error) {
        results.failed++;
        results.details.push({
          recordingId: record.recordingId,
          status: 'FAILED',
          error: error.message
        });
      }
    }

    return results;
  }
}

module.exports = BlockchainService;
