'use client';

import { useState, useEffect } from 'react';
import { Box, keyframes } from '@mui/material';

// Animação de bounce personalizada
const bounceAnimation = keyframes`
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -10px, 0);
  }
  70% {
    transform: translate3d(0, -5px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
`;

// Animação de pulse personalizada
const pulseAnimation = keyframes`
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
`;

// Animação de spin personalizada
const spinAnimation = keyframes`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`;

// Componente de ícone animado para sucesso
export const AnimatedSuccess = ({ className = "w-6 h-6", color = "success.main" }: { className?: string; color?: string }) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  return (
    <Box
      component="svg"
      className={className}
      sx={{
        color: color,
        transform: isVisible ? 'scale(1)' : 'scale(0)',
        opacity: isVisible ? 1 : 0,
        transition: 'all 0.7s ease-in-out',
        animation: `${pulseAnimation} 2s infinite`,
      }}
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
      />
    </Box>
  );
};

// Componente de loading com animação
export const AnimatedLoading = ({ className = "w-6 h-6", color = "primary.main" }: { className?: string; color?: string }) => {
  return (
    <Box
      className={className}
      sx={{
        borderRadius: '50%',
        border: '2px solid',
        borderColor: 'grey.300',
        borderTopColor: color,
        animation: `${spinAnimation} 1s linear infinite`,
      }}
    />
  );
};

// Componente de ícone de segurança com animação
export const AnimatedSecurity = ({ className = "w-6 h-6", color = "success.main" }: { className?: string; color?: string }) => {
  return (
    <Box
      component="svg"
      className={className}
      sx={{
        color: color,
        animation: `${pulseAnimation} 2s infinite`,
      }}
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
      />
    </Box>
  );
};

// Componente de ícone de validação com animação
export const AnimatedValidation = ({ className = "w-6 h-6", color = "primary.main" }: { className?: string; color?: string }) => {
  return (
    <Box
      component="svg"
      className={className}
      sx={{
        color: color,
        animation: `${bounceAnimation} 2s infinite`,
      }}
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
      />
    </Box>
  );
};

// Componente de contador animado
export const AnimatedCounter = ({
  target,
  suffix = "",
  duration = 2000,
  className = "text-2xl font-bold"
}: {
  target: number;
  suffix?: string;
  duration?: number;
  className?: string;
}) => {
  const [count, setCount] = useState(0);
  
  useEffect(() => {
    let startTime: number;

    const updateCount = (timestamp: number) => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / duration, 1);
      
      setCount(Math.floor(progress * target));
      
      if (progress < 1) {
        requestAnimationFrame(updateCount);
      }
    };

    requestAnimationFrame(updateCount);
  }, [target, duration]);

  return (
    <Box
      component="span"
      className={className}
      sx={{
        fontWeight: 'bold',
        fontSize: '1.5rem',
      }}
    >
      {count.toLocaleString()}{suffix}
    </Box>
  );
};

// Componente de botão animado
export const AnimatedButton = ({
  children,
  onClick,
  variant = "contained",
  disabled = false,
  className = "",
  ...props
}: {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: "contained" | "outlined" | "text";
  disabled?: boolean;
  className?: string;
  [key: string]: any;
}) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <Box
      component="button"
      onClick={onClick}
      disabled={disabled}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      className={className}
      sx={{
        px: 3,
        py: 1.5,
        borderRadius: 2,
        fontSize: '1rem',
        fontWeight: 'bold',
        cursor: disabled ? 'not-allowed' : 'pointer',
        transition: 'all 0.3s ease',
        transform: isHovered && !disabled ? 'translateY(-2px)' : 'translateY(0)',
        boxShadow: isHovered && !disabled ? '0 4px 12px rgba(0,0,0,0.15)' : '0 2px 4px rgba(0,0,0,0.1)',
        backgroundColor: variant === 'contained' ? 'primary.main' : 'transparent',
        color: variant === 'contained' ? 'white' : 'primary.main',
        border: variant === 'outlined' ? '2px solid' : 'none',
        borderColor: variant === 'outlined' ? 'primary.main' : 'transparent',
        opacity: disabled ? 0.6 : 1,
        '&:hover': {
          backgroundColor: variant === 'contained' ? 'primary.dark' : 'primary.light',
          color: variant === 'contained' ? 'white' : 'primary.dark',
        },
      }}
      {...props}
    >
      {children}
    </Box>
  );
};

// Componente de card animado
export const AnimatedCard = ({
  children,
  className = "",
  delay = 0,
  ...props
}: {
  children: React.ReactNode;
  className?: string;
  delay?: number;
  [key: string]: any;
}) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, delay);

    return () => clearTimeout(timer);
  }, [delay]);

  return (
    <Box
      className={className}
      sx={{
        transform: isVisible ? 'translateY(0)' : 'translateY(20px)',
        opacity: isVisible ? 1 : 0,
        transition: 'all 0.6s ease-out',
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: '0 8px 25px rgba(0,0,0,0.12)',
        },
      }}
      {...props}
    >
      {children}
    </Box>
  );
};

// Componente de seção com fade in
export const FadeInSection = ({
  children,
  className = "",
  delay = 0,
  ...props
}: {
  children: React.ReactNode;
  className?: string;
  delay?: number;
  [key: string]: any;
}) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, delay);

    return () => clearTimeout(timer);
  }, [delay]);

  return (
    <Box
      className={className}
      sx={{
        opacity: isVisible ? 1 : 0,
        transform: isVisible ? 'translateX(0)' : 'translateX(-30px)',
        transition: 'all 0.8s ease-out',
      }}
      {...props}
    >
      {children}
    </Box>
  );
};

export default {
  AnimatedSuccess,
  AnimatedLoading,
  AnimatedSecurity,
  AnimatedValidation,
  AnimatedCounter,
  AnimatedButton,
  AnimatedCard,
  FadeInSection,
};
