# 🐳 Docker para Produção - Sistema de Validação de Residência

Este guia explica como usar Docker para deploy em produção do Sistema de Validação de Residência.

## 📋 Pré-requisitos

### Sistema
- Docker 20.10+
- Docker Compose 2.0+
- Linux/Ubuntu (recomendado) ou Windows Server
- Mínimo 2GB RAM, 10GB disco
- Domínio configurado (para HTTPS)

### Credenciais Google
- Projeto no Google Cloud Platform
- Timeline API ativada
- Credenciais OAuth2 configuradas

## 🚀 Deploy Rápido

### 1. Preparar Ambiente
```bash
# Clonar repositório
git clone seu-repositorio
cd sistema-residencia-app

# Copiar arquivo de ambiente
cp .env.production.example .env.production

# Editar variáveis de produção
nano .env.production
```

### 2. Configurar Variáveis
Edite `.env.production` com seus valores reais:

```env
# Google OAuth2
GOOGLE_CLIENT_ID=seu_client_id_real
GOOGLE_CLIENT_SECRET=seu_client_secret_real

# URLs
NEXT_PUBLIC_BASE_URL=https://seu-dominio.com
NEXT_PUBLIC_GOOGLE_REDIRECT_URI=https://seu-dominio.com/api/auth/callback

# Senhas seguras
JWT_SECRET=sua_chave_jwt_complexa_e_unica
ENCRYPTION_KEY=chave_de_exatos_32_caracteres!!
POSTGRES_PASSWORD=senha_postgres_super_segura
REDIS_PASSWORD=senha_redis_super_segura
```

### 3. Executar Deploy
```bash
# Tornar script executável
chmod +x deploy.sh

# Executar deploy
./deploy.sh
```

## 🔧 Configuração Manual

### 1. Build da Aplicação
```bash
# Build da imagem Docker
docker-compose -f docker-compose.prod.yml build

# Subir todos os serviços
docker-compose -f docker-compose.prod.yml up -d
```

### 2. Verificar Status
```bash
# Status dos containers
docker-compose -f docker-compose.prod.yml ps

# Logs da aplicação
docker-compose -f docker-compose.prod.yml logs -f app
```

## 🌐 Configuração de Domínio

### 1. DNS
Configure seu domínio para apontar para o servidor:
```
A    seu-dominio.com    -> IP_DO_SERVIDOR
A    www.seu-dominio.com -> IP_DO_SERVIDOR
```

### 2. SSL/HTTPS com Let's Encrypt
```bash
# Instalar Certbot
sudo apt install certbot

# Obter certificados
sudo certbot certonly --standalone -d seu-dominio.com -d www.seu-dominio.com

# Copiar certificados para projeto
sudo cp /etc/letsencrypt/live/seu-dominio.com/fullchain.pem ./ssl/
sudo cp /etc/letsencrypt/live/seu-dominio.com/privkey.pem ./ssl/
sudo chown $USER:$USER ./ssl/*

# Restart nginx
docker-compose -f docker-compose.prod.yml restart nginx
```

### 3. Atualizar Nginx
Edite `nginx.conf` e substitua `seu-dominio.com` pelo seu domínio real.

## 📊 Monitoramento

### Logs
```bash
# Logs de todos os serviços
docker-compose -f docker-compose.prod.yml logs -f

# Logs específicos
docker-compose -f docker-compose.prod.yml logs -f app
docker-compose -f docker-compose.prod.yml logs -f postgres
docker-compose -f docker-compose.prod.yml logs -f nginx
```

### Health Checks
```bash
# Verificar saúde da aplicação
curl http://localhost:3000/health

# Verificar banco
docker-compose -f docker-compose.prod.yml exec postgres pg_isready

# Verificar Redis
docker-compose -f docker-compose.prod.yml exec redis redis-cli ping
```

### Métricas
```bash
# Uso de recursos
docker stats

# Espaço em disco
docker system df
```

## 💾 Backup e Restore

### Backup Automático
O sistema inclui backup automático diário:
```bash
# Executar backup manual
docker-compose -f docker-compose.prod.yml exec backup /backup-script.sh

# Ver backups
ls -la backups/
```

### Backup Manual
```bash
# Backup do banco
docker-compose -f docker-compose.prod.yml exec postgres pg_dump -U residencia_user residencia_db > backup.sql

# Backup de volumes
docker run --rm -v sistema-residencia-app_postgres_data:/data -v $(pwd):/backup alpine tar czf /backup/postgres_data.tar.gz /data
```

### Restore
```bash
# Restore do banco
cat backup.sql | docker-compose -f docker-compose.prod.yml exec -T postgres psql -U residencia_user -d residencia_db
```

## 🔒 Segurança

### Firewall
```bash
# Ubuntu/Debian
sudo ufw allow 80
sudo ufw allow 443
sudo ufw allow 22
sudo ufw --force enable
```

### Updates
```bash
# Atualizar sistema
sudo apt update && sudo apt upgrade

# Atualizar Docker images
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d
```

### Hardening
- Remover porta 5432 do docker-compose em produção
- Usar secrets do Docker para senhas
- Configurar fail2ban
- Monitorar logs com ELK stack

## 🚨 Troubleshooting

### Aplicação não inicia
```bash
# Verificar logs
docker-compose -f docker-compose.prod.yml logs app

# Verificar variáveis de ambiente
docker-compose -f docker-compose.prod.yml exec app env | grep -E "GOOGLE|JWT"

# Restart forçado
docker-compose -f docker-compose.prod.yml down
docker-compose -f docker-compose.prod.yml up -d
```

### Banco não conecta
```bash
# Verificar status do PostgreSQL
docker-compose -f docker-compose.prod.yml exec postgres pg_isready

# Verificar logs do banco
docker-compose -f docker-compose.prod.yml logs postgres

# Conectar manualmente
docker-compose -f docker-compose.prod.yml exec postgres psql -U residencia_user -d residencia_db
```

### Erro de SSL
```bash
# Verificar certificados
ls -la ssl/

# Testar configuração nginx
docker-compose -f docker-compose.prod.yml exec nginx nginx -t

# Restart nginx
docker-compose -f docker-compose.prod.yml restart nginx
```

## 📈 Escalabilidade

### Load Balancer
Para múltiplas instâncias, use:
```yaml
# No docker-compose.prod.yml
app:
  deploy:
    replicas: 3
```

### Banco Externo
Para usar PostgreSQL externo (AWS RDS, etc.):
```env
DATABASE_URL=***************************************/db
```

Remova o serviço `postgres` do docker-compose.

### CDN
Configure CDN (CloudFlare, AWS CloudFront) para assets estáticos.

## 📞 Suporte

Para problemas específicos:
1. Verificar logs: `docker-compose -f docker-compose.prod.yml logs`
2. Health checks: `curl http://localhost:3000/health`
3. Verificar recursos: `docker stats`
4. Documentação: README.md principal

---

**Deploy seguro e monitorado! 🚀**
