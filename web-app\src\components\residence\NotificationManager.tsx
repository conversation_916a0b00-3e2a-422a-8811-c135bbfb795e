import React, { useEffect, useState } from 'react';
import {
  Snack<PERSON>,
  Alert,
  Button,
  Box,
  Typography,
  IconButton
} from '@mui/material';
import {
  Close,
  Notifications,
  NotificationsOff,
  Schedule
} from '@mui/icons-material';
import { useNotifications } from '../../hooks/useNotifications';

interface NotificationManagerProps {
  userId?: string;
  onTimelineReminderSent?: () => void;
}

const NotificationManager: React.FC<NotificationManagerProps> = ({
  userId,
  onTimelineReminderSent
}) => {
  const {
    isSupported,
    permission,
    isSubscribed,
    requestPermission,
    subscribe,
    sendTimelineReminder
  } = useNotifications();

  const [showPermissionPrompt, setShowPermissionPrompt] = useState(false);
  const [showSubscribePrompt, setShowSubscribePrompt] = useState(false);
  const [reminderScheduled, setReminderScheduled] = useState(false);

  useEffect(() => {
    // Verificar se deve mostrar prompts
    if (isSupported && permission === 'default') {
      // Mostrar prompt de permissão após 10 segundos
      const timer = setTimeout(() => {
        setShowPermissionPrompt(true);
      }, 10000);

      return () => clearTimeout(timer);
    }

    if (isSupported && permission === 'granted' && !isSubscribed) {
      // Mostrar prompt de subscription após 5 segundos
      const timer = setTimeout(() => {
        setShowSubscribePrompt(true);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [isSupported, permission, isSubscribed]);

  useEffect(() => {
    // Agendar lembretes automáticos se subscrito
    if (isSubscribed && !reminderScheduled) {
      scheduleTimelineReminders();
      setReminderScheduled(true);
    }
  }, [isSubscribed, reminderScheduled]);

  const scheduleTimelineReminders = () => {
    // Lembrete após 1 hora se Timeline não estiver ativado
    setTimeout(() => {
      checkAndSendTimelineReminder('1_hour');
    }, 60 * 60 * 1000); // 1 hora

    // Lembrete após 24 horas
    setTimeout(() => {
      checkAndSendTimelineReminder('24_hours');
    }, 24 * 60 * 60 * 1000); // 24 horas

    // Lembrete após 7 dias
    setTimeout(() => {
      checkAndSendTimelineReminder('7_days');
    }, 7 * 24 * 60 * 60 * 1000); // 7 dias
  };

  const checkAndSendTimelineReminder = async (interval: string) => {
    try {
      // Verificar se o usuário já ativou o Timeline
      // (em produção, fazer uma verificação real via API)
      const hasTimelineActive = localStorage.getItem('timeline_active') === 'true';
      
      if (!hasTimelineActive) {
        const success = await sendTimelineReminder(userId);
        if (success) {
          console.log(`[NotificationManager] Timeline reminder sent (${interval})`);
          onTimelineReminderSent?.();
        }
      }
    } catch (error) {
      console.error('[NotificationManager] Error sending timeline reminder:', error);
    }
  };

  const handleRequestPermission = async () => {
    const granted = await requestPermission();
    setShowPermissionPrompt(false);
    
    if (granted) {
      setShowSubscribePrompt(true);
    }
  };

  const handleSubscribe = async () => {
    const success = await subscribe();
    setShowSubscribePrompt(false);
    
    if (success) {
      console.log('[NotificationManager] Successfully subscribed to notifications');
    }
  };

  const handleMarkTimelineActive = () => {
    localStorage.setItem('timeline_active', 'true');
    localStorage.setItem('timeline_activated_at', new Date().toISOString());
  };

  // Não renderizar nada se notificações não são suportadas
  if (!isSupported) {
    return null;
  }

  return (
    <>
      {/* Prompt de Permissão */}
      <Snackbar
        open={showPermissionPrompt}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        autoHideDuration={null}
      >
        <Alert
          severity="info"
          action={
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                color="inherit"
                size="small"
                onClick={handleRequestPermission}
                startIcon={<Notifications />}
              >
                Ativar
              </Button>
              <IconButton
                size="small"
                color="inherit"
                onClick={() => setShowPermissionPrompt(false)}
              >
                <Close fontSize="small" />
              </IconButton>
            </Box>
          }
        >
          <Box>
            <Typography variant="body2" sx={{ fontWeight: 500 }}>
              Ativar Notificações
            </Typography>
            <Typography variant="caption">
              Receba lembretes sobre Timeline e status de validação
            </Typography>
          </Box>
        </Alert>
      </Snackbar>

      {/* Prompt de Subscription */}
      <Snackbar
        open={showSubscribePrompt}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        autoHideDuration={null}
      >
        <Alert
          severity="success"
          action={
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                color="inherit"
                size="small"
                onClick={handleSubscribe}
                startIcon={<Notifications />}
              >
                Confirmar
              </Button>
              <IconButton
                size="small"
                color="inherit"
                onClick={() => setShowSubscribePrompt(false)}
              >
                <Close fontSize="small" />
              </IconButton>
            </Box>
          }
        >
          <Box>
            <Typography variant="body2" sx={{ fontWeight: 500 }}>
              Finalizar Configuração
            </Typography>
            <Typography variant="caption">
              Confirme para receber notificações push
            </Typography>
          </Box>
        </Alert>
      </Snackbar>

      {/* Status das Notificações (para debug) */}
      {process.env.NODE_ENV === 'development' && (
        <Box
          sx={{
            position: 'fixed',
            bottom: 16,
            right: 16,
            bgcolor: 'background.paper',
            p: 2,
            borderRadius: 2,
            boxShadow: 2,
            minWidth: 200,
            fontSize: '0.75rem'
          }}
        >
          <Typography variant="caption" sx={{ fontWeight: 'bold', display: 'block' }}>
            Notificações Debug
          </Typography>
          <Typography variant="caption" color="text.secondary">
            Suportado: {isSupported ? '✅' : '❌'}
          </Typography>
          <br />
          <Typography variant="caption" color="text.secondary">
            Permissão: {permission}
          </Typography>
          <br />
          <Typography variant="caption" color="text.secondary">
            Subscrito: {isSubscribed ? '✅' : '❌'}
          </Typography>
          <br />
          <Typography variant="caption" color="text.secondary">
            Lembretes: {reminderScheduled ? '✅' : '❌'}
          </Typography>
          
          <Box sx={{ mt: 1, display: 'flex', gap: 0.5 }}>
            <Button
              size="small"
              variant="outlined"
              onClick={() => sendTimelineReminder(userId)}
              startIcon={<Schedule />}
              sx={{ fontSize: '0.7rem', py: 0.5 }}
            >
              Testar Lembrete
            </Button>
          </Box>
          
          <Box sx={{ mt: 0.5 }}>
            <Button
              size="small"
              variant="text"
              onClick={handleMarkTimelineActive}
              sx={{ fontSize: '0.7rem', py: 0.5 }}
            >
              Marcar Timeline Ativo
            </Button>
          </Box>
        </Box>
      )}
    </>
  );
};

export default NotificationManager;
