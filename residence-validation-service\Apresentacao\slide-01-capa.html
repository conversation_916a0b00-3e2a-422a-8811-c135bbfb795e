<!DOCTYPE html>
<html lang="pt-br">
<head>
  <meta charset="UTF-8">
  <title>ProvaSegura - Slide 1: Capa</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');
    
    body {
      margin: 0;
      padding: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      font-family: 'Inter', 'Segoe UI', Arial, sans-serif;
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .slide {
      width: 1280px;
      height: 720px;
      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
      box-shadow: 0 20px 60px rgba(0,0,0,0.15), 0 8px 32px rgba(0,0,0,0.08);
      border-radius: 24px;
      display: flex;
      flex-direction: row;
      overflow: hidden;
      position: relative;
      border: 1px solid rgba(255,255,255,0.2);
    }
    
    .slide::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
      border-radius: 24px 24px 0 0;
    }
    
    .slide-content {
      padding: 80px 100px;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      position: relative;
      z-index: 2;
    }
    
    .slide-title {
      font-size: 3.5rem;
      font-weight: 800;
      background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-bottom: 32px;
      line-height: 1.1;
      letter-spacing: -0.02em;
    }
    
    .slide-subtitle {
      font-size: 1.8rem;
      color: #475569;
      margin-bottom: 40px;
      font-weight: 500;
      line-height: 1.4;
    }
    
    .slide-img {
      flex: 0 0 400px;
      background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      overflow: hidden;
    }
    
    .slide-img::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 30% 70%, rgba(102, 126, 234, 0.1) 0%, transparent 50%);
    }
    
    .slide-img svg {
      filter: drop-shadow(0 15px 35px rgba(102, 126, 234, 0.3));
      z-index: 1;
      position: relative;
    }
    
    .slide-footer {
      position: absolute;
      bottom: 40px;
      left: 100px;
      font-size: 1.2rem;
      color: #64748b;
      font-weight: 500;
      padding: 12px 24px;
      background: rgba(255,255,255,0.8);
      border-radius: 12px;
      backdrop-filter: blur(10px);
    }
    
    .slide-number {
      position: absolute;
      top: 30px;
      right: 40px;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-weight: 700;
      font-size: 1.3rem;
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }

    .logo-elements {
      position: absolute;
      top: 50px;
      left: 50px;
      display: flex;
      gap: 20px;
      opacity: 0.1;
    }

    .floating-element {
      position: absolute;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      opacity: 0.05;
      animation: float 6s ease-in-out infinite;
    }

    .floating-element:nth-child(1) {
      width: 60px;
      height: 60px;
      top: 10%;
      left: 10%;
      animation-delay: 0s;
    }

    .floating-element:nth-child(2) {
      width: 40px;
      height: 40px;
      top: 70%;
      left: 15%;
      animation-delay: 2s;
    }

    .floating-element:nth-child(3) {
      width: 80px;
      height: 80px;
      top: 40%;
      left: 5%;
      animation-delay: 4s;
    }

    @keyframes float {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-20px); }
    }

    @media print {
      body { background: #fff; }
      .slide { box-shadow: none; border: 1px solid #e2e8f0; }
    }
  </style>
</head>
<body>

<div class="slide">
  <div class="floating-element"></div>
  <div class="floating-element"></div>
  <div class="floating-element"></div>
  
  <div class="slide-number">1</div>
  <div class="slide-content">
    <div class="slide-title">ProvaSegura: A Solução Definitiva para Comprovação de Residência e Localização</div>
    <div class="slide-subtitle">Transformando processos judiciais, financeiros e de RH com tecnologia confiável</div>
    <div class="slide-footer">Apresentação para investidores - 22 de junho de 2025</div>
  </div>
  <div class="slide-img">
    <!-- Smartphone + mapa + biometria melhorado -->
    <svg width="280" height="400" viewBox="0 0 280 400" fill="none">
      <!-- Smartphone principal -->
      <rect x="40" y="40" width="200" height="320" rx="40" fill="#fff" stroke="#667eea" stroke-width="8"/>
      <rect x="60" y="80" width="160" height="240" rx="20" fill="#f1f5f9"/>
      
      <!-- Tela com mapa -->
      <rect x="70" y="100" width="140" height="120" rx="16" fill="#e0e7ff"/>
      
      <!-- Pin de localização central -->
      <circle cx="140" cy="140" r="25" fill="#667eea"/>
      <path d="M140 120 L155 145 L140 170 L125 145 Z" fill="#fff"/>
      <circle cx="140" cy="145" r="6" fill="#667eea"/>
      
      <!-- Pontos de GPS ao redor -->
      <circle cx="110" cy="120" r="4" fill="#10b981"/>
      <circle cx="170" cy="130" r="4" fill="#10b981"/>
      <circle cx="160" cy="180" r="4" fill="#10b981"/>
      <circle cx="120" cy="190" r="4" fill="#10b981"/>
      
      <!-- Linhas de conexão GPS -->
      <line x1="110" y1="120" x2="140" y2="145" stroke="#94a3b8" stroke-width="2" opacity="0.5"/>
      <line x1="170" y1="130" x2="140" y2="145" stroke="#94a3b8" stroke-width="2" opacity="0.5"/>
      <line x1="160" y1="180" x2="140" y2="145" stroke="#94a3b8" stroke-width="2" opacity="0.5"/>
      <line x1="120" y1="190" x2="140" y2="145" stroke="#94a3b8" stroke-width="2" opacity="0.5"/>
      
      <!-- Interface do app -->
      <rect x="80" y="240" width="120" height="60" rx="12" fill="#f8fafc"/>
      <rect x="90" y="250" width="100" height="8" rx="4" fill="#cbd5e1"/>
      <rect x="90" y="265" width="80" height="6" rx="3" fill="#e2e8f0"/>
      <rect x="90" y="275" width="60" height="6" rx="3" fill="#e2e8f0"/>
      
      <!-- Botão de biometria -->
      <circle cx="140" cy="320" r="28" fill="#fff" stroke="#667eea" stroke-width="6"/>
      <circle cx="140" cy="320" r="18" fill="none" stroke="#667eea" stroke-width="3" stroke-dasharray="5,5"/>
      <circle cx="140" cy="320" r="8" fill="#667eea"/>
      
      <!-- Elementos de segurança -->
      <rect x="20" y="20" width="30" height="30" rx="8" fill="#10b981" opacity="0.8"/>
      <text x="35" y="40" text-anchor="middle" font-size="20" fill="#fff">🔒</text>
      
      <rect x="260" y="360" width="30" height="30" rx="8" fill="#f59e0b" opacity="0.8"/>
      <text x="275" y="380" text-anchor="middle" font-size="20" fill="#fff">✓</text>
      
      <!-- Ondas de sinal -->
      <path d="M60 60 Q70 50 80 60" stroke="#667eea" stroke-width="3" fill="none" opacity="0.6"/>
      <path d="M50 70 Q70 40 90 70" stroke="#667eea" stroke-width="2" fill="none" opacity="0.4"/>
      <path d="M40 80 Q70 30 100 80" stroke="#667eea" stroke-width="1" fill="none" opacity="0.3"/>    </svg>
  </div>
</div>

<div class="navigation">
  <a href="index.html" class="nav-button">📋 Índice</a>
  <a href="slide-02-problema.html" class="nav-button">Próximo →</a>
</div>

<style>
.navigation {
  position: fixed;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 15px;
  z-index: 1000;
}

.nav-button {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  color: #1e293b;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  text-decoration: none;
  display: inline-block;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.nav-button:hover {
  background: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}
</style>

<script>
document.addEventListener('keydown', function(e) {
  switch(e.key) {
    case 'ArrowRight':
      window.location.href = 'slide-02-problema.html';
      break;
    case 'Home':
      window.location.href = 'index.html';
      break;
    case 'F11':
      e.preventDefault();
      if (document.fullscreenElement) {
        document.exitFullscreen();
      } else {
        document.documentElement.requestFullscreen();
      }
      break;
  }
});
</script>

</body>
</html>
