// Utilitários para validação de residência
const haversine = require('haversine');

class ResidenceValidationUtils {
  
  // Calcular distância usando fórmula de Haversine
  static calculateDistance(lat1, lon1, lat2, lon2) {
    const start = { latitude: lat1, longitude: lon1 };
    const end = { latitude: lat2, longitude: lon2 };
    
    return haversine(start, end, { unit: 'meter' });
  }

  // Verificar se um ponto está dentro do período noturno
  static isNightTime(timestamp) {
    const date = new Date(timestamp);
    const hour = date.getHours();
    
    // Período noturno: 18h às 6h do dia seguinte
    return hour >= 18 || hour <= 6;
  }

  // Verificar se um ponto está dentro do raio de casa
  static isWithinHomeRadius(pointLat, pointLon, homeLat, homeLon, radiusMeters = 100) {
    const distance = this.calculateDistance(pointLat, pointLon, homeLat, homeLon);
    return distance <= radiusMeters;
  }

  // Agrupar localizações por dia
  static groupLocationsByDay(locations) {
    const grouped = {};
    
    locations.forEach(location => {
      const date = new Date(location.timestamp).toISOString().split('T')[0];
      if (!grouped[date]) {
        grouped[date] = [];
      }
      grouped[date].push(location);
    });
    
    return grouped;
  }

  // Validar residência baseado nos dados coletados
  static validateResidence(locations, homeLat, homeLon, config = {}) {
    const CONFIG = {
      MINIMUM_DAYS: config.minimumDays || 15,
      MAX_DISTANCE_FROM_HOME: config.maxDistance || 100,
      CONFIDENCE_THRESHOLD: config.confidenceThreshold || 0.7,
      NIGHT_START_HOUR: config.nightStartHour || 18,
      NIGHT_END_HOUR: config.nightEndHour || 6,
      ...config
    };

    // 1. Agrupar dados por dia
    const locationsByDay = this.groupLocationsByDay(locations);
    
    // 2. Analisar cada dia
    const nightlyRecords = [];
    
    for (const [date, dayLocations] of Object.entries(locationsByDay)) {
      // 2.1. Filtrar período noturno
      const nightlyPoints = dayLocations.filter(location => 
        this.isNightTime(location.timestamp)
      );
      
      if (nightlyPoints.length === 0) {
        nightlyRecords.push({
          date,
          isPresent: false,
          confidence: 0,
          totalPoints: 0,
          homePoints: 0,
          reason: 'Sem dados noturnos'
        });
        continue;
      }
      
      // 2.2. Contar pontos dentro do raio de casa
      const homePoints = nightlyPoints.filter(location =>
        this.isWithinHomeRadius(
          location.latitude, 
          location.longitude, 
          homeLat, 
          homeLon, 
          CONFIG.MAX_DISTANCE_FROM_HOME
        )
      );
      
      // 2.3. Calcular presença e confiabilidade
      const presenceRatio = homePoints.length / nightlyPoints.length;
      const isPresent = presenceRatio >= CONFIG.CONFIDENCE_THRESHOLD;
      
      nightlyRecords.push({
        date,
        isPresent,
        confidence: presenceRatio,
        totalPoints: nightlyPoints.length,
        homePoints: homePoints.length,
        reason: isPresent ? 'Presente' : 'Ausente ou dados insuficientes'
      });
    }
    
    // 3. Contar dias consecutivos válidos
    let consecutiveDays = 0;
    let maxConsecutiveDays = 0;
    
    for (const record of nightlyRecords) {
      if (record.isPresent) {
        consecutiveDays++;
        maxConsecutiveDays = Math.max(maxConsecutiveDays, consecutiveDays);
      } else {
        consecutiveDays = 0;
      }
    }
    
    // 4. Determinar aprovação
    const isValid = maxConsecutiveDays >= CONFIG.MINIMUM_DAYS;
    const score = nightlyRecords.filter(r => r.isPresent).length / nightlyRecords.length;
    
    // 5. Calcular estatísticas
    const totalDays = nightlyRecords.length;
    const validDays = nightlyRecords.filter(r => r.isPresent).length;
    const averageConfidence = nightlyRecords.reduce((sum, r) => sum + r.confidence, 0) / totalDays;
    
    return {
      isValid,
      score: Math.round(score * 100) / 100,
      maxConsecutiveDays,
      totalDays,
      validDays,
      averageConfidence: Math.round(averageConfidence * 100) / 100,
      nightlyRecords,
      config: CONFIG,
      summary: {
        status: isValid ? 'APROVADO' : 'REPROVADO',
        reason: isValid 
          ? `Residência validada com ${maxConsecutiveDays} dias consecutivos` 
          : `Insuficiente: apenas ${maxConsecutiveDays} dias consecutivos (mínimo: ${CONFIG.MINIMUM_DAYS})`,
        scorePercentage: Math.round(score * 100),
        recommendation: isValid 
          ? 'Certificado preliminar pode ser gerado'
          : 'Colete mais dados ou use período diferente'
      }
    };
  }

  // Gerar dados de demonstração para testes
  static generateDemoData(homeLat, homeLon, days = 30) {
    const locations = [];
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    for (let i = 0; i < days; i++) {
      const currentDate = new Date(startDate);
      currentDate.setDate(startDate.getDate() + i);
      
      // Gerar pontos noturnos (18h às 6h)
      for (let hour = 18; hour <= 23; hour++) {
        const timestamp = new Date(currentDate);
        timestamp.setHours(hour, Math.floor(Math.random() * 60), 0, 0);
        
        // 90% das vezes em casa, 10% fora
        const isAtHome = Math.random() > 0.1;
        
        if (isAtHome) {
          // Variar dentro do raio de casa (0-50m)
          const randomRadius = Math.random() * 50;
          const randomAngle = Math.random() * 2 * Math.PI;
          
          const lat = homeLat + (randomRadius * Math.cos(randomAngle)) / 111320;
          const lon = homeLon + (randomRadius * Math.sin(randomAngle)) / (111320 * Math.cos(homeLat));
          
          locations.push({
            timestamp: timestamp.toISOString(),
            latitude: lat,
            longitude: lon,
            accuracy: Math.floor(Math.random() * 20) + 5
          });
        } else {
          // Localização aleatória fora do raio
          const randomRadius = Math.random() * 5000 + 200; // 200m a 5km
          const randomAngle = Math.random() * 2 * Math.PI;
          
          const lat = homeLat + (randomRadius * Math.cos(randomAngle)) / 111320;
          const lon = homeLon + (randomRadius * Math.sin(randomAngle)) / (111320 * Math.cos(homeLat));
          
          locations.push({
            timestamp: timestamp.toISOString(),
            latitude: lat,
            longitude: lon,
            accuracy: Math.floor(Math.random() * 50) + 10
          });
        }
      }
      
      // Gerar alguns pontos da madrugada (0h às 6h)
      for (let hour = 0; hour <= 6; hour++) {
        if (Math.random() > 0.7) continue; // Nem todos os horários
        
        const timestamp = new Date(currentDate);
        timestamp.setHours(hour, Math.floor(Math.random() * 60), 0, 0);
        
        // 95% das vezes em casa durante a madrugada
        const isAtHome = Math.random() > 0.05;
        
        if (isAtHome) {
          const randomRadius = Math.random() * 30;
          const randomAngle = Math.random() * 2 * Math.PI;
          
          const lat = homeLat + (randomRadius * Math.cos(randomAngle)) / 111320;
          const lon = homeLon + (randomRadius * Math.sin(randomAngle)) / (111320 * Math.cos(homeLat));
          
          locations.push({
            timestamp: timestamp.toISOString(),
            latitude: lat,
            longitude: lon,
            accuracy: Math.floor(Math.random() * 15) + 5
          });
        }
      }
    }
    
    return locations.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
  }
}

module.exports = ResidenceValidationUtils;
