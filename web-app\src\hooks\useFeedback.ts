import { useState, useCallback, useRef, useEffect } from 'react';
import { FeedbackState } from '../components/common/FeedbackSystem';

interface UseFeedbackOptions {
  defaultDuration?: number;
  maxConcurrent?: number;
  position?: 'top' | 'bottom' | 'center';
  variant?: 'snackbar' | 'card' | 'overlay' | 'inline';
}

interface FeedbackItem extends FeedbackState {
  id: string;
  timestamp: number;
}

export const useFeedback = (options: UseFeedbackOptions = {}) => {
  const {
    defaultDuration = 6000,
    maxConcurrent = 3,
    position = 'top',
    variant = 'snackbar'
  } = options;

  const [feedbacks, setFeedbacks] = useState<FeedbackItem[]>([]);
  const [currentFeedback, setCurrentFeedback] = useState<FeedbackItem | null>(null);
  const timeoutRefs = useRef<Map<string, NodeJS.Timeout>>(new Map());
  const idCounter = useRef(0);

  // Limpar timeouts quando o componente for desmontado
  useEffect(() => {
    return () => {
      timeoutRefs.current.forEach(timeout => clearTimeout(timeout));
      timeoutRefs.current.clear();
    };
  }, []);

  const generateId = useCallback(() => {
    return `feedback-${++idCounter.current}-${Date.now()}`;
  }, []);

  const addFeedback = useCallback((feedback: Omit<FeedbackState, 'id'>) => {
    const id = generateId();
    const newFeedback: FeedbackItem = {
      ...feedback,
      id,
      timestamp: Date.now(),
      duration: feedback.duration ?? defaultDuration
    };

    setFeedbacks(prev => {
      const updated = [...prev, newFeedback];
      // Limitar número de feedbacks simultâneos
      if (updated.length > maxConcurrent) {
        const removed = updated.shift();
        if (removed) {
          const timeout = timeoutRefs.current.get(removed.id);
          if (timeout) {
            clearTimeout(timeout);
            timeoutRefs.current.delete(removed.id);
          }
        }
      }
      return updated;
    });

    // Definir como feedback atual se for o primeiro
    setCurrentFeedback(prev => prev || newFeedback);

    // Auto-remover após duração especificada (se não for persistente)
    if (!feedback.persistent && feedback.type !== 'loading') {
      const timeout = setTimeout(() => {
        removeFeedback(id);
      }, newFeedback.duration);
      timeoutRefs.current.set(id, timeout);
    }

    return id;
  }, [defaultDuration, maxConcurrent, generateId]);

  const removeFeedback = useCallback((id: string) => {
    // Limpar timeout se existir
    const timeout = timeoutRefs.current.get(id);
    if (timeout) {
      clearTimeout(timeout);
      timeoutRefs.current.delete(id);
    }

    setFeedbacks(prev => {
      const updated = prev.filter(f => f.id !== id);
      return updated;
    });

    setCurrentFeedback(prev => {
      if (prev?.id === id) {
        // Se removeu o feedback atual, definir o próximo
        setFeedbacks(current => {
          const next = current.find(f => f.id !== id);
          return current.filter(f => f.id !== id);
        });
        return null;
      }
      return prev;
    });
  }, []);

  const updateFeedback = useCallback((id: string, updates: Partial<FeedbackState>) => {
    setFeedbacks(prev => prev.map(f => 
      f.id === id ? { ...f, ...updates } : f
    ));

    setCurrentFeedback(prev => 
      prev?.id === id ? { ...prev, ...updates } : prev
    );
  }, []);

  const clearAll = useCallback(() => {
    // Limpar todos os timeouts
    timeoutRefs.current.forEach(timeout => clearTimeout(timeout));
    timeoutRefs.current.clear();
    
    setFeedbacks([]);
    setCurrentFeedback(null);
  }, []);

  // Métodos de conveniência
  const showSuccess = useCallback((message: string, options?: Partial<FeedbackState>) => {
    return addFeedback({
      type: 'success',
      message,
      ...options
    });
  }, [addFeedback]);

  const showError = useCallback((message: string, options?: Partial<FeedbackState>) => {
    return addFeedback({
      type: 'error',
      message,
      persistent: true, // Erros são persistentes por padrão
      ...options
    });
  }, [addFeedback]);

  const showWarning = useCallback((message: string, options?: Partial<FeedbackState>) => {
    return addFeedback({
      type: 'warning',
      message,
      ...options
    });
  }, [addFeedback]);

  const showInfo = useCallback((message: string, options?: Partial<FeedbackState>) => {
    return addFeedback({
      type: 'info',
      message,
      ...options
    });
  }, [addFeedback]);

  const showLoading = useCallback((message: string, options?: Partial<FeedbackState>) => {
    return addFeedback({
      type: 'loading',
      message,
      persistent: true,
      ...options
    });
  }, [addFeedback]);

  const showProgress = useCallback((message: string, progress: number, options?: Partial<FeedbackState>) => {
    return addFeedback({
      type: 'progress',
      message,
      progress,
      persistent: true,
      ...options
    });
  }, [addFeedback]);

  // Métodos para operações comuns
  const withLoading = useCallback(async <T>(
    operation: () => Promise<T>,
    loadingMessage: string = 'Carregando...',
    successMessage?: string,
    errorMessage?: string
  ): Promise<T> => {
    const loadingId = showLoading(loadingMessage);
    
    try {
      const result = await operation();
      removeFeedback(loadingId);
      
      if (successMessage) {
        showSuccess(successMessage);
      }
      
      return result;
    } catch (error) {
      removeFeedback(loadingId);
      
      const message = errorMessage || 
        (error instanceof Error ? error.message : 'Erro desconhecido');
      showError(message);
      
      throw error;
    }
  }, [showLoading, removeFeedback, showSuccess, showError]);

  const withProgress = useCallback(async <T>(
    operation: (updateProgress: (progress: number, message?: string) => void) => Promise<T>,
    initialMessage: string = 'Processando...',
    successMessage?: string,
    errorMessage?: string
  ): Promise<T> => {
    let progressId: string | null = null;
    
    const updateProgress = (progress: number, message?: string) => {
      if (progressId) {
        updateFeedback(progressId, {
          progress,
          message: message || initialMessage
        });
      } else {
        progressId = showProgress(message || initialMessage, progress);
      }
    };
    
    try {
      const result = await operation(updateProgress);
      
      if (progressId) {
        removeFeedback(progressId);
      }
      
      if (successMessage) {
        showSuccess(successMessage);
      }
      
      return result;
    } catch (error) {
      if (progressId) {
        removeFeedback(progressId);
      }
      
      const message = errorMessage || 
        (error instanceof Error ? error.message : 'Erro desconhecido');
      showError(message);
      
      throw error;
    }
  }, [showProgress, updateFeedback, removeFeedback, showSuccess, showError]);

  // Avançar para o próximo feedback
  const nextFeedback = useCallback(() => {
    setFeedbacks(prev => {
      if (prev.length > 0) {
        const [current, ...rest] = prev;
        setCurrentFeedback(rest[0] || null);
        return rest;
      }
      return prev;
    });
  }, []);

  return {
    // Estado
    feedbacks,
    currentFeedback,
    hasFeedbacks: feedbacks.length > 0,
    
    // Métodos principais
    addFeedback,
    removeFeedback,
    updateFeedback,
    clearAll,
    nextFeedback,
    
    // Métodos de conveniência
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showLoading,
    showProgress,
    
    // Métodos para operações
    withLoading,
    withProgress,
    
    // Configurações
    position,
    variant
  };
};

export default useFeedback;
