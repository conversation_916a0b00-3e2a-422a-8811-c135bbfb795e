<!DOCTYPE html>
<html lang="pt-br">
<head>
  <meta charset="UTF-8">
  <title>ProvaSegura - Slide 8: Tração</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');
    
    body {
      margin: 0;
      padding: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      font-family: 'Inter', 'Segoe UI', Arial, sans-serif;
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }
      .slide {
      width: 90vw;
      max-width: 1280px;
      height: 80vh;
      max-height: 720px;
      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
      box-shadow: 0 20px 60px rgba(0,0,0,0.15), 0 8px 32px rgba(0,0,0,0.08);
      border-radius: 24px;
      display: flex;
      flex-direction: row;
      overflow: hidden;
      position: relative;
      border: 1px solid rgba(255,255,255,0.2);
    }
    
    .slide::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
      border-radius: 24px 24px 0 0;
    }
      .slide-content {
      padding: 40px 60px;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      position: relative;
      z-index: 2;
    }
      .slide-title {
      font-size: 2.5rem;
      font-weight: 800;
      background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-bottom: 30px;
      line-height: 1.1;
      letter-spacing: -0.02em;
    }
    
    .slide-text {
      font-size: 1.2rem;
      color: #334155;
      line-height: 1.7;
      font-weight: 400;
    }
    
    .slide-text ul {
      padding-left: 0;
      list-style: none;
    }
      .slide-text li {
      position: relative;
      padding: 18px 18px 18px 55px;
      margin-bottom: 16px;
      background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
      border-left: 4px solid #0ea5e9;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(14, 165, 233, 0.1);
    }
      .slide-text li::before {
      position: absolute;
      left: 18px;
      top: 18px;
      font-size: 1.5rem;
    }
    
    .slide-text li:nth-child(1)::before { content: '🚀'; }
    .slide-text li:nth-child(2)::before { content: '✅'; }
    .slide-text li:nth-child(3)::before { content: '📊'; }
    .slide-text li:nth-child(4)::before { content: '☁️'; }
    
    .slide-text strong {
      color: #0c4a6e;
      font-weight: 700;
    }
    
    .slide-img {
      flex: 0 0 400px;
      background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      overflow: hidden;
      order: -1;
    }
    
    .slide-img::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 70% 30%, rgba(102, 126, 234, 0.1) 0%, transparent 50%);
    }
    
    .slide-img svg {
      filter: drop-shadow(0 15px 35px rgba(102, 126, 234, 0.2));
      z-index: 1;
      position: relative;
    }
    
    .slide-number {
      position: absolute;
      top: 30px;
      right: 40px;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-weight: 700;
      font-size: 1.3rem;
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }    @media print {
      body { background: #fff; }
      .slide { box-shadow: none; border: 1px solid #e2e8f0; }
    }

    @media (max-width: 768px) {
      .slide {
        width: 95vw;
        height: 90vh;
        flex-direction: column;
        padding: 30px 20px;
      }
      
      .slide-content {
        padding: 20px;
        order: 2;
      }
      
      .slide-title {
        font-size: 2rem;
        margin-bottom: 20px;
      }
      
      .slide-text {
        font-size: 1rem;
      }
      
      .slide-text li {
        padding: 15px 15px 15px 50px;
        margin-bottom: 12px;
      }
      
      .slide-text li::before {
        font-size: 1.3rem;
        left: 15px;
        top: 15px;
      }
      
      .slide-img {
        flex: 0 0 200px;
        order: 1;
      }
      
      .slide-img svg {
        width: 280px;
        height: 200px;
      }
    }

    .navigation {
      position: fixed;
      bottom: 30px;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      gap: 15px;
      z-index: 1000;
    }

    .nav-button {
      background: rgba(255, 255, 255, 0.9);
      border: none;
      padding: 12px 24px;
      border-radius: 25px;
      color: #1e293b;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      text-decoration: none;
      display: inline-block;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .nav-button:hover {
      background: white;
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }
  </style>
</head>
<body>

<div class="slide">
  <div class="slide-number">8</div>
  <div class="slide-content">
    <div class="slide-title">Estamos Prontos para Crescer</div>
    <div class="slide-text">
      <ul>
        <li><strong>Protótipo funcional:</strong> App já integra biometria e Google Timeline, testado em cenários reais (ex.: comprovar residência, álibi).</li>
        <li><strong>Mercado validado:</strong> Interesse de escritórios de advocacia e empresas de RH em testes iniciais.</li>
        <li><strong>Plano piloto:</strong> Parcerias com 5 empresas de RH e 2 bancos para validar 1.000 relatórios em 2025.</li>
        <li><strong>Escalabilidade:</strong> Infraestrutura em nuvem pronta para milhares de validações diárias.</li>
      </ul>
    </div>
  </div>
  <div class="slide-img">
    <!-- Crescimento e equipe melhorado -->
    <svg width="350" height="300" viewBox="0 0 350 300">
      <!-- Gráfico de crescimento -->
      <g transform="translate(50, 80)">
        <!-- Eixos -->
        <line x1="0" y1="150" x2="200" y2="150" stroke="#94a3b8" stroke-width="2"/>
        <line x1="0" y1="150" x2="0" y2="20" stroke="#94a3b8" stroke-width="2"/>
        
        <!-- Barras de crescimento -->
        <rect x="20" y="120" width="30" height="30" rx="8" fill="#c7d2fe"/>
        <rect x="65" y="100" width="30" height="50" rx="8" fill="#a5b4fc"/>
        <rect x="110" y="70" width="30" height="80" rx="8" fill="#8b5cf6"/>
        <rect x="155" y="40" width="30" height="110" rx="8" fill="#7c3aed"/>
        
        <!-- Labels -->
        <text x="35" y="170" text-anchor="middle" font-size="10" fill="#64748b">Q1</text>
        <text x="80" y="170" text-anchor="middle" font-size="10" fill="#64748b">Q2</text>
        <text x="125" y="170" text-anchor="middle" font-size="10" fill="#64748b">Q3</text>
        <text x="170" y="170" text-anchor="middle" font-size="10" fill="#64748b">Q4</text>
        
        <text x="100" y="190" text-anchor="middle" font-size="12" font-weight="bold" fill="#1e293b">Crescimento 2025</text>
      </g>
      
      <!-- Equipe -->
      <g transform="translate(280, 60)">
        <circle cx="0" cy="20" r="22" fill="#e0e7ff"/>
        <text x="0" y="28" text-anchor="middle" font-size="24" fill="#667eea">👩‍💻</text>
        <text x="0" y="50" text-anchor="middle" font-size="10" font-weight="bold" fill="#1e293b">Tech Lead</text>
        
        <circle cx="0" cy="100" r="22" fill="#e0e7ff"/>
        <text x="0" y="108" text-anchor="middle" font-size="24" fill="#667eea">👨‍💼</text>
        <text x="0" y="130" text-anchor="middle" font-size="10" font-weight="bold" fill="#1e293b">CEO</text>
        
        <circle cx="0" cy="180" r="22" fill="#e0e7ff"/>
        <text x="0" y="188" text-anchor="middle" font-size="24" fill="#667eea">👨‍⚖️</text>
        <text x="0" y="210" text-anchor="middle" font-size="10" font-weight="bold" fill="#1e293b">Legal</text>
      </g>
      
      <!-- Métricas chave -->
      <g transform="translate(30, 20)">
        <rect x="0" y="0" width="180" height="40" rx="12" fill="#f0f9ff" stroke="#0ea5e9" stroke-width="2"/>
        <text x="90" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#0c4a6e">MVP Testado</text>
        <text x="90" y="32" text-anchor="middle" font-size="10" fill="#0369a1">100+ validações realizadas</text>
      </g>
      
      <!-- Parcerias -->
      <g transform="translate(50, 240)">
        <circle cx="0" cy="0" r="15" fill="#3b82f6"/>
        <text x="0" y="5" text-anchor="middle" font-size="16" fill="#fff">🏦</text>
        
        <circle cx="40" cy="0" r="15" fill="#10b981"/>
        <text x="40" y="5" text-anchor="middle" font-size="16" fill="#fff">🏢</text>
        
        <circle cx="80" cy="0" r="15" fill="#f59e0b"/>
        <text x="80" y="5" text-anchor="middle" font-size="16" fill="#fff">⚖️</text>
        
        <circle cx="120" cy="0" r="15" fill="#8b5cf6"/>
        <text x="120" y="5" text-anchor="middle" font-size="16" fill="#fff">🤝</text>
        
        <text x="60" y="25" text-anchor="middle" font-size="12" font-weight="bold" fill="#1e293b">Parcerias Piloto</text>
      </g>
      
      <!-- Seta de crescimento -->
      <path d="M250 200 Q270 180 290 160 Q310 140 330 120" 
            stroke="#10b981" stroke-width="4" fill="none" marker-end="url(#arrowhead2)"/>
      
      <defs>
        <marker id="arrowhead2" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
          <polygon points="0 0, 10 3.5, 0 7" fill="#10b981" />
        </marker>
      </defs>
    </svg>  </div>
</div>

<div class="navigation">
  <a href="slide-07-modelo.html" class="nav-button">← Anterior</a>
  <a href="index.html" class="nav-button">Índice</a>
  <a href="slide-09-investimento.html" class="nav-button">Próximo →</a>
</div>

<script>
document.addEventListener('keydown', function(event) {
  if (event.key === 'ArrowLeft') {
    window.location.href = 'slide-07-modelo.html';
  } else if (event.key === 'ArrowRight') {
    window.location.href = 'slide-09-investimento.html';
  } else if (event.key === 'Escape') {
    window.location.href = 'index.html';
  }
});
</script>

</body>
</html>
