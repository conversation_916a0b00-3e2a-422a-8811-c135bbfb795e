# ==============================================================================
# STAGE 1: Dependencies - Instala todas as dependências
# ==============================================================================
FROM node:18-alpine AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Copia os arquivos de dependências
COPY package.json package-lock.json ./
# Instala dependências com cache otimizado
RUN npm ci --only=production --ignore-scripts

# ==============================================================================
# STAGE 2: Builder - Builda a aplicação
# ==============================================================================
FROM node:18-alpine AS builder
WORKDIR /app

# Copia dependências do stage anterior
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Variáveis de ambiente necessárias para o build
ENV NEXT_TELEMETRY_DISABLED 1
ENV NODE_ENV production

# Executa o build
RUN npm run build

# ==============================================================================
# STAGE 3: Runner - Imagem final de produção
# ==============================================================================
FROM node:18-alpine AS runner
WORKDIR /app

# Adiciona usuário não-root para segurança
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copia arquivos estáticos
COPY --from=builder /app/public ./public

# Cria diretório .next com permissões corretas
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Copia build da aplicação
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# Variáveis de ambiente de produção
ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1
ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

# Usuário não-root
USER nextjs

# Porta da aplicação
EXPOSE 3000

# Comando para iniciar a aplicação
CMD ["node", "server.js"]
