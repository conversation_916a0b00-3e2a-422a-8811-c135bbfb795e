const express = require('express');
const EmbeddingService = require('../services/embeddingService');
const router = express.Router();

/**
 * @swagger
 * /api/search/semantic:
 *   post:
 *     summary: Busca semântica unificada
 *     tags: [Search]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               query:
 *                 type: string
 *                 description: Texto da consulta
 *               userId:
 *                 type: string
 *                 description: ID do usuário (opcional, para filtrar resultados)
 *               similarityThreshold:
 *                 type: number
 *                 minimum: 0
 *                 maximum: 1
 *                 default: 0.7
 *                 description: Limiar de similaridade
 *               maxResults:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 50
 *                 default: 10
 *                 description: Número máximo de resultados
 *               contentTypes:
 *                 type: array
 *                 items:
 *                   type: string
 *                   enum: [residence_certificate, recording, ocr_document, timeline_location]
 *                 description: Tipos de conteúdo para buscar
 *     responses:
 *       200:
 *         description: Resultados da busca semântica
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 results:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       content_type:
 *                         type: string
 *                       content_id:
 *                         type: string
 *                       title:
 *                         type: string
 *                       description:
 *                         type: string
 *                       similarity_score:
 *                         type: number
 *                       metadata:
 *                         type: object
 *                       created_at:
 *                         type: string
 *                         format: date-time
 *                 query:
 *                   type: string
 *                 total_results:
 *                   type: integer
 *       400:
 *         description: Parâmetros inválidos
 *       500:
 *         description: Erro interno do servidor
 */
router.post('/semantic', async (req, res) => {
  try {
    const {
      query,
      userId,
      similarityThreshold = 0.7,
      maxResults = 10,
      contentTypes = ['residence_certificate', 'recording', 'ocr_document', 'timeline_location']
    } = req.body;

    // Validar parâmetros
    if (!query || typeof query !== 'string' || query.trim().length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Query é obrigatória e deve ser uma string não vazia'
      });
    }

    if (similarityThreshold < 0 || similarityThreshold > 1) {
      return res.status(400).json({
        success: false,
        error: 'similarityThreshold deve estar entre 0 e 1'
      });
    }

    if (maxResults < 1 || maxResults > 50) {
      return res.status(400).json({
        success: false,
        error: 'maxResults deve estar entre 1 e 50'
      });
    }

    // Executar busca semântica
    const results = await EmbeddingService.searchSemantic(query, {
      userId,
      similarityThreshold,
      maxResults,
      contentTypes
    });

    res.json({
      success: true,
      results,
      query: query.trim(),
      total_results: results.length,
      filters: {
        userId,
        similarityThreshold,
        maxResults,
        contentTypes
      }
    });

  } catch (error) {
    console.error('[Search] Erro na busca semântica:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/search/similar:
 *   post:
 *     summary: Buscar conteúdo similar a um item específico
 *     tags: [Search]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               contentType:
 *                 type: string
 *                 enum: [residence_certificate, recording, ocr_document, timeline_location]
 *               contentId:
 *                 type: string
 *               maxResults:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 20
 *                 default: 5
 *     responses:
 *       200:
 *         description: Conteúdo similar encontrado
 *       400:
 *         description: Parâmetros inválidos
 *       404:
 *         description: Conteúdo não encontrado
 *       500:
 *         description: Erro interno do servidor
 */
router.post('/similar', async (req, res) => {
  try {
    const { contentType, contentId, maxResults = 5 } = req.body;

    // Validar parâmetros
    const validContentTypes = ['residence_certificate', 'recording', 'ocr_document', 'timeline_location'];
    if (!contentType || !validContentTypes.includes(contentType)) {
      return res.status(400).json({
        success: false,
        error: `contentType deve ser um dos: ${validContentTypes.join(', ')}`
      });
    }

    if (!contentId || typeof contentId !== 'string') {
      return res.status(400).json({
        success: false,
        error: 'contentId é obrigatório e deve ser uma string'
      });
    }

    if (maxResults < 1 || maxResults > 20) {
      return res.status(400).json({
        success: false,
        error: 'maxResults deve estar entre 1 e 20'
      });
    }

    // Buscar conteúdo similar
    const results = await EmbeddingService.findSimilarContent(contentType, contentId, maxResults);

    res.json({
      success: true,
      results,
      reference: {
        contentType,
        contentId
      },
      total_results: results.length
    });

  } catch (error) {
    console.error('[Search] Erro ao buscar conteúdo similar:', error);
    
    if (error.message.includes('não encontrado')) {
      return res.status(404).json({
        success: false,
        error: error.message
      });
    }

    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/search/stats:
 *   get:
 *     summary: Estatísticas dos embeddings
 *     tags: [Search]
 *     responses:
 *       200:
 *         description: Estatísticas dos embeddings
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 stats:
 *                   type: object
 *                   properties:
 *                     residence_certificates:
 *                       type: integer
 *                     recordings:
 *                       type: integer
 *                     ocr_documents:
 *                       type: integer
 *                     timeline_locations:
 *                       type: integer
 *                     total:
 *                       type: integer
 *       500:
 *         description: Erro interno do servidor
 */
router.get('/stats', async (req, res) => {
  try {
    const stats = await EmbeddingService.getEmbeddingStats();

    res.json({
      success: true,
      stats,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[Search] Erro ao obter estatísticas:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/search/index:
 *   post:
 *     summary: Indexar conteúdo para busca semântica
 *     tags: [Search]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               contentType:
 *                 type: string
 *                 enum: [residence_certificate, recording, ocr_document, timeline_location]
 *               data:
 *                 type: object
 *                 description: Dados específicos do tipo de conteúdo
 *     responses:
 *       200:
 *         description: Conteúdo indexado com sucesso
 *       400:
 *         description: Parâmetros inválidos
 *       500:
 *         description: Erro interno do servidor
 */
router.post('/index', async (req, res) => {
  try {
    const { contentType, data } = req.body;

    // Validar parâmetros
    const validContentTypes = ['residence_certificate', 'recording', 'ocr_document', 'timeline_location'];
    if (!contentType || !validContentTypes.includes(contentType)) {
      return res.status(400).json({
        success: false,
        error: `contentType deve ser um dos: ${validContentTypes.join(', ')}`
      });
    }

    if (!data || typeof data !== 'object') {
      return res.status(400).json({
        success: false,
        error: 'data é obrigatório e deve ser um objeto'
      });
    }

    let embeddingId;

    // Indexar baseado no tipo de conteúdo
    switch (contentType) {
      case 'residence_certificate':
        embeddingId = await EmbeddingService.saveResidenceCertificateEmbedding(data);
        break;
      case 'recording':
        embeddingId = await EmbeddingService.saveRecordingEmbedding(data);
        break;
      case 'ocr_document':
        embeddingId = await EmbeddingService.saveOCRDocumentEmbedding(data);
        break;
      case 'timeline_location':
        embeddingId = await EmbeddingService.saveTimelineLocationEmbedding(data);
        break;
    }

    res.json({
      success: true,
      message: 'Conteúdo indexado com sucesso',
      embeddingId,
      contentType,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[Search] Erro ao indexar conteúdo:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
