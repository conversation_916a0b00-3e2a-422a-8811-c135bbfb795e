const express = require('express');
const webpush = require('web-push');
const router = express.Router();

// Configurar VAPID keys para Web Push
webpush.setVapidDetails(
  'mailto:<EMAIL>',
  process.env.VAPID_PUBLIC_KEY || 'BEl62iUYgUivxIkv69yViEuiBIa40HcCWLEaQC7-jCuLKRBjfHUYiSW2UNzSdHoTxKv4t2Vd6tIFvGD8MIbSA1s',
  process.env.VAPID_PRIVATE_KEY || 'tUkzMpKWaRtchFLXcxJlNP4DiTAKwVdHkKjwgmhb3N0'
);

// Armazenar subscriptions (em produção, usar banco de dados)
const subscriptions = new Map();

/**
 * @swagger
 * /api/notifications/subscribe:
 *   post:
 *     summary: Subscrever para notificações push
 *     tags: [Notifications]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               endpoint:
 *                 type: string
 *               keys:
 *                 type: object
 *                 properties:
 *                   p256dh:
 *                     type: string
 *                   auth:
 *                     type: string
 *     responses:
 *       200:
 *         description: Subscription criada com sucesso
 *       400:
 *         description: Dados inválidos
 */
router.post('/subscribe', (req, res) => {
  try {
    const subscription = req.body;
    
    // Validar subscription
    if (!subscription || !subscription.endpoint || !subscription.keys) {
      return res.status(400).json({
        success: false,
        error: 'Subscription inválida'
      });
    }

    // Gerar ID único para a subscription
    const subscriptionId = `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Armazenar subscription (em produção, salvar no banco)
    subscriptions.set(subscriptionId, {
      ...subscription,
      userId: req.user?.id || 'anonymous',
      createdAt: new Date().toISOString()
    });

    console.log(`[Notifications] New subscription: ${subscriptionId}`);

    res.json({
      success: true,
      subscriptionId,
      message: 'Notificações ativadas com sucesso!'
    });

  } catch (error) {
    console.error('[Notifications] Error subscribing:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor'
    });
  }
});

/**
 * @swagger
 * /api/notifications/unsubscribe:
 *   post:
 *     summary: Cancelar notificações push
 *     tags: [Notifications]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               subscriptionId:
 *                 type: string
 *     responses:
 *       200:
 *         description: Subscription removida com sucesso
 */
router.post('/unsubscribe', (req, res) => {
  try {
    const { subscriptionId } = req.body;
    
    if (subscriptions.has(subscriptionId)) {
      subscriptions.delete(subscriptionId);
      console.log(`[Notifications] Subscription removed: ${subscriptionId}`);
    }

    res.json({
      success: true,
      message: 'Notificações desativadas'
    });

  } catch (error) {
    console.error('[Notifications] Error unsubscribing:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor'
    });
  }
});

/**
 * @swagger
 * /api/notifications/send:
 *   post:
 *     summary: Enviar notificação push
 *     tags: [Notifications]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *               body:
 *                 type: string
 *               data:
 *                 type: object
 *               userId:
 *                 type: string
 *     responses:
 *       200:
 *         description: Notificação enviada com sucesso
 */
router.post('/send', async (req, res) => {
  try {
    const { title, body, data, userId } = req.body;

    if (!title || !body) {
      return res.status(400).json({
        success: false,
        error: 'Título e corpo são obrigatórios'
      });
    }

    const payload = JSON.stringify({
      title,
      body,
      data: {
        ...data,
        timestamp: Date.now(),
        url: data?.url || '/residence'
      },
      icon: '/logo192.png',
      badge: '/logo192.png',
      actions: [
        {
          action: 'view',
          title: 'Ver Detalhes'
        },
        {
          action: 'dismiss',
          title: 'Dispensar'
        }
      ]
    });

    let sentCount = 0;
    let errorCount = 0;

    // Enviar para todas as subscriptions (ou filtrar por userId se especificado)
    for (const [subscriptionId, subscription] of subscriptions.entries()) {
      if (userId && subscription.userId !== userId) {
        continue;
      }

      try {
        await webpush.sendNotification(subscription, payload);
        sentCount++;
        console.log(`[Notifications] Sent to ${subscriptionId}`);
      } catch (error) {
        errorCount++;
        console.error(`[Notifications] Failed to send to ${subscriptionId}:`, error);
        
        // Remover subscriptions inválidas
        if (error.statusCode === 410) {
          subscriptions.delete(subscriptionId);
          console.log(`[Notifications] Removed invalid subscription: ${subscriptionId}`);
        }
      }
    }

    res.json({
      success: true,
      sentCount,
      errorCount,
      message: `Notificação enviada para ${sentCount} dispositivos`
    });

  } catch (error) {
    console.error('[Notifications] Error sending notification:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor'
    });
  }
});

/**
 * @swagger
 * /api/notifications/timeline-reminder:
 *   post:
 *     summary: Enviar lembrete para ativar Timeline
 *     tags: [Notifications]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               userId:
 *                 type: string
 *     responses:
 *       200:
 *         description: Lembrete enviado com sucesso
 */
router.post('/timeline-reminder', async (req, res) => {
  try {
    const { userId } = req.body;

    const payload = {
      title: '📍 Ative seu Google Maps Timeline',
      body: 'Para validar sua residência, você precisa ativar o histórico de localização no Google Maps. Toque aqui para ver o tutorial.',
      data: {
        type: 'timeline_reminder',
        action: 'open_guide',
        url: '/residence?guide=true'
      }
    };

    // Reutilizar a função de envio
    req.body = payload;
    await router.post('/send')(req, res);

  } catch (error) {
    console.error('[Notifications] Error sending timeline reminder:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor'
    });
  }
});

/**
 * @swagger
 * /api/notifications/validation-status:
 *   post:
 *     summary: Notificar status de validação
 *     tags: [Notifications]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               userId:
 *                 type: string
 *               status:
 *                 type: string
 *                 enum: [processing, completed, failed]
 *               validationId:
 *                 type: string
 *     responses:
 *       200:
 *         description: Notificação de status enviada
 */
router.post('/validation-status', async (req, res) => {
  try {
    const { userId, status, validationId } = req.body;

    let title, body, url;

    switch (status) {
      case 'processing':
        title = '⏳ Validação em Andamento';
        body = 'Estamos analisando seus dados de localização. Você será notificado quando concluir.';
        url = `/residence/status/${validationId}`;
        break;
      
      case 'completed':
        title = '✅ Validação Concluída!';
        body = 'Sua validação de residência foi concluída com sucesso. Certificado disponível para download.';
        url = `/residence/certificate/${validationId}`;
        break;
      
      case 'failed':
        title = '❌ Validação Não Aprovada';
        body = 'Não foi possível validar sua residência com os dados disponíveis. Veja os detalhes e tente novamente.';
        url = `/residence/retry/${validationId}`;
        break;
      
      default:
        return res.status(400).json({
          success: false,
          error: 'Status inválido'
        });
    }

    const payload = {
      title,
      body,
      data: {
        type: 'validation_status',
        status,
        validationId,
        url
      },
      userId
    };

    // Reutilizar a função de envio
    req.body = payload;
    await router.post('/send')(req, res);

  } catch (error) {
    console.error('[Notifications] Error sending validation status:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor'
    });
  }
});

/**
 * @swagger
 * /api/notifications/stats:
 *   get:
 *     summary: Estatísticas de notificações
 *     tags: [Notifications]
 *     responses:
 *       200:
 *         description: Estatísticas das notificações
 */
router.get('/stats', (req, res) => {
  try {
    const stats = {
      totalSubscriptions: subscriptions.size,
      activeSubscriptions: Array.from(subscriptions.values()).filter(
        sub => new Date() - new Date(sub.createdAt) < 30 * 24 * 60 * 60 * 1000 // 30 dias
      ).length,
      subscriptionsByUser: {}
    };

    // Contar por usuário
    for (const subscription of subscriptions.values()) {
      const userId = subscription.userId || 'anonymous';
      stats.subscriptionsByUser[userId] = (stats.subscriptionsByUser[userId] || 0) + 1;
    }

    res.json({
      success: true,
      stats
    });

  } catch (error) {
    console.error('[Notifications] Error getting stats:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor'
    });
  }
});

module.exports = router;
