#!/bin/bash
# ==============================================================================
# Script de Deploy para Produção - Sistema de Validação de Residência
# ==============================================================================

set -e

echo "🚀 Iniciando deploy do Sistema de Validação de Residência..."

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Função para logs
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Verificar se Docker está instalado
if ! command -v docker &> /dev/null; then
    log_error "Docker não está instalado!"
    exit 1
fi

# Verificar se Docker Compose está instalado
if ! command -v docker-compose &> /dev/null; then
    log_error "Docker Compose não está instalado!"
    exit 1
fi

# Verificar se arquivo .env.production existe
if [ ! -f ".env.production" ]; then
    log_error "Arquivo .env.production não encontrado!"
    log_info "Copie o arquivo .env.production.example e configure as variáveis"
    exit 1
fi

# Verificar variáveis críticas
log_info "Verificando configurações..."

# Ler variáveis do arquivo .env.production
set -a
source .env.production
set +a

# Verificar variáveis obrigatórias
REQUIRED_VARS=(
    "GOOGLE_CLIENT_ID"
    "GOOGLE_CLIENT_SECRET"
    "JWT_SECRET"
    "ENCRYPTION_KEY"
    "POSTGRES_PASSWORD"
    "REDIS_PASSWORD"
)

for var in "${REQUIRED_VARS[@]}"; do
    if [ -z "${!var}" ]; then
        log_error "Variável $var não está definida em .env.production"
        exit 1
    fi
done

log_info "Configurações validadas com sucesso!"

# Parar containers existentes
log_info "Parando containers existentes..."
docker-compose -f docker-compose.prod.yml down

# Limpar imagens antigas (opcional)
read -p "Deseja remover imagens antigas? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    log_info "Removendo imagens antigas..."
    docker system prune -f
    docker image prune -f
fi

# Criar diretórios necessários
log_info "Criando diretórios necessários..."
mkdir -p logs
mkdir -p backups
mkdir -p ssl

# Verificar certificados SSL
if [ ! -f "ssl/fullchain.pem" ] || [ ! -f "ssl/privkey.pem" ]; then
    log_warn "Certificados SSL não encontrados em ./ssl/"
    log_warn "Para HTTPS, coloque os certificados em:"
    log_warn "  - ssl/fullchain.pem"
    log_warn "  - ssl/privkey.pem"
    log_warn "Ou use Let's Encrypt com Certbot"
fi

# Build das imagens
log_info "Construindo imagens Docker..."
docker-compose -f docker-compose.prod.yml build --no-cache

# Inicializar banco de dados
log_info "Inicializando banco de dados..."
docker-compose -f docker-compose.prod.yml up -d postgres redis

# Aguardar banco estar pronto
log_info "Aguardando banco de dados ficar pronto..."
sleep 30

# Verificar se banco está funcionando
if ! docker-compose -f docker-compose.prod.yml exec postgres pg_isready -U residencia_user -d residencia_db; then
    log_error "Banco de dados não está respondendo!"
    exit 1
fi

# Iniciar todos os serviços
log_info "Iniciando todos os serviços..."
docker-compose -f docker-compose.prod.yml up -d

# Aguardar aplicação estar pronta
log_info "Aguardando aplicação ficar pronta..."
sleep 45

# Verificar se aplicação está rodando
if curl -f http://localhost:3000/health &> /dev/null; then
    log_info "✅ Aplicação está respondendo!"
else
    log_warn "⚠️  Aplicação pode não estar totalmente pronta ainda"
fi

# Mostrar status dos containers
log_info "Status dos containers:"
docker-compose -f docker-compose.prod.yml ps

# Mostrar logs da aplicação
log_info "Últimos logs da aplicação:"
docker-compose -f docker-compose.prod.yml logs --tail=20 app

# Informações finais
echo ""
log_info "🎉 Deploy concluído!"
echo ""
echo "📋 Informações do deployment:"
echo "  - Aplicação: http://localhost:3000"
echo "  - Base de dados: PostgreSQL na porta 5432"
echo "  - Cache: Redis"
echo "  - Nginx: Porta 80/443"
echo ""
echo "📝 Comandos úteis:"
echo "  - Ver logs: docker-compose -f docker-compose.prod.yml logs -f"
echo "  - Parar: docker-compose -f docker-compose.prod.yml down"
echo "  - Restart: docker-compose -f docker-compose.prod.yml restart"
echo "  - Backup DB: docker-compose -f docker-compose.prod.yml exec backup /backup-script.sh"
echo ""

# Verificar se deve configurar domínio
if [ "$NEXT_PUBLIC_BASE_URL" = "https://seu-dominio.com" ]; then
    log_warn "⚠️  Lembre-se de:"
    log_warn "  1. Configurar seu domínio no DNS"
    log_warn "  2. Atualizar nginx.conf com seu domínio real"
    log_warn "  3. Configurar certificados SSL"
    log_warn "  4. Atualizar .env.production com URLs corretas"
fi

log_info "Deploy finalizado com sucesso! 🚀"
