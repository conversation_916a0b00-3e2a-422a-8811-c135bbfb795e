const express = require('express');
const AuditService = require('../services/auditService');
const router = express.Router();

const auditService = new AuditService();

/**
 * @swagger
 * /api/audit/logs:
 *   get:
 *     summary: Obter logs de auditoria
 *     tags: [Audit]
 *     parameters:
 *       - in: query
 *         name: recordingId
 *         schema:
 *           type: string
 *         description: Filtrar por ID da gravação
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *         description: Filtrar por ID do usuário
 *       - in: query
 *         name: action
 *         schema:
 *           type: string
 *         description: Filtrar por ação
 *       - in: query
 *         name: severity
 *         schema:
 *           type: string
 *           enum: [INFO, WARNING, ERROR, CRITICAL]
 *         description: Filtrar por severidade
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Data de início
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Data de fim
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 100
 *         description: Número máximo de registros
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           default: 0
 *         description: Offset para paginação
 *     responses:
 *       200:
 *         description: Logs de auditoria
 *       500:
 *         description: Erro interno do servidor
 */
router.get('/logs', async (req, res) => {
  try {
    const filters = {
      recordingId: req.query.recordingId,
      userId: req.query.userId,
      action: req.query.action,
      severity: req.query.severity,
      category: req.query.category,
      startDate: req.query.startDate,
      endDate: req.query.endDate,
      limit: parseInt(req.query.limit) || 100,
      offset: parseInt(req.query.offset) || 0,
      orderBy: req.query.orderBy || 'timestamp',
      orderDirection: req.query.orderDirection || 'DESC'
    };

    const result = await auditService.getAuditLogs(filters);

    // Log da consulta de auditoria
    await auditService.logAction({
      action: 'AUDIT_LOGS_QUERY',
      userId: req.user?.id,
      userName: req.user?.name,
      userRole: req.user?.role,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      details: {
        filters,
        resultCount: result.logs.length
      },
      severity: 'INFO',
      category: 'AUDIT_ACCESS'
    });

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('[Audit] Erro ao obter logs:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao obter logs de auditoria',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/audit/log:
 *   post:
 *     summary: Registrar ação de auditoria
 *     tags: [Audit]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               recordingId:
 *                 type: string
 *               action:
 *                 type: string
 *               details:
 *                 type: object
 *               severity:
 *                 type: string
 *                 enum: [INFO, WARNING, ERROR, CRITICAL]
 *               category:
 *                 type: string
 *     responses:
 *       200:
 *         description: Ação registrada com sucesso
 *       400:
 *         description: Dados inválidos
 *       500:
 *         description: Erro interno do servidor
 */
router.post('/log', async (req, res) => {
  try {
    const { recordingId, action, details, severity, category } = req.body;

    if (!action) {
      return res.status(400).json({
        success: false,
        error: 'Ação é obrigatória'
      });
    }

    const actionData = {
      recordingId,
      action,
      userId: req.user?.id,
      userName: req.user?.name,
      userRole: req.user?.role,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      details: details || {},
      severity: severity || 'INFO',
      category: category || 'MANUAL'
    };

    const result = await auditService.logAction(actionData);

    res.json({
      success: true,
      message: 'Ação registrada com sucesso',
      data: result
    });
  } catch (error) {
    console.error('[Audit] Erro ao registrar ação:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao registrar ação de auditoria',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/audit/verify/{logId}:
 *   get:
 *     summary: Verificar integridade de log
 *     tags: [Audit]
 *     parameters:
 *       - in: path
 *         name: logId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID do log
 *     responses:
 *       200:
 *         description: Verificação concluída
 *       404:
 *         description: Log não encontrado
 *       500:
 *         description: Erro interno do servidor
 */
router.get('/verify/:logId', async (req, res) => {
  try {
    const { logId } = req.params;
    const result = await auditService.verifyLogIntegrity(parseInt(logId));

    // Log da verificação
    await auditService.logAction({
      action: 'AUDIT_LOG_VERIFICATION',
      userId: req.user?.id,
      userName: req.user?.name,
      userRole: req.user?.role,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      details: {
        verifiedLogId: logId,
        verificationResult: result.valid
      },
      severity: result.valid ? 'INFO' : 'WARNING',
      category: 'INTEGRITY_CHECK'
    });

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('[Audit] Erro na verificação:', error);
    res.status(500).json({
      success: false,
      error: 'Erro na verificação de integridade',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/audit/verify-batch:
 *   post:
 *     summary: Verificar integridade de múltiplos logs
 *     tags: [Audit]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               logIds:
 *                 type: array
 *                 items:
 *                   type: integer
 *     responses:
 *       200:
 *         description: Verificação em lote concluída
 *       400:
 *         description: Dados inválidos
 *       500:
 *         description: Erro interno do servidor
 */
router.post('/verify-batch', async (req, res) => {
  try {
    const { logIds } = req.body;

    if (!Array.isArray(logIds) || logIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Lista de IDs de log é obrigatória'
      });
    }

    if (logIds.length > 1000) {
      return res.status(400).json({
        success: false,
        error: 'Máximo de 1000 logs por verificação'
      });
    }

    const result = await auditService.verifyMultipleLogsIntegrity(logIds);

    // Log da verificação em lote
    await auditService.logAction({
      action: 'AUDIT_BATCH_VERIFICATION',
      userId: req.user?.id,
      userName: req.user?.name,
      userRole: req.user?.role,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      details: {
        totalLogs: result.total,
        validLogs: result.valid,
        invalidLogs: result.invalid
      },
      severity: result.invalid > 0 ? 'WARNING' : 'INFO',
      category: 'INTEGRITY_CHECK'
    });

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('[Audit] Erro na verificação em lote:', error);
    res.status(500).json({
      success: false,
      error: 'Erro na verificação em lote',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/audit/report:
 *   get:
 *     summary: Gerar relatório de auditoria
 *     tags: [Audit]
 *     parameters:
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Data de início
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Data de fim
 *     responses:
 *       200:
 *         description: Relatório de auditoria
 *       500:
 *         description: Erro interno do servidor
 */
router.get('/report', async (req, res) => {
  try {
    const filters = {
      startDate: req.query.startDate,
      endDate: req.query.endDate
    };

    const report = await auditService.generateAuditReport(filters);

    // Log da geração de relatório
    await auditService.logAction({
      action: 'AUDIT_REPORT_GENERATION',
      userId: req.user?.id,
      userName: req.user?.name,
      userRole: req.user?.role,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      details: {
        filters,
        totalLogs: report.summary.totalLogs
      },
      severity: 'INFO',
      category: 'REPORT_GENERATION'
    });

    res.json({
      success: true,
      data: report
    });
  } catch (error) {
    console.error('[Audit] Erro ao gerar relatório:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao gerar relatório de auditoria',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/audit/statistics:
 *   get:
 *     summary: Obter estatísticas de auditoria
 *     tags: [Audit]
 *     responses:
 *       200:
 *         description: Estatísticas de auditoria
 *       500:
 *         description: Erro interno do servidor
 */
router.get('/statistics', async (req, res) => {
  try {
    const stats = await auditService.pool.query(`
      SELECT 
        COUNT(*) as total_logs,
        COUNT(CASE WHEN severity = 'ERROR' THEN 1 END) as error_logs,
        COUNT(CASE WHEN severity = 'WARNING' THEN 1 END) as warning_logs,
        COUNT(CASE WHEN severity = 'CRITICAL' THEN 1 END) as critical_logs,
        COUNT(DISTINCT user_id) as unique_users,
        COUNT(DISTINCT recording_id) as recordings_accessed,
        COUNT(CASE WHEN timestamp > NOW() - INTERVAL '24 hours' THEN 1 END) as logs_last_24h,
        COUNT(CASE WHEN timestamp > NOW() - INTERVAL '7 days' THEN 1 END) as logs_last_7d
      FROM audit_logs
    `);

    const topActions = await auditService.pool.query(`
      SELECT action, COUNT(*) as count
      FROM audit_logs
      WHERE timestamp > NOW() - INTERVAL '30 days'
      GROUP BY action
      ORDER BY count DESC
      LIMIT 10
    `);

    const topUsers = await auditService.pool.query(`
      SELECT user_id, user_name, COUNT(*) as actions
      FROM audit_logs
      WHERE timestamp > NOW() - INTERVAL '30 days'
      GROUP BY user_id, user_name
      ORDER BY actions DESC
      LIMIT 10
    `);

    res.json({
      success: true,
      data: {
        overview: stats.rows[0],
        topActions: topActions.rows,
        topUsers: topUsers.rows,
        generatedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('[Audit] Erro ao obter estatísticas:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao obter estatísticas de auditoria',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
