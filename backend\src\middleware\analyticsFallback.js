/**
 * Middleware para garantir fallbacks em rotas de analytics
 * Evita que erros de banco quebrem o frontend
 */

const analyticsFallbackData = {
  dashboard: {
    totalRecordings: 0,
    totalUsers: 0,
    totalSize: 0,
    avgDuration: 0,
    completedRecordings: 0,
    processingRecordings: 0,
    failedRecordings: 0,
    archivedRecordings: 0,
    firstRecording: null,
    lastRecording: null,
    totalTranscriptions: 0,
    totalEmbeddings: 0,
    totalSizeGB: 0,
    avgDurationMinutes: 0,
    completionRate: 0,
    systemUptime: 0,
    memoryUsage: {
      used: 0,
      total: 0
    },
    timestamp: new Date().toISOString()
  },
  
  trends: [],
  
  users: [],
  
  compliance: {
    totalRecordings: 0,
    withConsent: 0,
    withoutConsent: 0,
    complianceRate: 100,
    oldestRecording: null,
    oldRecordings: 0,
    recentRecordings: 0,
    dataRetentionStatus: 'compliant',
    dataRetentionDays: 730,
    recommendations: ['Sistema em conformidade com políticas de retenção']
  }
};

/**
 * Middleware para interceptar erros em rotas de analytics
 */
function analyticsErrorHandler(req, res, next) {
  // Interceptar o método json para capturar erros
  const originalJson = res.json;
  
  res.json = function(data) {
    // Se é um erro (success: false), converter para fallback
    if (data && data.success === false) {
      console.warn(`[Analytics Fallback] Convertendo erro para fallback na rota: ${req.path}`);
      
      // Determinar qual fallback usar baseado na rota
      let fallbackData;
      
      if (req.path.includes('/dashboard')) {
        fallbackData = analyticsFallbackData.dashboard;
      } else if (req.path.includes('/trends')) {
        fallbackData = analyticsFallbackData.trends;
      } else if (req.path.includes('/users')) {
        fallbackData = analyticsFallbackData.users;
      } else if (req.path.includes('/compliance')) {
        fallbackData = analyticsFallbackData.compliance;
      } else {
        fallbackData = {};
      }
      
      // Retornar dados de fallback com sucesso
      const fallbackResponse = {
        success: true,
        data: fallbackData,
        fallback: true,
        originalError: data.error || data.message,
        timestamp: new Date().toISOString()
      };
      
      return originalJson.call(this, fallbackResponse);
    }
    
    // Se não é erro, retornar normalmente
    return originalJson.call(this, data);
  };
  
  next();
}

/**
 * Middleware para adicionar timeout em rotas de analytics
 */
function analyticsTimeout(timeoutMs = 10000) {
  return (req, res, next) => {
    const timeout = setTimeout(() => {
      if (!res.headersSent) {
        console.warn(`[Analytics Timeout] Timeout na rota: ${req.path}`);
        
        // Determinar fallback baseado na rota
        let fallbackData;
        
        if (req.path.includes('/dashboard')) {
          fallbackData = analyticsFallbackData.dashboard;
        } else if (req.path.includes('/trends')) {
          fallbackData = analyticsFallbackData.trends;
        } else if (req.path.includes('/users')) {
          fallbackData = analyticsFallbackData.users;
        } else if (req.path.includes('/compliance')) {
          fallbackData = analyticsFallbackData.compliance;
        } else {
          fallbackData = {};
        }
        
        res.json({
          success: true,
          data: fallbackData,
          timeout: true,
          message: 'Dados obtidos com fallback devido a timeout',
          timestamp: new Date().toISOString()
        });
      }
    }, timeoutMs);
    
    // Limpar timeout quando resposta for enviada
    res.on('finish', () => {
      clearTimeout(timeout);
    });
    
    next();
  };
}

/**
 * Middleware para log de performance de analytics
 */
function analyticsPerformanceLogger(req, res, next) {
  const startTime = Date.now();
  
  // Interceptar o método json para medir tempo
  const originalJson = res.json;
  
  res.json = function(data) {
    const duration = Date.now() - startTime;
    
    console.log(`[Analytics Performance] ${req.method} ${req.path} - ${duration}ms - Success: ${data?.success || false}`);
    
    // Log warning se demorou muito
    if (duration > 5000) {
      console.warn(`[Analytics Performance] Rota lenta detectada: ${req.path} (${duration}ms)`);
    }
    
    return originalJson.call(this, data);
  };
  
  next();
}

/**
 * Middleware para validar parâmetros de analytics
 */
function analyticsParamsValidator(req, res, next) {
  // Validar parâmetros comuns
  if (req.query.period && !['day', 'week', 'month', 'year'].includes(req.query.period)) {
    return res.json({
      success: true,
      data: analyticsFallbackData.trends,
      warning: 'Período inválido, usando dados padrão',
      timestamp: new Date().toISOString()
    });
  }
  
  if (req.query.limit) {
    const limit = parseInt(req.query.limit);
    if (isNaN(limit) || limit < 1 || limit > 1000) {
      req.query.limit = '100'; // Valor padrão
    }
  }
  
  next();
}

/**
 * Middleware combinado para analytics
 */
function analyticsMiddleware(options = {}) {
  const {
    timeout = 10000,
    enablePerformanceLog = true,
    enableParamsValidation = true
  } = options;
  
  return [
    // Timeout
    analyticsTimeout(timeout),
    
    // Performance log (se habilitado)
    ...(enablePerformanceLog ? [analyticsPerformanceLogger] : []),
    
    // Validação de parâmetros (se habilitado)
    ...(enableParamsValidation ? [analyticsParamsValidator] : []),
    
    // Error handler (sempre por último)
    analyticsErrorHandler
  ];
}

module.exports = {
  analyticsErrorHandler,
  analyticsTimeout,
  analyticsPerformanceLogger,
  analyticsParamsValidator,
  analyticsMiddleware,
  analyticsFallbackData
};
