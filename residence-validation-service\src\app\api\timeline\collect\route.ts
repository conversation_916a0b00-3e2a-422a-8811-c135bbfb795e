import { NextRequest, NextResponse } from 'next/server';
import { googleAuthService } from '@/lib/google-auth';
import { validateResidence } from '@/utils/validation';
import { LocationData, ValidationResult } from '@/types';
import jwt from 'jsonwebtoken';
import { ENV } from '@/lib/config';

// Middleware para verificar autenticação
function verifyToken(request: NextRequest) {
  const token = request.cookies.get('auth-token')?.value;
  
  if (!token) {
    throw new Error('Token não encontrado');
  }

  try {
    const decoded = jwt.verify(token, ENV.JWT_SECRET) as any;
    return decoded;
  } catch (error) {
    throw new Error('Token inválido');
  }
}

export async function POST(request: NextRequest) {
  try {
    // Verifica autenticação
    const userToken = verifyToken(request);
    
    // Parse do body da requisição
    const { homeAddress, coordinates, startDate, endDate } = await request.json();

    if (!homeAddress || !coordinates || !startDate || !endDate) {
      return NextResponse.json(
        { success: false, error: 'Dados obrigatórios não fornecidos' },
        { status: 400 }
      );
    }

    // Configura credenciais do Google
    googleAuthService.setCredentials(userToken.googleTokens);

    // Verifica se o token ainda é válido
    if (!googleAuthService.isTokenValid()) {
      await googleAuthService.refreshToken();
    }

    // Coleta dados da Timeline
    const timelineData = await googleAuthService.getTimelineData(startDate, endDate);

    // Processa dados da Timeline para o formato esperado
    const locationData: LocationData[] = [];

    if (timelineData.timelineObjects) {
      for (const obj of timelineData.timelineObjects) {
        // Processa segmentos de atividade
        if (obj.activitySegment) {
          const segment = obj.activitySegment;
          
          // Adiciona localização inicial
          if (segment.startLocation) {
            locationData.push({
              timestamp: segment.duration.startTimestamp,
              latitude: segment.startLocation.latitudeE7 / 10000000,
              longitude: segment.startLocation.longitudeE7 / 10000000,
              accuracy: 50, // Estimativa padrão
              source: 'GPS',
              address: segment.startLocation.address,
            });
          }

          // Adiciona localização final
          if (segment.endLocation) {
            locationData.push({
              timestamp: segment.duration.endTimestamp,
              latitude: segment.endLocation.latitudeE7 / 10000000,
              longitude: segment.endLocation.longitudeE7 / 10000000,
              accuracy: 50,
              source: 'GPS',
              address: segment.endLocation.address,
            });
          }
        }

        // Processa visitas a lugares
        if (obj.placeVisit) {
          const visit = obj.placeVisit;
          
          if (visit.location) {
            locationData.push({
              timestamp: visit.duration.startTimestamp,
              latitude: visit.location.latitudeE7 / 10000000,
              longitude: visit.location.longitudeE7 / 10000000,
              accuracy: 30,
              source: 'GPS',
              address: visit.location.address || visit.location.name,
            });
          }
        }
      }
    }

    // Ordena por timestamp
    locationData.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

    // Executa validação de residência
    const validationResult: ValidationResult = validateResidence(
      locationData,
      coordinates.latitude,
      coordinates.longitude
    );

    // Cria registro de validação
    const validationRecord = {
      id: `val_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId: userToken.userId,
      address: homeAddress,
      coordinates: coordinates,
      validationPeriod: {
        startDate: startDate,
        endDate: endDate,
      },
      nightlyPresence: validationResult.nightlyRecords,
      status: validationResult.isValid ? 'APPROVED' : 'REJECTED',
      validationScore: validationResult.score,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // TODO: Salvar no banco de dados
    // await saveValidationRecord(validationRecord);

    return NextResponse.json({
      success: true,
      data: {
        validation: validationRecord,
        result: validationResult,
        totalLocationPoints: locationData.length,
      },
    });

  } catch (error) {
    console.error('Erro ao coletar dados da Timeline:', error);
    
    if (error instanceof Error && error.message.includes('Token')) {
      return NextResponse.json(
        { success: false, error: 'Não autorizado' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
