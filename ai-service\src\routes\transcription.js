const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const TranscriptionService = require('../services/transcriptionService');
const queueManager = require('../services/queueManager');

const router = express.Router();
const transcriptionService = new TranscriptionService();

// Configurar multer para upload de arquivos
const storage = multer.diskStorage({
  destination: '/app/storage/temp',
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 100 * 1024 * 1024 // 100MB
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      'video/mp4', 'video/avi', 'video/mov', 'video/wmv',
      'audio/mp3', 'audio/wav', 'audio/m4a', 'audio/ogg'
    ];
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Tipo de arquivo não suportado'), false);
    }
  }
});

/**
 * GET /api/ai/transcription
 * Informações sobre o serviço de transcrição
 */
router.get('/', (req, res) => {
  res.json({
    success: true,
    service: 'Serviço de Transcrição de IA',
    version: '1.0.0',
    availableEndpoints: [
      'POST /api/ai/transcription/upload - Upload e transcrição de arquivo',
      'POST /api/ai/transcription/audio - Transcrição de áudio direto',
      'POST /api/ai/transcription/batch - Transcrição em lote',
      'GET /api/ai/transcription/status/:id - Status da transcrição'
    ],
    supportedFormats: [
      'video/mp4', 'video/avi', 'video/mov', 'video/wmv',
      'audio/mp3', 'audio/wav', 'audio/m4a', 'audio/ogg'
    ],
    maxFileSize: '100MB',
    features: [
      'Transcrição automática com IA',
      'Suporte a múltiplos idiomas',
      'Processamento em lote',
      'Detecção de termos jurídicos',
      'Timestamps precisos',
      'Confiança por palavra'
    ],
    timestamp: new Date().toISOString()
  });
});

/**
 * POST /api/ai/transcription/upload
 * Fazer upload e transcrever arquivo
 */
router.post('/upload', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'Nenhum arquivo enviado'
      });
    }

    const options = {
      language: req.body.language || 'pt',
      prompt: req.body.prompt || 'Esta é uma gravação de cartório com termos jurídicos brasileiros.'
    };

    // Transcrever arquivo
    const result = await transcriptionService.transcribeRecording(req.file.path, options);

    // Limpar arquivo temporário
    await fs.unlink(req.file.path).catch(() => {});

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    // Limpar arquivo em caso de erro
    if (req.file) {
      await fs.unlink(req.file.path).catch(() => {});
    }

    res.status(500).json({
      success: false,
      message: 'Erro na transcrição',
      error: error.message
    });
  }
});

/**
 * POST /api/ai/transcription/recording/:recordingId
 * Transcrever gravação existente
 */
router.post('/recording/:recordingId', async (req, res) => {
  try {
    const { recordingId } = req.params;
    
    // Buscar gravação no backend principal
    const axios = require('axios');
    const backendUrl = process.env.BACKEND_URL || 'http://backend:3001';
    
    const recordingResponse = await axios.get(`${backendUrl}/api/recordings/${recordingId}`);
    const recording = recordingResponse.data;

    if (!recording) {
      return res.status(404).json({
        success: false,
        message: 'Gravação não encontrada'
      });
    }

    const options = {
      language: req.body.language || 'pt',
      prompt: req.body.prompt || 'Esta é uma gravação de cartório com termos jurídicos brasileiros.'
    };

    // Transcrever arquivo da gravação
    const result = await transcriptionService.transcribeRecording(recording.file_path, options);

    // Salvar resultado no banco de dados
    const aiDatabase = require('../database/aiDatabase');
    await aiDatabase.saveTranscription(recordingId, result);

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erro na transcrição da gravação',
      error: error.message
    });
  }
});

/**
 * POST /api/ai/transcription/batch
 * Transcrever múltiplas gravações
 */
router.post('/batch', async (req, res) => {
  try {
    const { recordingIds, options = {} } = req.body;

    if (!recordingIds || !Array.isArray(recordingIds)) {
      return res.status(400).json({
        success: false,
        message: 'Lista de IDs de gravações é obrigatória'
      });
    }

    // Adicionar trabalhos à fila
    const jobs = [];
    for (const recordingId of recordingIds) {
      const job = await queueManager.addTranscriptionJob(recordingId, options);
      jobs.push({
        recordingId,
        jobId: job.id,
        status: 'queued'
      });
    }

    res.json({
      success: true,
      message: `${jobs.length} trabalhos de transcrição adicionados à fila`,
      data: jobs
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erro ao processar lote de transcrições',
      error: error.message
    });
  }
});

/**
 * GET /api/ai/transcription/status/:jobId
 * Verificar status de trabalho de transcrição
 */
router.get('/status/:jobId', async (req, res) => {
  try {
    const { jobId } = req.params;
    const status = await queueManager.getJobStatus(jobId);

    res.json({
      success: true,
      data: status
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erro ao verificar status',
      error: error.message
    });
  }
});

/**
 * GET /api/ai/transcription/recording/:recordingId
 * Obter transcrição de uma gravação
 */
router.get('/recording/:recordingId', async (req, res) => {
  try {
    const { recordingId } = req.params;
    
    const aiDatabase = require('../database/aiDatabase');
    const transcription = await aiDatabase.getTranscription(recordingId);

    if (!transcription) {
      return res.status(404).json({
        success: false,
        message: 'Transcrição não encontrada'
      });
    }

    res.json({
      success: true,
      data: transcription
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erro ao buscar transcrição',
      error: error.message
    });
  }
});

module.exports = router;
