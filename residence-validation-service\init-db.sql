-- ==============================================================================
-- Script de Inicialização do Banco de Dados
-- Sistema de Validação de Residência
-- ==============================================================================

-- Criar extensões necessárias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Tabela de usuários
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    google_id VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    document VARCHAR(20),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de validações de residência
CREATE TABLE IF NOT EXISTS residence_validations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    address TEXT NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status VARCHAR(20) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'VALIDATING', 'APPROVED', 'REJECTED')),
    validation_score DECIMAL(3, 2) DEFAULT 0.0,
    valid_days INTEGER DEFAULT 0,
    total_days INTEGER DEFAULT 0,
    confidence DECIMAL(3, 2) DEFAULT 0.0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de registros noturnos
CREATE TABLE IF NOT EXISTS nightly_presence_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    validation_id UUID NOT NULL REFERENCES residence_validations(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    start_time TIME DEFAULT '18:00:00',
    end_time TIME DEFAULT '06:00:00',
    is_present BOOLEAN DEFAULT FALSE,
    confidence DECIMAL(3, 2) DEFAULT 0.0,
    distance_from_home DECIMAL(8, 2) DEFAULT 0.0,
    location_points_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(validation_id, date)
);

-- Tabela de certificados
CREATE TABLE IF NOT EXISTS residence_certificates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    validation_id UUID NOT NULL REFERENCES residence_validations(id) ON DELETE CASCADE,
    certificate_number VARCHAR(50) UNIQUE NOT NULL,
    issue_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    valid_until TIMESTAMP WITH TIME ZONE NOT NULL,
    address TEXT NOT NULL,
    validation_score DECIMAL(3, 2) NOT NULL,
    valid_days INTEGER NOT NULL,
    pdf_url TEXT,
    qr_code VARCHAR(100) NOT NULL,
    status VARCHAR(20) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'EXPIRED', 'REVOKED')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de dados de localização (temporária - para processamento)
CREATE TABLE IF NOT EXISTS location_data_temp (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    validation_id UUID NOT NULL REFERENCES residence_validations(id) ON DELETE CASCADE,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    accuracy INTEGER DEFAULT 0,
    source VARCHAR(20) DEFAULT 'GPS',
    address TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de logs de auditoria (LGPD)
CREATE TABLE IF NOT EXISTS audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    action VARCHAR(50) NOT NULL,
    resource_type VARCHAR(50),
    resource_id UUID,
    details JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    INDEX (user_id, created_at),
    INDEX (action, created_at)
);

-- Tabela de consentimentos LGPD
CREATE TABLE IF NOT EXISTS user_consents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    consent_type VARCHAR(50) NOT NULL,
    consent_given BOOLEAN DEFAULT FALSE,
    consent_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ip_address INET,
    version VARCHAR(10) DEFAULT '1.0',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, consent_type)
);

-- Tabela de sessões (opcional - se não usar Redis)
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    INDEX (session_token),
    INDEX (expires_at)
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_google_id ON users(google_id);
CREATE INDEX IF NOT EXISTS idx_validations_user_id ON residence_validations(user_id);
CREATE INDEX IF NOT EXISTS idx_validations_status ON residence_validations(status);
CREATE INDEX IF NOT EXISTS idx_validations_created_at ON residence_validations(created_at);
CREATE INDEX IF NOT EXISTS idx_nightly_records_validation_id ON nightly_presence_records(validation_id);
CREATE INDEX IF NOT EXISTS idx_nightly_records_date ON nightly_presence_records(date);
CREATE INDEX IF NOT EXISTS idx_certificates_user_id ON residence_certificates(user_id);
CREATE INDEX IF NOT EXISTS idx_certificates_number ON residence_certificates(certificate_number);
CREATE INDEX IF NOT EXISTS idx_certificates_status ON residence_certificates(status);
CREATE INDEX IF NOT EXISTS idx_location_temp_validation_id ON location_data_temp(validation_id);
CREATE INDEX IF NOT EXISTS idx_location_temp_timestamp ON location_data_temp(timestamp);

-- Triggers para updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_validations_updated_at BEFORE UPDATE ON residence_validations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_certificates_updated_at BEFORE UPDATE ON residence_certificates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Função para limpeza automática de dados temporários (LGPD)
CREATE OR REPLACE FUNCTION cleanup_old_data()
RETURNS void AS $$
BEGIN
    -- Remove dados de localização temporários após 90 dias
    DELETE FROM location_data_temp 
    WHERE created_at < NOW() - INTERVAL '90 days';
    
    -- Remove logs de auditoria após 1 ano
    DELETE FROM audit_logs 
    WHERE created_at < NOW() - INTERVAL '1 year';
    
    -- Remove sessões expiradas
    DELETE FROM user_sessions 
    WHERE expires_at < NOW();
    
    -- Remove certificados expirados há mais de 1 ano
    UPDATE residence_certificates 
    SET status = 'EXPIRED' 
    WHERE valid_until < NOW() AND status = 'ACTIVE';
END;
$$ LANGUAGE plpgsql;

-- Inserir dados iniciais se necessário
INSERT INTO user_consents (user_id, consent_type, consent_given, version) 
VALUES (uuid_generate_v4(), 'data_processing', true, '1.0') 
ON CONFLICT DO NOTHING;

-- Mensagem de sucesso
DO $$
BEGIN
    RAISE NOTICE 'Database initialized successfully for Sistema de Validação de Residência';
END $$;
