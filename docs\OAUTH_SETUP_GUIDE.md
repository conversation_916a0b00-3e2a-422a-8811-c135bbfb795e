# 🔧 Guia de Configuração OAuth2 Google - Resolução de Problemas

## 🚨 Erro: "OAuth client was not found" - Erro 401: invalid_client

Este guia resolve o problema de configuração do Google OAuth2 para o Sistema de Validação de Residência.

## 📋 Pré-requisitos

1. ✅ Conta Google Cloud Platform
2. ✅ Projeto criado no Google Cloud Console
3. ✅ APIs necessárias habilitadas

## 🏗️ Passo 1: Configurar Google Cloud Console

### 1.1 Acessar o Console
1. Vá para [Google Cloud Console](https://console.cloud.google.com)
2. Faça login com sua conta Google
3. Selecione ou crie um projeto

### 1.2 Habilitar APIs Necessárias
```bash
# APIs obrigatórias:
1. Google+ API (para userinfo)
2. People API (para perfil)
3. Maps JavaScript API (para geocoding)
4. Timeline API (se disponível)
```

**Como habilitar:**
1. No menu lateral → "APIs & Services" → "Library"
2. Busque por cada API acima
3. Clique em "Enable" para cada uma

### 1.3 Criar Credenciais OAuth2

1. **Ir para Credenciais:**
   - Menu lateral → "APIs & Services" → "Credentials"

2. **Criar OAuth 2.0 Client ID:**
   - Clique em "+ CREATE CREDENTIALS"
   - Selecione "OAuth 2.0 Client ID"

3. **Configurar Tela de Consentimento (se necessário):**
   ```
   Application name: Sistema de Validação de Residência
   User support email: <EMAIL>
   Authorized domains: localhost (para dev) + seu-dominio.com (para prod)
   ```

4. **Configurar Client ID:**
   ```
   Application type: Web application
   Name: Sistema Residencia - Web Client
   
   Authorized JavaScript origins:
   - http://localhost:3000 (desenvolvimento)
   - https://seu-dominio.com (produção)
   
   Authorized redirect URIs:
   - http://localhost:3000/api/auth/callback (desenvolvimento)  
   - https://seu-dominio.com/api/auth/callback (produção)
   ```

5. **Salvar e Copiar Credenciais:**
   - Copie o `Client ID` e `Client Secret`

## 🏗️ Passo 2: Configurar Variáveis de Ambiente

### 2.1 Para Desenvolvimento Local

Edite o arquivo `.env.local`:

```bash
# ⚠️ SUBSTITUA PELOS VALORES REAIS DO GOOGLE CLOUD CONSOLE
GOOGLE_CLIENT_ID=123456789-abcdefghijklmnop.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
NEXT_PUBLIC_GOOGLE_REDIRECT_URI=http://localhost:3000/api/auth/callback

# JWT Secret (use um valor complexo)
JWT_SECRET=minha_chave_jwt_super_secreta_123456789

# Chave de criptografia (exatamente 32 caracteres)
ENCRYPTION_KEY=minha_chave_de_32_caracteres_ok!!

# URL base
NEXT_PUBLIC_BASE_URL=http://localhost:3000
NODE_ENV=development
```

### 2.2 Para Docker/Produção

Edite o arquivo `.env.production`:

```bash
# ⚠️ SUBSTITUA PELOS VALORES REAIS
GOOGLE_CLIENT_ID=seu_client_id_de_producao
GOOGLE_CLIENT_SECRET=sua_secret_de_producao  
NEXT_PUBLIC_GOOGLE_REDIRECT_URI=https://seu-dominio.com/api/auth/callback

JWT_SECRET=sua_chave_jwt_de_producao_super_secreta
ENCRYPTION_KEY=sua_chave_de_32_caracteres_prod!!
NEXT_PUBLIC_BASE_URL=https://seu-dominio.com
NODE_ENV=production
```

## 🐳 Passo 3: Configurar Docker

### 3.1 Verificar docker-compose.prod.yml

```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      # Carregar do arquivo .env.production
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - NEXT_PUBLIC_GOOGLE_REDIRECT_URI=${NEXT_PUBLIC_GOOGLE_REDIRECT_URI}
      - JWT_SECRET=${JWT_SECRET}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - NEXT_PUBLIC_BASE_URL=${NEXT_PUBLIC_BASE_URL}
      - NODE_ENV=production
    env_file:
      - .env.production
```

### 3.2 Executar Docker com Variáveis

```bash
# Opção 1: Com docker-compose
docker-compose -f docker-compose.prod.yml up --build

# Opção 2: Com docker run
docker run -p 3000:3000 \
  --env-file .env.production \
  sistema-residencia

# Opção 3: Passar variáveis diretamente
docker run -p 3000:3000 \
  -e GOOGLE_CLIENT_ID="seu_client_id_aqui" \
  -e GOOGLE_CLIENT_SECRET="sua_secret_aqui" \
  -e NEXT_PUBLIC_GOOGLE_REDIRECT_URI="http://localhost:3000/api/auth/callback" \
  sistema-residencia
```

## 🔍 Passo 4: Testes e Diagnósticos

### 4.1 Verificar se Variáveis Estão Carregando

Adicione este endpoint temporário para debug:

```typescript
// src/app/api/debug/env/route.ts (APENAS PARA DEBUG - REMOVER EM PRODUÇÃO)
export async function GET() {
  return Response.json({
    hasClientId: !!process.env.GOOGLE_CLIENT_ID,
    hasClientSecret: !!process.env.GOOGLE_CLIENT_SECRET,
    redirectUri: process.env.NEXT_PUBLIC_GOOGLE_REDIRECT_URI,
    nodeEnv: process.env.NODE_ENV,
    // NÃO EXPONHA OS VALORES REAIS
    clientIdPrefix: process.env.GOOGLE_CLIENT_ID?.substring(0, 10) + '...',
  });
}
```

Acesse: `http://localhost:3000/api/debug/env`

### 4.2 Testar URLs de Redirect

Verifique se as URLs estão exatamente iguais em:
1. ✅ Google Cloud Console → Credentials → Authorized redirect URIs
2. ✅ Variável `NEXT_PUBLIC_GOOGLE_REDIRECT_URI`
3. ✅ Código da aplicação

### 4.3 Logs de Debug

Adicione logs temporários em `src/lib/google-auth.ts`:

```typescript
constructor() {
  console.log('🔧 Google Auth Debug:', {
    hasClientId: !!process.env.GOOGLE_CLIENT_ID,
    hasClientSecret: !!process.env.GOOGLE_CLIENT_SECRET,
    redirectUri: process.env.NEXT_PUBLIC_GOOGLE_REDIRECT_URI,
  });
  
  // ... resto do código
}
```

## 🎯 Soluções Rápidas por Cenário

### Cenário 1: Erro em Desenvolvimento Local
```bash
# 1. Verificar arquivo .env.local existe
ls -la .env.local

# 2. Verificar se valores não são placeholders
cat .env.local | grep GOOGLE_CLIENT_ID

# 3. Reiniciar servidor
npm run dev
```

### Cenário 2: Erro no Docker
```bash
# 1. Verificar se .env.production tem valores reais
cat .env.production | grep GOOGLE_CLIENT_ID

# 2. Rebuild Docker com --no-cache
docker build --no-cache -t sistema-residencia .

# 3. Verificar variáveis dentro do container
docker run -it sistema-residencia env | grep GOOGLE
```

### Cenário 3: URLs de Redirect Incorretas
```bash
# Verificar se URLs no Google Console são EXATAMENTE:
# Desenvolvimento: http://localhost:3000/api/auth/callback
# Produção: https://seu-dominio.com/api/auth/callback

# ⚠️ CUIDADO COM:
# - http vs https
# - localhost vs 127.0.0.1
# - Barras extras (//)
# - Portas diferentes
```

## 📝 Checklist de Verificação

### ✅ Google Cloud Console
- [ ] Projeto criado e selecionado
- [ ] APIs habilitadas (Google+, People, Maps)
- [ ] OAuth 2.0 Client ID criado
- [ ] JavaScript origins configurados
- [ ] Redirect URIs configurados
- [ ] Credenciais copiadas

### ✅ Arquivos de Configuração
- [ ] `.env.local` com credenciais reais
- [ ] `.env.production` com credenciais de produção
- [ ] `docker-compose.prod.yml` carregando variáveis
- [ ] Dockerfile copiando arquivos de ambiente

### ✅ Aplicação
- [ ] Servidor reiniciado após mudanças
- [ ] Docker rebuild após mudanças de configuração
- [ ] URLs de redirect exatamente iguais
- [ ] Logs não mostram erros de carregamento

## 🆘 Se Ainda Não Funcionar

1. **Verificar Status das APIs do Google:**
   - Às vezes as APIs podem estar temporariamente indisponíveis

2. **Testar com Credenciais Simples:**
   - Crie um projeto Google Cloud completamente novo
   - Use apenas as APIs básicas (Google+ e People)

3. **Verificar Quotas:**
   - Google Cloud Console → APIs & Services → Quotas
   - Verificar se não atingiu limites

4. **Logs Detalhados:**
   - Ativar logs verbosos no Google Cloud Console
   - Verificar logs do Container/Docker

## 🔄 Resultado Esperado

Após seguir este guia:
- ✅ Botão "Autorizar com Google Maps" funciona
- ✅ Redirecionamento para Google OAuth acontece
- ✅ Callback retorna com sucesso
- ✅ Usuário é autenticado no sistema
- ✅ Dashboard carrega normalmente

---

**⚠️ LEMBRETE DE SEGURANÇA:** 
- Nunca commite arquivos `.env` com credenciais reais
- Use credenciais diferentes para desenvolvimento e produção
- Mantenha Client Secret sempre privado
