const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { body, validationResult } = require('express-validator');
const { 
  saveRecording, 
  getRecordingById, 
  getAllRecordings,
  deleteRecording,
  calculateFileHash
} = require('../database');

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     RecordingUpload:
 *       type: object
 *       required:
 *         - video
 *         - metadata
 *         - consent
 *       properties:
 *         video:
 *           type: string
 *           format: binary
 *           description: Arquivo de vídeo da gravação
 *         metadata:
 *           type: string
 *           description: Metadados da gravação em JSON
 *           example: '{"filename":"gravacao.webm","duration":180,"size":15728640}'
 *         consent:
 *           type: string
 *           description: Dados de consentimento LGPD em JSON
 *           example: '{"name":"<PERSON>","document":"123.456.789-00","purpose":"Registro de procuração"}'
 */

// Configuração do multer para upload de arquivos
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    // Usar diretório permanente em vez de temp
    const uploadDir = path.join(__dirname, '../../storage/recordings');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
      console.log('📁 Diretório de gravações criado:', uploadDir);
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 1024 * 1024 * 500 // 500MB (limite de tamanho)
  },
  fileFilter: (req, file, cb) => {
    // Aceitar apenas arquivos de vídeo
    if (file.mimetype.startsWith('video/')) {
      cb(null, true);
    } else {
      cb(new Error('Apenas arquivos de vídeo são permitidos'));
    }
  }
});

// Validações para upload de gravação
const uploadValidations = [
  body('consent_timestamp').isISO8601().withMessage('Timestamp de consentimento inválido'),
  body('consent_ip').isIP().withMessage('Endereço IP de consentimento inválido'),
  body('consent_user_agent').isString().withMessage('User Agent de consentimento inválido'),
  body('device_info').isString().withMessage('Informações do dispositivo inválidas'),
  body('orientation').isIn(['vertical', 'horizontal']).withMessage('Orientação deve ser vertical ou horizontal')
];

/**
 * @swagger
 * /api/recordings:
 *   get:
 *     summary: Listar todas as gravações
 *     tags: [Recordings]
 *     description: Retorna uma lista paginada de todas as gravações do sistema
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Número da página
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Itens por página
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Termo de busca
 *       - in: query
 *         name: user_name
 *         schema:
 *           type: string
 *         description: Filtrar por nome de usuário
 *       - in: query
 *         name: date_from
 *         schema:
 *           type: string
 *           format: date
 *         description: Data inicial (YYYY-MM-DD)
 *       - in: query
 *         name: date_to
 *         schema:
 *           type: string
 *           format: date
 *         description: Data final (YYYY-MM-DD)
 *       - in: query
 *         name: status
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *             enum: [completed, processing, failed, pending]
 *         description: Filtrar por status
 *     responses:
 *       200:
 *         description: Lista de gravações retornada com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Recording'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     total:
 *                       type: integer
 *                     pages:
 *                       type: integer
 *       500:
 *         description: Erro interno do servidor
 */
router.get('/', async (req, res, next) => {
  try {
    const recordings = await getAllRecordings();
    res.json({
      success: true,
      data: recordings,
      pagination: {
        page: 1,
        limit: recordings.length,
        total: recordings.length,
        pages: 1
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/recordings/{id}:
 *   get:
 *     summary: Buscar gravação por ID
 *     tags: [Recordings]
 *     description: Retorna os detalhes de uma gravação específica
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID único da gravação
 *     responses:
 *       200:
 *         description: Gravação encontrada
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/Recording'
 *       404:
 *         description: Gravação não encontrada
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: Gravação não encontrada
 *       500:
 *         description: Erro interno do servidor
 */
router.get('/:id', async (req, res, next) => {
  try {
    const recording = await getRecordingById(req.params.id);
    
    if (!recording) {
      return res.status(404).json({ 
        success: false,
        error: 'Gravação não encontrada' 
      });
    }
    
    res.json({
      success: true,
      data: recording
    });
  } catch (error) {
    next(error);
  }
});

// Rota para fazer download de uma gravação
router.get('/:id/download', async (req, res, next) => {
  try {
    const recording = await getRecordingById(req.params.id);
    
    if (!recording) {
      return res.status(404).json({ error: 'Gravação não encontrada' });
    }
    
    // Verificar se o arquivo existe
    if (!fs.existsSync(recording.file_path)) {
      console.error('❌ Arquivo não encontrado:', {
        id: req.params.id,
        expected_path: recording.file_path,
        filename: recording.filename
      });

      return res.status(404).json({
        error: 'Arquivo de gravação não encontrado',
        details: {
          id: req.params.id,
          filename: recording.filename,
          expected_path: recording.file_path
        }
      });
    }
    
    // Verificar a integridade do arquivo
    const currentHash = await calculateFileHash(recording.file_path);
    if (currentHash !== recording.hash) {
      return res.status(500).json({ 
        error: 'Falha na verificação de integridade',
        message: 'O hash do arquivo não corresponde ao hash original'
      });
    }
    
    // Enviar o arquivo
    res.download(recording.file_path, recording.file_name);
  } catch (error) {
    next(error);
  }
});

// Rota para fazer upload de uma gravação
router.post('/', upload.single('video'), uploadValidations, async (req, res, next) => {
  // Verificar erros de validação
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    // Remover o arquivo temporário em caso de erro
    if (req.file) {
      fs.unlinkSync(req.file.path);
    }
    return res.status(400).json({ errors: errors.array() });
  }
  
  if (!req.file) {
    return res.status(400).json({ error: 'Nenhum arquivo enviado' });
  }
  
  try {
    // Calcular hash do arquivo
    const fileHash = await calculateFileHash(req.file.path);

    // Criar caminho permanente para o arquivo
    const permanentDir = path.join(__dirname, '../../storage/recordings');
    const permanentPath = path.join(permanentDir, req.file.filename);

    // Garantir que o diretório permanente existe
    if (!fs.existsSync(permanentDir)) {
      fs.mkdirSync(permanentDir, { recursive: true });
    }

    // Mover arquivo do temp para permanente (se necessário)
    if (req.file.path !== permanentPath) {
      fs.copyFileSync(req.file.path, permanentPath);
      fs.unlinkSync(req.file.path); // Remove o temporário
      console.log('📁 Arquivo movido para:', permanentPath);
    }

    // Preparar dados no formato esperado pelo PostgreSQL adapter
    const recordingData = {
      user_id: req.body.user_id || null, // UUID do usuário
      filename: req.file.filename, // Nome gerado pelo multer
      original_filename: req.file.originalname, // Nome original do arquivo
      file_path: permanentPath, // Caminho PERMANENTE do arquivo
      file_size: req.file.size, // Tamanho do arquivo
      duration: req.body.duration ? parseInt(req.body.duration) : null,
      file_hash: fileHash, // Hash do arquivo
      user_name: req.body.user_name || 'Usuário Padrão',
      consent_name: req.body.consent_name || req.body.user_name || 'Usuário',
      consent_document: req.body.consent_document || 'N/A',
      consent_purpose: req.body.consent_purpose || 'Gravação de tela para fins cartorários',
      consent_timestamp: req.body.consent_timestamp || new Date().toISOString(),
      consent_ip: req.ip || req.connection.remoteAddress || '127.0.0.1',
      consent_user_agent: req.get('User-Agent') || 'Unknown',
      metadata: {
        device_info: req.body.device_info ? JSON.parse(req.body.device_info) : {},
        orientation: req.body.orientation || 'landscape',
        mime_type: req.file.mimetype,
        additional_data: req.body.additional_data ? JSON.parse(req.body.additional_data) : {}
      }
    };

    console.log('📝 Dados da gravação preparados:', {
      filename: recordingData.filename,
      file_path: recordingData.file_path,
      size: recordingData.file_size,
      duration: recordingData.duration,
      mime_type: recordingData.mime_type
    });

    const result = await saveRecording(recordingData);

    // NÃO remover o arquivo - ele agora está no local permanente

    console.log('✅ Gravação salva com sucesso:', result.id);

    res.status(201).json({
      success: true,
      id: result.id,
      message: 'Gravação enviada com sucesso',
      data: {
        id: result.id,
        filename: result.filename,
        original_filename: result.original_filename,
        file_size: result.file_size,
        duration: result.duration,
        created_at: result.created_at
      }
    });
  } catch (error) {
    // Remover o arquivo temporário em caso de erro
    if (req.file) {
      fs.unlinkSync(req.file.path);
    }
    next(error);
  }
});

// Rota para excluir uma gravação (soft delete)
router.delete('/:id', async (req, res, next) => {
  try {
    const deleted = await deleteRecording(req.params.id);
    
    if (!deleted) {
      return res.status(404).json({ error: 'Gravação não encontrada' });
    }
    
    res.json({ message: 'Gravação excluída com sucesso' });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/recordings/bulk:
 *   post:
 *     summary: Operações em lote para gravações
 *     tags: [Recordings]
 *     description: Permite executar operações em múltiplas gravações simultaneamente
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - operation
 *               - recordingIds
 *             properties:
 *               operation:
 *                 type: string
 *                 enum: [delete, archive, unarchive, tag, export]
 *                 description: Tipo de operação a ser executada
 *               recordingIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Lista de IDs das gravações
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Tags a serem aplicadas (apenas para operação 'tag')
 *               exportFormat:
 *                 type: string
 *                 enum: [zip, individual]
 *                 description: Formato de exportação (apenas para operação 'export')
 *     responses:
 *       200:
 *         description: Operação executada com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 processed:
 *                   type: integer
 *                   description: Número de itens processados
 *                 failed:
 *                   type: integer
 *                   description: Número de itens que falharam
 *                 results:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       status:
 *                         type: string
 *                         enum: [success, error]
 *                       message:
 *                         type: string
 */
router.post('/bulk', [
  body('operation').isIn(['delete', 'archive', 'unarchive', 'tag', 'export']).withMessage('Operação inválida'),
  body('recordingIds').isArray({ min: 1 }).withMessage('Lista de IDs é obrigatória'),
  body('recordingIds.*').isUUID().withMessage('ID inválido')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Dados inválidos',
        errors: errors.array()
      });
    }

    const { operation, recordingIds, tags = [], exportFormat = 'zip' } = req.body;
    const results = [];
    let processed = 0;
    let failed = 0;

    for (const recordingId of recordingIds) {
      try {
        let result = { id: recordingId, status: 'success', message: 'Processado com sucesso' };

        switch (operation) {
          case 'delete':
            await deleteRecording(recordingId);
            result.message = 'Gravação excluída com sucesso';
            break;

          case 'archive':
            await updateRecordingStatus(recordingId, { archived: true });
            result.message = 'Gravação arquivada com sucesso';
            break;

          case 'unarchive':
            await updateRecordingStatus(recordingId, { archived: false });
            result.message = 'Gravação desarquivada com sucesso';
            break;

          case 'tag':
            await updateRecordingTags(recordingId, tags);
            result.message = `Tags aplicadas: ${tags.join(', ')}`;
            break;

          case 'export':
            // Para export, apenas preparamos os dados - o download será feito em outro endpoint
            result.message = 'Preparado para exportação';
            result.exportFormat = exportFormat;
            break;

          default:
            throw new Error('Operação não implementada');
        }

        processed++;
        results.push(result);
      } catch (error) {
        failed++;
        results.push({
          id: recordingId,
          status: 'error',
          message: error.message
        });
      }
    }

    res.json({
      success: true,
      message: `Operação ${operation} executada em ${processed} item(s). ${failed} falharam.`,
      processed,
      failed,
      results
    });
  } catch (error) {
    console.error('Erro em operação bulk:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/recordings/export:
 *   post:
 *     summary: Exportar gravações
 *     tags: [Recordings]
 *     description: Prepara um arquivo de download com as gravações selecionadas
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - recordingIds
 *             properties:
 *               recordingIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Lista de IDs das gravações para exportar
 *               format:
 *                 type: string
 *                 enum: [zip, individual]
 *                 default: zip
 *                 description: Formato de exportação
 *               includeMetadata:
 *                 type: boolean
 *                 default: true
 *                 description: Incluir metadados nos arquivos exportados
 *     responses:
 *       200:
 *         description: Arquivo preparado para download
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 downloadUrl:
 *                   type: string
 *                   description: URL para download do arquivo
 *                 filename:
 *                   type: string
 *                   description: Nome do arquivo gerado
 *                 size:
 *                   type: integer
 *                   description: Tamanho do arquivo em bytes
 *                 expiresAt:
 *                   type: string
 *                   format: date-time
 *                   description: Data de expiração do link
 */
router.post('/export', [
  body('recordingIds').isArray({ min: 1 }).withMessage('Lista de IDs é obrigatória'),
  body('recordingIds.*').isUUID().withMessage('ID inválido'),
  body('format').optional().isIn(['zip', 'individual']).withMessage('Formato inválido')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Dados inválidos',
        errors: errors.array()
      });
    }

    const { recordingIds, format = 'zip', includeMetadata = true } = req.body;
    
    // Verificar se todas as gravações existem
    const recordings = [];
    for (const id of recordingIds) {
      const recording = await getRecordingById(id);
      if (!recording) {
        return res.status(404).json({
          success: false,
          message: `Gravação não encontrada: ${id}`
        });
      }
      recordings.push(recording);
    }

    // Gerar nome único para o arquivo de exportação
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const exportId = require('crypto').randomUUID();
    const filename = format === 'zip' 
      ? `cartorio-export-${timestamp}.zip`
      : `cartorio-export-${timestamp}`;

    // Em um sistema real, aqui você criaria o arquivo ZIP ou prepararia os arquivos
    // Por now, vamos simular o processo
    const exportData = {
      id: exportId,
      filename,
      recordings: recordings.map(r => ({
        id: r.id,
        filename: r.filename,
        metadata: includeMetadata ? r : { id: r.id, filename: r.filename }
      })),
      format,
      createdAt: new Date().toISOString(),
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 horas
    };

    // Salvar informações da exportação (em um sistema real, você salvaria no banco)
    const exportsDir = path.join(__dirname, '../../storage/exports');
    if (!fs.existsSync(exportsDir)) {
      fs.mkdirSync(exportsDir, { recursive: true });
    }

    fs.writeFileSync(
      path.join(exportsDir, `${exportId}.json`),
      JSON.stringify(exportData, null, 2)
    );

    res.json({
      success: true,
      message: 'Exportação preparada com sucesso',
      downloadUrl: `/api/recordings/download/export/${exportId}`,
      filename,
      size: recordings.reduce((total, r) => total + (r.file_size || 0), 0),
      expiresAt: exportData.expiresAt,
      recordingsCount: recordings.length
    });
  } catch (error) {
    console.error('Erro ao preparar exportação:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/recordings/search:
 *   post:
 *     summary: Busca avançada de gravações
 *     tags: [Recordings]
 *     description: Realiza busca avançada com múltiplos filtros
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               searchTerm:
 *                 type: string
 *                 description: Termo de busca geral
 *               dateFrom:
 *                 type: string
 *                 format: date
 *                 description: Data inicial
 *               dateTo:
 *                 type: string
 *                 format: date
 *                 description: Data final
 *               users:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Filtrar por usuários
 *               types:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Filtrar por tipos de documento
 *               status:
 *                 type: array
 *                 items:
 *                   type: string
 *                   enum: [completed, processing, failed, pending]
 *                 description: Filtrar por status
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Filtrar por tags
 *               sortBy:
 *                 type: string
 *                 enum: [date, name, user, duration, size]
 *                 default: date
 *                 description: Campo para ordenação
 *               sortOrder:
 *                 type: string
 *                 enum: [asc, desc]
 *                 default: desc
 *                 description: Ordem da classificação
 *               page:
 *                 type: integer
 *                 minimum: 1
 *                 default: 1
 *                 description: Página
 *               limit:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 100
 *                 default: 20
 *                 description: Itens por página
 *     responses:
 *       200:
 *         description: Resultados da busca
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Recording'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     total:
 *                       type: integer
 *                     pages:
 *                       type: integer
 *                 filters:
 *                   type: object
 *                   description: Filtros aplicados
 */
router.post('/search', async (req, res) => {
  try {
    const {
      searchTerm = '',
      dateFrom,
      dateTo,
      users = [],
      types = [],
      status = [],
      tags = [],
      sortBy = 'date',
      sortOrder = 'desc',
      page = 1,
      limit = 20
    } = req.body;

    // Construir consulta SQL dinâmica baseada nos filtros
    let sql = `
      SELECT r.*, 
             COUNT(*) OVER() as total_count
      FROM recordings r 
      WHERE 1=1
    `;
    const params = [];
    let paramIndex = 1;

    // Filtro de busca geral
    if (searchTerm) {
      sql += ` AND (r.filename LIKE ? OR r.user_name LIKE ? OR r.purpose LIKE ?)`;
      const searchPattern = `%${searchTerm}%`;
      params.push(searchPattern, searchPattern, searchPattern);
      paramIndex += 3;
    }

    // Filtro de data
    if (dateFrom) {
      sql += ` AND r.created_at >= ?`;
      params.push(new Date(dateFrom).toISOString());
      paramIndex++;
    }

    if (dateTo) {
      sql += ` AND r.created_at <= ?`;
      params.push(new Date(dateTo).toISOString());
      paramIndex++;
    }

    // Filtro de usuários
    if (users.length > 0) {
      const placeholders = users.map(() => '?').join(',');
      sql += ` AND r.user_name IN (${placeholders})`;
      params.push(...users);
      paramIndex += users.length;
    }

    // Filtro de status
    if (status.length > 0) {
      const placeholders = status.map(() => '?').join(',');
      sql += ` AND r.status IN (${placeholders})`;
      params.push(...status);
      paramIndex += status.length;
    }

    // Ordenação
    const validSortColumns = {
      date: 'r.created_at',
      name: 'r.filename',
      user: 'r.user_name',
      duration: 'r.duration',
      size: 'r.file_size'
    };

    const sortColumn = validSortColumns[sortBy] || 'r.created_at';
    const order = sortOrder === 'asc' ? 'ASC' : 'DESC';
    sql += ` ORDER BY ${sortColumn} ${order}`;

    // Paginação
    const offset = (page - 1) * limit;
    sql += ` LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    const results = await new Promise((resolve, reject) => {
      const db = require('../database').getDatabase();
      db.all(sql, params, (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    const total = results.length > 0 ? results[0].total_count : 0;
    const data = results.map(row => {
      const { total_count, ...recording } = row;
      return recording;
    });

    res.json({
      success: true,
      data,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: parseInt(total),
        pages: Math.ceil(total / limit)
      },
      filters: {
        searchTerm,
        dateFrom,
        dateTo,
        users,
        types,
        status,
        tags,
        sortBy,
        sortOrder
      }
    });
  } catch (error) {
    console.error('Erro na busca avançada:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
});

// Funções auxiliares (implementar no database.js)
async function updateRecordingStatus(recordingId, updates) {
  return new Promise((resolve, reject) => {
    const db = require('../database').getDatabase();
    const fields = Object.keys(updates).map(key => `${key} = ?`).join(', ');
    const values = Object.values(updates);
    
    db.run(
      `UPDATE recordings SET ${fields}, updated_at = datetime('now') WHERE id = ?`,
      [...values, recordingId],
      function(err) {
        if (err) reject(err);
        else resolve(this.changes);
      }
    );
  });
}

async function updateRecordingTags(recordingId, tags) {
  return new Promise((resolve, reject) => {
    const db = require('../database').getDatabase();
    const tagsJson = JSON.stringify(tags);
    
    db.run(
      `UPDATE recordings SET tags = ?, updated_at = datetime('now') WHERE id = ?`,
      [tagsJson, recordingId],
      function(err) {
        if (err) reject(err);
        else resolve(this.changes);
      }
    );
  });
}

module.exports = router;
