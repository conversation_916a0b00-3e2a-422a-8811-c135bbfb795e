<!DOCTYPE html>
<html lang="pt-br">
<head>
  <meta charset="UTF-8">
  <title>ProvaSegura - Slide 9: Investimento</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');
    
    body {
      margin: 0;
      padding: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      font-family: 'Inter', 'Segoe UI', Arial, sans-serif;
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }
      .slide {
      width: 90vw;
      max-width: 1280px;
      height: 80vh;
      max-height: 720px;
      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
      box-shadow: 0 20px 60px rgba(0,0,0,0.15), 0 8px 32px rgba(0,0,0,0.08);
      border-radius: 24px;
      display: flex;
      flex-direction: row;
      overflow: hidden;
      position: relative;
      border: 1px solid rgba(255,255,255,0.2);
    }
    
    .slide::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #f59e0b 0%, #d97706 50%, #b45309 100%);
      border-radius: 24px 24px 0 0;
    }
      .slide-content {
      padding: 40px 60px;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      position: relative;
      z-index: 2;
    }
      .slide-title {
      font-size: 2.5rem;
      font-weight: 800;
      background: linear-gradient(135deg, #b45309 0%, #f59e0b 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-bottom: 24px;
      line-height: 1.1;
      letter-spacing: -0.02em;
    }
    
    .slide-text {
      font-size: 1.2rem;
      color: #334155;
      line-height: 1.7;
      font-weight: 400;
    }
    
    .slide-text ul {
      padding-left: 0;
      list-style: none;
    }
      .slide-text li {
      position: relative;
      padding: 16px 16px 16px 50px;
      margin-bottom: 12px;
      background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
      border-left: 4px solid #f59e0b;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(245, 158, 11, 0.1);
    }
      .slide-text li::before {
      content: '💸';
      position: absolute;
      left: 16px;
      top: 16px;
      font-size: 1.5rem;
    }
    
    .slide-text strong {
      color: #92400e;
      font-weight: 700;
    }
      .highlight-investment {
      background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
      color: white;
      padding: 20px;
      border-radius: 16px;
      margin: 20px 0;
      text-align: center;
      font-size: 1.2rem;
      font-weight: 700;
      box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
    }
    
    .returns-section {
      background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
      padding: 20px;
      border-radius: 16px;
      margin-top: 20px;
      border: 2px solid rgba(16, 185, 129, 0.2);
    }
    
    .slide-img {
      flex: 0 0 400px;
      background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      overflow: hidden;
      order: -1;
    }
    
    .slide-img::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 70% 30%, rgba(245, 158, 11, 0.1) 0%, transparent 50%);
    }
    
    .slide-img svg {
      filter: drop-shadow(0 15px 35px rgba(245, 158, 11, 0.2));
      z-index: 1;
      position: relative;
    }
    
    .slide-number {
      position: absolute;
      top: 30px;
      right: 40px;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-weight: 700;
      font-size: 1.3rem;
      box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
    }    @media print {
      body { background: #fff; }
      .slide { box-shadow: none; border: 1px solid #e2e8f0; }
    }

    @media (max-width: 768px) {
      .slide {
        width: 95vw;
        height: 90vh;
        flex-direction: column;
        padding: 30px 20px;
      }
      
      .slide-content {
        padding: 20px;
        order: 2;
      }
      
      .slide-title {
        font-size: 2rem;
        margin-bottom: 20px;
      }
      
      .slide-text {
        font-size: 1rem;
      }
      
      .slide-text li {
        padding: 12px 12px 12px 40px;
        margin-bottom: 10px;
      }
      
      .slide-text li::before {
        font-size: 1.3rem;
        left: 12px;
        top: 12px;
      }
      
      .highlight-investment {
        font-size: 1.1rem;
        padding: 16px;
        margin: 16px 0;
      }
      
      .returns-section {
        padding: 16px;
        margin-top: 16px;
      }
      
      .slide-img {
        flex: 0 0 200px;
        order: 1;
      }
      
      .slide-img svg {
        width: 300px;
        height: 200px;
      }
    }

    .navigation {
      position: fixed;
      bottom: 30px;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      gap: 15px;
      z-index: 1000;
    }

    .nav-button {
      background: rgba(255, 255, 255, 0.9);
      border: none;
      padding: 12px 24px;
      border-radius: 25px;
      color: #1e293b;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      text-decoration: none;
      display: inline-block;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .nav-button:hover {
      background: white;
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }
  </style>
</head>
<body>

<div class="slide">
  <div class="slide-number">9</div>
  <div class="slide-content">
    <div class="slide-title">Junte-se à Revolução da Comprovação Digital</div>
    
    <div class="highlight-investment">
      Investimento necessário: R$ 2 milhões
    </div>
    
    <div class="slide-text">
      <ul>
        <li><strong>Desenvolvimento (R$ 500.000):</strong> Aprimorar app e integrar blockchain.</li>
        <li><strong>Conformidade (R$ 300.000):</strong> LGPD e homologação com Banco Central/CNJ.</li>
        <li><strong>Marketing e parcerias (R$ 1,2 milhão):</strong> Pilotos com bancos, RH e varejo.</li>
      </ul>
    </div>
    
    <div class="returns-section">
      <strong style="color: #065f46; font-size: 1.3rem;">Retorno esperado:</strong><br>
      <strong>Ano 1:</strong> R$ 5-10 milhões em receita (5% do mercado de RH e bens caros).<br>
      <strong>Ano 3:</strong> R$ 50-100 milhões, capturando 10% do mercado brasileiro.<br><br>
      <strong>Saída estratégica:</strong> Aquisição por bancos, empresas de tecnologia ou legaltechs em 3-5 anos.
    </div>
  </div>
  <div class="slide-img">
    <!-- Gráfico de projeção melhorado -->
    <svg width="380" height="300" viewBox="0 0 380 300">
      <!-- Background do gráfico -->
      <rect x="30" y="30" width="320" height="240" rx="16" fill="#fff" stroke="#e2e8f0" stroke-width="2"/>
      
      <!-- Título do gráfico -->
      <text x="190" y="55" text-anchor="middle" font-size="16" font-weight="bold" fill="#1e293b">Projeção de Receita (R$ Milhões)</text>
      
      <!-- Eixos -->
      <line x1="60" y1="250" x2="320" y2="250" stroke="#94a3b8" stroke-width="2"/>
      <line x1="60" y1="250" x2="60" y2="80" stroke="#94a3b8" stroke-width="2"/>
      
      <!-- Grid lines -->
      <line x1="60" y1="220" x2="320" y2="220" stroke="#e2e8f0" stroke-width="1"/>
      <line x1="60" y1="190" x2="320" y2="190" stroke="#e2e8f0" stroke-width="1"/>
      <line x1="60" y1="160" x2="320" y2="160" stroke="#e2e8f0" stroke-width="1"/>
      <line x1="60" y1="130" x2="320" y2="130" stroke="#e2e8f0" stroke-width="1"/>
      <line x1="60" y1="100" x2="320" y2="100" stroke="#e2e8f0" stroke-width="1"/>
      
      <!-- Linha de crescimento -->
      <polyline points="60,240 120,210 180,160 240,110 300,85" 
                fill="none" stroke="url(#gradient3)" stroke-width="6"/>
      
      <!-- Área sob a curva -->
      <path d="M60,240 L120,210 L180,160 L240,110 L300,85 L300,250 L60,250 Z" 
            fill="url(#gradient3)" opacity="0.2"/>
      
      <!-- Pontos de dados -->
      <circle cx="60" cy="240" r="6" fill="#f59e0b"/>
      <circle cx="120" cy="210" r="6" fill="#f59e0b"/>
      <circle cx="180" cy="160" r="6" fill="#f59e0b"/>
      <circle cx="240" cy="110" r="6" fill="#f59e0b"/>
      <circle cx="300" cy="85" r="8" fill="#10b981"/>
      
      <!-- Labels dos anos -->
      <text x="60" y="270" text-anchor="middle" font-size="12" fill="#64748b">2025</text>
      <text x="120" y="270" text-anchor="middle" font-size="12" fill="#64748b">2026</text>
      <text x="180" y="270" text-anchor="middle" font-size="12" fill="#64748b">2027</text>
      <text x="240" y="270" text-anchor="middle" font-size="12" fill="#64748b">2028</text>
      <text x="300" y="270" text-anchor="middle" font-size="12" fill="#64748b">2029</text>
      
      <!-- Valores Y -->
      <text x="50" y="255" text-anchor="end" font-size="10" fill="#64748b">0</text>
      <text x="50" y="225" text-anchor="end" font-size="10" fill="#64748b">25M</text>
      <text x="50" y="195" text-anchor="end" font-size="10" fill="#64748b">50M</text>
      <text x="50" y="165" text-anchor="end" font-size="10" fill="#64748b">75M</text>
      <text x="50" y="135" text-anchor="end" font-size="10" fill="#64748b">100M</text>
      <text x="50" y="105" text-anchor="end" font-size="10" fill="#64748b">125M</text>
      
      <!-- Valores específicos -->
      <text x="60" y="235" text-anchor="middle" font-size="10" font-weight="bold" fill="#1e293b">5M</text>
      <text x="120" y="205" text-anchor="middle" font-size="10" font-weight="bold" fill="#1e293b">20M</text>
      <text x="180" y="155" text-anchor="middle" font-size="10" font-weight="bold" fill="#1e293b">60M</text>
      <text x="240" y="105" text-anchor="middle" font-size="10" font-weight="bold" fill="#1e293b">100M</text>
      <text x="300" y="80" text-anchor="middle" font-size="10" font-weight="bold" fill="#059669">150M</text>
      
      <!-- ROI indicator -->
      <g transform="translate(330, 120)">
        <circle r="25" fill="#10b981"/>
        <text x="0" y="-5" text-anchor="middle" font-size="12" font-weight="bold" fill="#fff">ROI</text>
        <text x="0" y="8" text-anchor="middle" font-size="12" font-weight="bold" fill="#fff">75x</text>
      </g>
      
      <!-- Definições de gradiente -->
      <defs>
        <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#10b981;stop-opacity:1" />
        </linearGradient>
      </defs>
    </svg>  </div>
</div>

<div class="navigation">
  <a href="slide-08-tracao.html" class="nav-button">← Anterior</a>
  <a href="index.html" class="nav-button">Índice</a>
  <a href="slide-10-equipe.html" class="nav-button">Próximo →</a>
</div>

<script>
document.addEventListener('keydown', function(event) {
  if (event.key === 'ArrowLeft') {
    window.location.href = 'slide-08-tracao.html';
  } else if (event.key === 'ArrowRight') {
    window.location.href = 'slide-10-equipe.html';
  } else if (event.key === 'Escape') {
    window.location.href = 'index.html';
  }
});
</script>

</body>
</html>
