# GUIA DE VALIDAÇÃO - SISTEMA CARTORIAL

## 🚀 INICIALIZAÇÃO DO SISTEMA

### 1. Parar containers existentes
```bash
docker-compose down
```

### 2. Limpar volumes (se necessário)
```bash
docker-compose down -v
```

### 3. Const<PERSON>ir e iniciar todos os serviços
```bash
docker-compose up --build -d
```

### 4. Verificar status dos serviços
```bash
docker-compose ps
```

### 5. Verificar logs dos serviços
```bash
# Logs do backend
docker-compose logs backend --tail=50

# Logs do AI service
docker-compose logs ai-service --tail=50

# Logs do signature service
docker-compose logs signature-service --tail=50

# Logs do web-app
docker-compose logs web-app --tail=50
```

## 🔍 ENDPOINTS PARA VALIDAÇÃO

### Backend Principal (http://localhost:3001)
- ✅ Health Check: `GET /health`
- ✅ Documentação: `GET /api-docs`
- ✅ Gravações: `GET /api/recordings`
- ✅ Relatórios: `GET /api/reports`
- ✅ Analytics: `GET /api/analytics/dashboard`

### AI Service (http://localhost:3003)
- ✅ Health Check: `GET /health`
- ✅ OCR: `POST /api/ai/ocr/process`
- ✅ Transcrição: `POST /api/ai/transcription/process`
- ✅ Compliance: `POST /api/ai/compliance/analyze`

### Signature Service (http://localhost:3004)
- ✅ Health Check: `GET /health`
- ✅ Timestamp: `POST /api/timestamp/create`
- ✅ Assinatura: `POST /api/signature/sign`
- ✅ Certificados: `GET /api/certificate/list`

### Web App (http://localhost)
- ✅ Frontend React
- ✅ Gravação de tela
- ✅ Dashboard de analytics
- ✅ Gerenciamento de gravações

## 🧪 TESTES DE VALIDAÇÃO

### 1. Teste de Gravação
1. Acesse http://localhost
2. Clique em "Iniciar Gravação"
3. Preencha os dados de consentimento LGPD
4. Grave por alguns segundos
5. Pare a gravação
6. Verifique se foi salva no sistema

### 2. Teste de OCR
```bash
curl -X POST \
  http://localhost:3003/api/ai/ocr/process \
  -H "Content-Type: multipart/form-data" \
  -F "image=@/path/to/test/image.jpg"
```

### 3. Teste de Timestamp
```bash
curl -X POST \
  http://localhost:3004/api/timestamp/create \
  -H "Content-Type: application/json" \
  -d '{"documentHash": "sha256_hash_here"}'
```

### 4. Teste de Analytics
```bash
curl -X GET http://localhost:3001/api/analytics/dashboard
```

## 📊 MÉTRICAS DE VALIDAÇÃO

### ✅ O que deve funcionar:
- [x] Todos os containers inicializam sem erro
- [x] Health checks passam
- [x] Frontend carrega corretamente
- [x] Gravação de tela funciona
- [x] Consentimento LGPD é coletado
- [x] Dados são salvos no PostgreSQL
- [x] Analytics mostram dados corretos

### ⚠️ O que pode precisar de configuração:
- [ ] OpenAI API (se não configurada, usa mock)
- [ ] TSA real (usa simulada por padrão)
- [ ] Certificados digitais (usa simulados)

## 🔧 TROUBLESHOOTING

### Erro: "Método getRecordingsByPeriod não está definido"
- ✅ **RESOLVIDO**: Método foi adicionado ao PostgreSQL adapter

### Erro: "Rota não encontrada"
- ✅ **RESOLVIDO**: Nginx configurado corretamente

### Erro: "Conexão com banco de dados"
- Verificar se PostgreSQL está funcionando: `docker-compose logs postgres`
- Verificar se as credenciais estão corretas no .env

### Erro: "Erro ao processar AI"
- Verificar se OPENAI_API_KEY está configurada
- Verificar logs do ai-service

## 🎯 PRÓXIMOS PASSOS

### Para Produção:
1. Configurar OPENAI_API_KEY real
2. Configurar TSA real
3. Gerar certificados digitais reais
4. Configurar SSL/TLS
5. Configurar backup automático
6. Implementar monitoramento

### Para Desenvolvimento:
1. Adicionar mais testes automatizados
2. Implementar CI/CD
3. Adicionar logging mais detalhado
4. Implementar rate limiting
5. Adicionar validação de entrada mais rigorosa
