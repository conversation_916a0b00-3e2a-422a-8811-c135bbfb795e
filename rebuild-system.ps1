#!/usr/bin/env pwsh

# Script para rebuild completo do sistema CartorioTech
# Corrige problemas de Web3, Analytics e reconstrói tudo

Write-Host "🚀 Iniciando rebuild completo do CartorioTech..." -ForegroundColor Green

# 1. Parar todos os containers
Write-Host "📦 Parando containers..." -ForegroundColor Yellow
docker-compose down

# 2. Limpar imagens antigas
Write-Host "🧹 Limpando imagens antigas..." -ForegroundColor Yellow
docker-compose rm -f
docker image prune -f

# 3. Rebuild do backend
Write-Host "🔧 Rebuilding backend..." -ForegroundColor Cyan
docker-compose build --no-cache backend

# 4. Rebuild do frontend
Write-Host "🎨 Rebuilding frontend..." -ForegroundColor Cyan
docker-compose build --no-cache web-app

# 5. Rebuild do AI service
Write-Host "🤖 Rebuilding AI service..." -ForegroundColor Cyan
docker-compose build --no-cache ai-service

# 6. Iniciar todos os serviços
Write-Host "🚀 Iniciando todos os serviços..." -ForegroundColor Green
docker-compose up -d

# 7. Aguardar inicialização
Write-Host "⏳ Aguardando inicialização dos serviços..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# 8. Verificar status dos serviços
Write-Host "📊 Verificando status dos serviços..." -ForegroundColor Cyan
docker-compose ps

# 9. Mostrar logs do backend para verificar se Web3 está funcionando
Write-Host "📋 Logs do backend (últimas 20 linhas):" -ForegroundColor Cyan
docker-compose logs backend --tail=20

# 10. Mostrar logs do frontend
Write-Host "📋 Logs do frontend (últimas 10 linhas):" -ForegroundColor Cyan
docker-compose logs web-app --tail=10

# 11. Testar endpoints críticos
Write-Host "🧪 Testando endpoints críticos..." -ForegroundColor Magenta

# Aguardar um pouco mais para garantir que os serviços estão prontos
Start-Sleep -Seconds 5

# Testar health check
try {
    $healthResponse = Invoke-RestMethod -Uri "http://localhost:3001/health" -Method GET -TimeoutSec 10
    Write-Host "✅ Health check: OK" -ForegroundColor Green
} catch {
    Write-Host "❌ Health check: FALHOU - $($_.Exception.Message)" -ForegroundColor Red
}

# Testar analytics dashboard
try {
    $analyticsResponse = Invoke-RestMethod -Uri "http://localhost:3001/api/analytics/dashboard" -Method GET -TimeoutSec 10
    if ($analyticsResponse.success) {
        Write-Host "✅ Analytics dashboard: OK" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Analytics dashboard: Retornou dados de fallback" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Analytics dashboard: FALHOU - $($_.Exception.Message)" -ForegroundColor Red
}

# Testar frontend
try {
    $frontendResponse = Invoke-WebRequest -Uri "http://localhost:3000" -Method GET -TimeoutSec 10
    if ($frontendResponse.StatusCode -eq 200) {
        Write-Host "✅ Frontend: OK" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Frontend: FALHOU - $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "🎉 Rebuild completo finalizado!" -ForegroundColor Green
Write-Host ""
Write-Host "📍 URLs disponíveis:" -ForegroundColor Cyan
Write-Host "   Frontend: http://localhost:3000" -ForegroundColor White
Write-Host "   Backend API: http://localhost:3001" -ForegroundColor White
Write-Host "   API Docs: http://localhost:3001/api-docs" -ForegroundColor White
Write-Host "   AI Service: http://localhost:3002" -ForegroundColor White
Write-Host ""
Write-Host "🔧 Para monitorar logs em tempo real:" -ForegroundColor Cyan
Write-Host "   docker-compose logs -f backend" -ForegroundColor White
Write-Host "   docker-compose logs -f web-app" -ForegroundColor White
Write-Host "   docker-compose logs -f ai-service" -ForegroundColor White
Write-Host ""

# Verificar se há erros críticos nos logs
$backendLogs = docker-compose logs backend --tail=50 2>&1
if ($backendLogs -match "Error|TypeError|Cannot|Failed") {
    Write-Host "⚠️ ATENÇÃO: Detectados possíveis erros no backend!" -ForegroundColor Red
    Write-Host "Execute: docker-compose logs backend" -ForegroundColor Yellow
}

$webappLogs = docker-compose logs web-app --tail=20 2>&1
if ($webappLogs -match "Error|Failed|Cannot") {
    Write-Host "⚠️ ATENÇÃO: Detectados possíveis erros no frontend!" -ForegroundColor Red
    Write-Host "Execute: docker-compose logs web-app" -ForegroundColor Yellow
}

Write-Host "✨ Sistema CartorioTech pronto para uso!" -ForegroundColor Green
