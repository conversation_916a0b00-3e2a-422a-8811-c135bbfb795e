import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardA<PERSON>,
  Typo<PERSON>,
  Button,
  TextField,
  Box,
  Alert,
  Stepper,
  Step,
  StepLabel,
  Grid,
  FormControlLabel,
  Switch,
  Chip,
  LinearProgress,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider
} from '@mui/material';
import {
  LocationOn,
  Schedule,
  Assessment,
  FileDownload,
  Security,
  CheckCircle,
  Warning,
  Info
} from '@mui/icons-material';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { ptBR } from 'date-fns/locale';
import LGPDConsentModal from './LGPDConsentModal';
import ConsentModal from './ConsentModal';
import { 
  AnimatedSuccess, 
  AnimatedLoading, 
  AnimatedValidation, 
  AnimatedCounter,
  AnimatedButton,
  AnimatedCard,
  FadeInSection
} from './AnimatedIcons';

interface ValidationFormProps {
  onValidationComplete: (result: any) => void;
  onOpenMobileOAuth?: () => void;
}

const ValidationForm: React.FC<ValidationFormProps> = ({ onValidationComplete, onOpenMobileOAuth }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [showConsentModal, setShowConsentModal] = useState(false);
  const [consentGiven, setConsentGiven] = useState(false);
  
  // Form data
  const [formData, setFormData] = useState({
    homeAddress: '',
    coordinates: { latitude: 0, longitude: 0 },
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 dias atrás
    endDate: new Date(),
    useDemo: false
  });

  const [validationResult, setValidationResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const steps = [
    'Dados Básicos',
    'Período de Análise',
    'Validação',
    'Resultado'
  ];

  const handleAddressChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const address = event.target.value;
    setFormData(prev => ({ ...prev, homeAddress: address }));
    
    // Simulação de geocodificação (em produção, usar Google Geocoding API)
    if (address.length > 10) {
      // Coordenadas fictícias para demonstração
      const mockCoordinates = {
        latitude: -23.5505 + (Math.random() - 0.5) * 0.1,
        longitude: -46.6333 + (Math.random() - 0.5) * 0.1
      };
      setFormData(prev => ({ ...prev, coordinates: mockCoordinates }));
    }
  };

  const handleNext = () => {
    if (currentStep === 0) {
      // Verificar se precisa de consentimento
      if (!consentGiven) {
        setShowConsentModal(true);
        return;
      }
    }
    
    if (currentStep < steps.length - 1) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const handleConsentAccept = () => {
    setConsentGiven(true);
    setShowConsentModal(false);
    setCurrentStep(1);
  };

  const handleValidation = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/residence/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        throw new Error('Erro na validação');
      }

      const result = await response.json();
      setValidationResult(result);
      setCurrentStep(3);
      onValidationComplete(result);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Erro desconhecido');
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadCertificate = async () => {
    if (!validationResult) return;

    try {
      const response = await fetch('/api/residence/certificate/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          validationId: validationResult.validationId,
          validationData: validationResult
        })
      });

      if (!response.ok) {
        throw new Error('Erro ao gerar certificado');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `certificado_residencia_${validationResult.validationId}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Erro ao baixar certificado:', error);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Informações Básicas
            </Typography>
            <TextField
              fullWidth
              label="Endereço de Residência"
              value={formData.homeAddress}
              onChange={handleAddressChange}
              placeholder="Ex: Rua das Flores, 123, Bairro, Cidade - Estado"
              margin="normal"
              required
              helperText="Digite o endereço completo que deseja validar"
            />
            
            {formData.coordinates.latitude !== 0 && (
              <Alert severity="success" sx={{ mt: 2 }}>
                <Typography variant="body2">
                  Coordenadas encontradas: {formData.coordinates.latitude.toFixed(6)}, {formData.coordinates.longitude.toFixed(6)}
                </Typography>
              </Alert>
            )}

            <FormControlLabel
              control={
                <Switch
                  checked={formData.useDemo}
                  onChange={(e) => setFormData(prev => ({ ...prev, useDemo: e.target.checked }))}
                />
              }
              label="Usar dados de demonstração"
              sx={{ mt: 2 }}
            />
            
            {formData.useDemo && (
              <Alert severity="info" sx={{ mt: 1 }}>
                Modo demonstração ativado. Dados simulados serão usados para teste.
              </Alert>
            )}
          </Box>
        );

      case 1:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Período de Análise
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Selecione o período para análise dos dados de localização
            </Typography>
            
            <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ptBR}>
              <Grid container spacing={2} sx={{ mt: 1 }}>
                <Grid item xs={12} md={6}>
                  <DatePicker
                    label="Data de Início"
                    value={formData.startDate}
                    onChange={(date) => setFormData(prev => ({ ...prev, startDate: date || new Date() }))}
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        variant: 'outlined'
                      }
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <DatePicker
                    label="Data de Fim"
                    value={formData.endDate}
                    onChange={(date) => setFormData(prev => ({ ...prev, endDate: date || new Date() }))}
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        variant: 'outlined'
                      }
                    }}
                  />
                </Grid>
              </Grid>
            </LocalizationProvider>

            <Paper elevation={1} sx={{ p: 2, mt: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Critérios de Validação:
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemIcon><Schedule fontSize="small" /></ListItemIcon>
                  <ListItemText primary="Período noturno (18h às 6h)" />
                </ListItem>
                <ListItem>
                  <ListItemIcon><LocationOn fontSize="small" /></ListItemIcon>
                  <ListItemText primary="Raio de 100 metros do endereço" />
                </ListItem>
                <ListItem>
                  <ListItemIcon><Assessment fontSize="small" /></ListItemIcon>
                  <ListItemText primary="Mínimo 15 dias consecutivos" />
                </ListItem>
              </List>
            </Paper>
          </Box>
        );

      case 2:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Validação em Progresso
            </Typography>
            
            {loading && (
              <Box>
                <LinearProgress sx={{ mb: 2 }} />
                <Typography variant="body2" align="center">
                  Processando dados de localização...
                </Typography>
              </Box>
            )}
            
            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            <Paper elevation={1} sx={{ p: 2, mt: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Resumo da Validação:
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText 
                    primary="Endereço" 
                    secondary={formData.homeAddress}
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Período" 
                    secondary={`${formData.startDate.toLocaleDateString()} - ${formData.endDate.toLocaleDateString()}`}
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Modo" 
                    secondary={formData.useDemo ? 'Demonstração' : 'Dados Reais'}
                  />
                </ListItem>
              </List>
            </Paper>

            <Box sx={{ mt: 3, textAlign: 'center' }}>
              <Alert severity="info" sx={{ mb: 2 }}>
                <Typography variant="body2">
                  <strong>Escolha como validar:</strong> Use OAuth para validação automática ou faça upload manual dos dados.
                </Typography>
              </Alert>

              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, alignItems: 'center' }}>
                <Button
                  variant="contained"
                  onClick={onOpenMobileOAuth}
                  size="large"
                  startIcon={<Google />}
                  sx={{
                    minWidth: 250,
                    background: 'linear-gradient(135deg, #4285f4 0%, #34a853 100%)',
                    '&:hover': {
                      background: 'linear-gradient(135deg, #3367d6 0%, #2d8f47 100%)',
                    }
                  }}
                >
                  Autorizar com Google Maps
                </Button>

                <Typography variant="body2" color="text.secondary">
                  ou
                </Typography>

                <Button
                  variant="outlined"
                  onClick={handleValidation}
                  disabled={loading}
                  size="large"
                  startIcon={<Assessment />}
                  sx={{ minWidth: 250 }}
                >
                  {loading ? 'Validando...' : 'Validação Manual'}
                </Button>
              </Box>
            </Box>
          </Box>
        );

      case 3:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Resultado da Validação
            </Typography>
            
            {validationResult && (
              <Box>
                <Alert 
                  severity={validationResult.validation.isValid ? 'success' : 'warning'}
                  sx={{ mb: 2 }}
                >
                  <Typography variant="h6">
                    {validationResult.validation.summary.status}
                  </Typography>
                  <Typography variant="body2">
                    {validationResult.validation.summary.reason}
                  </Typography>
                </Alert>

                <Grid container spacing={2} sx={{ mb: 2 }}>
                  <Grid item xs={6} md={3}>
                    <Card>
                      <CardContent sx={{ textAlign: 'center' }}>
                        <Typography variant="h4" color="primary">
                          {validationResult.validation.summary.scorePercentage}%
                        </Typography>
                        <Typography variant="body2">
                          Score de Confiança
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={6} md={3}>
                    <Card>
                      <CardContent sx={{ textAlign: 'center' }}>
                        <Typography variant="h4" color="secondary">
                          {validationResult.validation.maxConsecutiveDays}
                        </Typography>
                        <Typography variant="body2">
                          Dias Consecutivos
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={6} md={3}>
                    <Card>
                      <CardContent sx={{ textAlign: 'center' }}>
                        <Typography variant="h4">
                          {validationResult.validation.validDays}
                        </Typography>
                        <Typography variant="body2">
                          Dias Válidos
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={6} md={3}>
                    <Card>
                      <CardContent sx={{ textAlign: 'center' }}>
                        <Typography variant="h4">
                          {validationResult.validation.totalDays}
                        </Typography>
                        <Typography variant="body2">
                          Total de Dias
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>

                <Alert severity="warning" sx={{ mb: 2 }}>
                  <Typography variant="body2">
                    <strong>Importante:</strong> Este certificado é preliminar e não possui validade legal. 
                    Para ter validade legal, deve ser autenticado em cartório competente.
                  </Typography>
                </Alert>

                <Box sx={{ textAlign: 'center' }}>
                  <Button
                    variant="contained"
                    onClick={handleDownloadCertificate}
                    startIcon={<FileDownload />}
                    size="large"
                  >
                    Baixar Certificado Preliminar
                  </Button>
                </Box>
              </Box>
            )}
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <Box>
      <Card>
        <CardContent>
          <Stepper activeStep={currentStep} sx={{ mb: 4 }}>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>

          {renderStepContent()}
        </CardContent>

        <CardActions sx={{ justifyContent: 'space-between', p: 2 }}>
          <Button
            onClick={handleBack}
            disabled={currentStep === 0}
          >
            Voltar
          </Button>
          
          {currentStep < steps.length - 1 && currentStep !== 2 && (
            <Button
              variant="contained"
              onClick={handleNext}
              disabled={
                (currentStep === 0 && (!formData.homeAddress || formData.coordinates.latitude === 0)) ||
                (currentStep === 1 && (!formData.startDate || !formData.endDate))
              }
            >
              Próximo
            </Button>
          )}
        </CardActions>
      </Card>

      <LGPDConsentModal
        open={showConsentModal}
        onClose={() => setShowConsentModal(false)}
        onAccept={handleConsentAccept}
      />
    </Box>
  );
};

export default ValidationForm;
