-- Migração 005: Adicionar suporte a assinatura digital e carimbo de tempo
-- Data: 2025-01-18
-- Descrição: Criar tabelas e campos para integridade de gravações com ICP-Brasil

-- Adicionar campos de integridade à tabela recordings
ALTER TABLE recordings ADD COLUMN IF NOT EXISTS file_hash VARCHAR(64);
ALTER TABLE recordings ADD COLUMN IF NOT EXISTS digital_signature TEXT;
ALTER TABLE recordings ADD COLUMN IF NOT EXISTS signature_path VARCHAR(500);
ALTER TABLE recordings ADD COLUMN IF NOT EXISTS certificate_data TEXT;
ALTER TABLE recordings ADD COLUMN IF NOT EXISTS signed_at TIMESTAMP;
ALTER TABLE recordings ADD COLUMN IF NOT EXISTS timestamp_token TEXT;
ALTER TABLE recordings ADD COLUMN IF NOT EXISTS timestamp_authority VARCHAR(255);
ALTER TABLE recordings ADD COLUMN IF NOT EXISTS timestamp_nonce VARCHAR(32);
ALTER TABLE recordings ADD COLUMN IF NOT EXISTS timestamp_at TIMESTAMP;
ALTER TABLE recordings ADD COLUMN IF NOT EXISTS integrity_verified BOOLEAN DEFAULT FALSE;
ALTER TABLE recordings ADD COLUMN IF NOT EXISTS last_verification_at TIMESTAMP;
ALTER TABLE recordings ADD COLUMN IF NOT EXISTS verification_status VARCHAR(20) DEFAULT 'PENDING';
ALTER TABLE recordings ADD COLUMN IF NOT EXISTS encrypted BOOLEAN DEFAULT FALSE;
ALTER TABLE recordings ADD COLUMN IF NOT EXISTS encrypted_path VARCHAR(500);
ALTER TABLE recordings ADD COLUMN IF NOT EXISTS encryption_key_id VARCHAR(255);
ALTER TABLE recordings ADD COLUMN IF NOT EXISTS encryption_iv VARCHAR(32);
ALTER TABLE recordings ADD COLUMN IF NOT EXISTS encryption_tag VARCHAR(32);
ALTER TABLE recordings ADD COLUMN IF NOT EXISTS encrypted_hash VARCHAR(64);
ALTER TABLE recordings ADD COLUMN IF NOT EXISTS encryption_algorithm VARCHAR(50);
ALTER TABLE recordings ADD COLUMN IF NOT EXISTS encrypted_at TIMESTAMP;
ALTER TABLE recordings ADD COLUMN IF NOT EXISTS key_rotated_at TIMESTAMP;
ALTER TABLE recordings ADD COLUMN IF NOT EXISTS last_encryption_verification TIMESTAMP;

-- Executar a migração para garantir que as colunas existam
DO $$
BEGIN
    -- Verificar se a coluna encrypted_path existe, se não, criar
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'recordings' AND column_name = 'encrypted_path') THEN
        ALTER TABLE recordings ADD COLUMN encrypted_path VARCHAR(500);
    END IF;

    -- Verificar se a coluna encrypted_hash existe, se não, criar
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'recordings' AND column_name = 'encrypted_hash') THEN
        ALTER TABLE recordings ADD COLUMN encrypted_hash VARCHAR(64);
    END IF;

    -- Verificar se a coluna file_hash existe, se não, criar
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'recordings' AND column_name = 'file_hash') THEN
        ALTER TABLE recordings ADD COLUMN file_hash VARCHAR(64);
    END IF;
END $$;

-- Tabela para armazenar certificados ICP-Brasil
CREATE TABLE IF NOT EXISTS icp_certificates (
    id SERIAL PRIMARY KEY,
    certificate_id VARCHAR(255) UNIQUE NOT NULL,
    subject_name VARCHAR(255) NOT NULL,
    issuer_name VARCHAR(255) NOT NULL,
    serial_number VARCHAR(100) NOT NULL,
    valid_from TIMESTAMP NOT NULL,
    valid_to TIMESTAMP NOT NULL,
    certificate_pem TEXT NOT NULL,
    fingerprint VARCHAR(64) NOT NULL,
    key_usage TEXT[],
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela para logs de assinatura digital
CREATE TABLE IF NOT EXISTS digital_signature_logs (
    id SERIAL PRIMARY KEY,
    recording_id VARCHAR(255) NOT NULL,
    operation VARCHAR(50) NOT NULL, -- SIGN, VERIFY, TIMESTAMP
    status VARCHAR(20) NOT NULL, -- SUCCESS, FAILED, WARNING
    details JSONB,
    certificate_id VARCHAR(255),
    timestamp_authority VARCHAR(255),
    processing_time_ms INTEGER,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Índices para performance
    FOREIGN KEY (certificate_id) REFERENCES icp_certificates(certificate_id) ON DELETE SET NULL
);

-- Tabela para carimbos de tempo
CREATE TABLE IF NOT EXISTS timestamp_records (
    id SERIAL PRIMARY KEY,
    recording_id VARCHAR(255) NOT NULL,
    hash_algorithm VARCHAR(20) DEFAULT 'sha256',
    hash_value VARCHAR(64) NOT NULL,
    timestamp_token TEXT NOT NULL,
    timestamp_authority VARCHAR(255) NOT NULL,
    nonce VARCHAR(32),
    policy_oid VARCHAR(100),
    timestamp_at TIMESTAMP NOT NULL,
    verified BOOLEAN DEFAULT FALSE,
    verification_details JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Índices para performance
    CONSTRAINT unique_recording_timestamp UNIQUE (recording_id, hash_value)
);

-- Tabela para verificações de integridade
CREATE TABLE IF NOT EXISTS integrity_verifications (
    id SERIAL PRIMARY KEY,
    recording_id VARCHAR(255) NOT NULL,
    verification_type VARCHAR(50) NOT NULL, -- SIGNATURE, TIMESTAMP, FULL
    original_hash VARCHAR(64) NOT NULL,
    current_hash VARCHAR(64) NOT NULL,
    hash_match BOOLEAN NOT NULL,
    signature_valid BOOLEAN,
    timestamp_valid BOOLEAN,
    overall_status VARCHAR(20) NOT NULL, -- VALID, INVALID, WARNING
    verification_details JSONB,
    verified_by VARCHAR(255),
    verified_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Índices para performance
    INDEX idx_integrity_verifications_recording_id (recording_id),
    INDEX idx_integrity_verifications_status (overall_status),
    INDEX idx_integrity_verifications_date (verified_at DESC)
);

-- Tabela para auditoria de integridade
CREATE TABLE IF NOT EXISTS integrity_audit_logs (
    id SERIAL PRIMARY KEY,
    recording_id VARCHAR(255) NOT NULL,
    audit_type VARCHAR(50) NOT NULL, -- SCHEDULED, MANUAL, ALERT
    findings JSONB NOT NULL,
    severity VARCHAR(20) NOT NULL, -- LOW, MEDIUM, HIGH, CRITICAL
    action_required BOOLEAN DEFAULT FALSE,
    action_taken TEXT,
    audited_by VARCHAR(255) DEFAULT 'system',
    audited_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP,

    -- Índices para performance
    INDEX idx_integrity_audit_recording_id (recording_id),
    INDEX idx_integrity_audit_severity (severity),
    INDEX idx_integrity_audit_date (audited_at DESC),
    INDEX idx_integrity_audit_unresolved (resolved_at) WHERE resolved_at IS NULL
);

-- Tabela para registros blockchain
CREATE TABLE IF NOT EXISTS blockchain_records (
    id SERIAL PRIMARY KEY,
    recording_id VARCHAR(255) NOT NULL,
    transaction_hash VARCHAR(66) NOT NULL, -- Hash da transação Ethereum
    block_number BIGINT NOT NULL,
    block_hash VARCHAR(66),
    gas_used INTEGER,
    gas_price BIGINT,
    network_id INTEGER,
    contract_address VARCHAR(42),
    registered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    verified_at TIMESTAMP,
    verification_status VARCHAR(20) DEFAULT 'PENDING', -- PENDING, CONFIRMED, FAILED

    -- Índices para performance
    CONSTRAINT unique_recording_blockchain UNIQUE (recording_id),
    INDEX idx_blockchain_records_transaction_hash (transaction_hash),
    INDEX idx_blockchain_records_block_number (block_number),
    INDEX idx_blockchain_records_status (verification_status)
);

-- Tabela para logs de criptografia
CREATE TABLE IF NOT EXISTS encryption_logs (
    id SERIAL PRIMARY KEY,
    recording_id VARCHAR(255) NOT NULL,
    operation VARCHAR(50) NOT NULL, -- ENCRYPT, DECRYPT, VERIFY, ROTATE_KEY
    status VARCHAR(20) NOT NULL, -- SUCCESS, FAILED, WARNING
    details JSONB,
    processing_time_ms INTEGER,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Índices para performance
    INDEX idx_encryption_logs_recording_id (recording_id),
    INDEX idx_encryption_logs_operation (operation),
    INDEX idx_encryption_logs_status (status),
    INDEX idx_encryption_logs_date (created_at DESC)
);

-- Tabela para logs de auditoria imutáveis
CREATE TABLE IF NOT EXISTS audit_logs (
    id SERIAL PRIMARY KEY,
    recording_id VARCHAR(255),
    action VARCHAR(100) NOT NULL,
    user_id VARCHAR(255) NOT NULL,
    user_name VARCHAR(255) NOT NULL,
    user_role VARCHAR(50),
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(32),
    details JSONB,
    severity VARCHAR(20) DEFAULT 'INFO', -- INFO, WARNING, ERROR, CRITICAL
    category VARCHAR(50) DEFAULT 'GENERAL', -- GENERAL, API_ACCESS, INTEGRITY_CHECK, etc.
    log_hash VARCHAR(64) NOT NULL, -- Hash imutável do log
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Índices para performance
    INDEX idx_audit_logs_recording_id (recording_id),
    INDEX idx_audit_logs_user_id (user_id),
    INDEX idx_audit_logs_action (action),
    INDEX idx_audit_logs_severity (severity),
    INDEX idx_audit_logs_category (category),
    INDEX idx_audit_logs_timestamp (timestamp DESC),
    INDEX idx_audit_logs_session_id (session_id),
    INDEX idx_audit_logs_hash (log_hash)
);

-- Tabela para resultados de verificações de integridade
CREATE TABLE IF NOT EXISTS integrity_check_results (
    id SERIAL PRIMARY KEY,
    check_type VARCHAR(20) NOT NULL, -- FULL, QUICK, CRITICAL
    total_files INTEGER NOT NULL,
    checked_files INTEGER NOT NULL,
    violations_found INTEGER NOT NULL,
    processing_time_ms INTEGER NOT NULL,
    results_data JSONB NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Índices para performance
    INDEX idx_integrity_check_results_type (check_type),
    INDEX idx_integrity_check_results_date (created_at DESC),
    INDEX idx_integrity_check_results_violations (violations_found)
);

-- Índices adicionais para performance
CREATE INDEX IF NOT EXISTS idx_recordings_hash ON recordings (file_hash);
CREATE INDEX IF NOT EXISTS idx_recordings_signed_at ON recordings (signed_at DESC);
CREATE INDEX IF NOT EXISTS idx_recordings_verification_status ON recordings (verification_status);
CREATE INDEX IF NOT EXISTS idx_digital_signature_logs_recording_id ON digital_signature_logs (recording_id);
CREATE INDEX IF NOT EXISTS idx_digital_signature_logs_status ON digital_signature_logs (status);
CREATE INDEX IF NOT EXISTS idx_timestamp_records_recording_id ON timestamp_records (recording_id);
CREATE INDEX IF NOT EXISTS idx_timestamp_records_verified ON timestamp_records (verified);

-- Função para atualizar timestamp de updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers para atualizar updated_at automaticamente
CREATE TRIGGER update_icp_certificates_updated_at
    BEFORE UPDATE ON icp_certificates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Função para verificar integridade de gravação
CREATE OR REPLACE FUNCTION verify_recording_integrity(
    p_recording_id VARCHAR(255),
    p_current_hash VARCHAR(64)
)
RETURNS TABLE (
    is_valid BOOLEAN,
    original_hash VARCHAR(64),
    hash_match BOOLEAN,
    signature_status VARCHAR(20),
    timestamp_status VARCHAR(20),
    last_verified TIMESTAMP
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        (r.file_hash = p_current_hash AND r.integrity_verified) as is_valid,
        r.file_hash as original_hash,
        (r.file_hash = p_current_hash) as hash_match,
        CASE 
            WHEN r.digital_signature IS NOT NULL THEN 'SIGNED'
            ELSE 'UNSIGNED'
        END as signature_status,
        CASE 
            WHEN r.timestamp_token IS NOT NULL THEN 'TIMESTAMPED'
            ELSE 'NOT_TIMESTAMPED'
        END as timestamp_status,
        r.last_verification_at as last_verified
    FROM recordings r
    WHERE r.id = p_recording_id;
END;
$$ LANGUAGE plpgsql;

-- Função para obter estatísticas de integridade
CREATE OR REPLACE FUNCTION get_integrity_statistics()
RETURNS TABLE (
    total_recordings BIGINT,
    signed_recordings BIGINT,
    timestamped_recordings BIGINT,
    verified_recordings BIGINT,
    integrity_violations BIGINT,
    pending_verifications BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_recordings,
        COUNT(CASE WHEN digital_signature IS NOT NULL THEN 1 END) as signed_recordings,
        COUNT(CASE WHEN timestamp_token IS NOT NULL THEN 1 END) as timestamped_recordings,
        COUNT(CASE WHEN integrity_verified = TRUE THEN 1 END) as verified_recordings,
        COUNT(CASE WHEN verification_status = 'INVALID' THEN 1 END) as integrity_violations,
        COUNT(CASE WHEN verification_status = 'PENDING' THEN 1 END) as pending_verifications
    FROM recordings;
END;
$$ LANGUAGE plpgsql;

-- Função para agendar verificação de integridade
CREATE OR REPLACE FUNCTION schedule_integrity_check(
    p_recording_id VARCHAR(255),
    p_priority VARCHAR(20) DEFAULT 'NORMAL'
)
RETURNS BOOLEAN AS $$
BEGIN
    INSERT INTO integrity_audit_logs (
        recording_id,
        audit_type,
        findings,
        severity,
        action_required
    ) VALUES (
        p_recording_id,
        'SCHEDULED',
        jsonb_build_object(
            'priority', p_priority,
            'scheduled_at', CURRENT_TIMESTAMP,
            'reason', 'Automatic integrity check'
        ),
        CASE p_priority
            WHEN 'HIGH' THEN 'HIGH'
            WHEN 'LOW' THEN 'LOW'
            ELSE 'MEDIUM'
        END,
        TRUE
    );
    
    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- View para relatório de integridade
CREATE OR REPLACE VIEW integrity_report AS
SELECT 
    r.id as recording_id,
    r.filename,
    r.file_hash,
    r.digital_signature IS NOT NULL as is_signed,
    r.timestamp_token IS NOT NULL as is_timestamped,
    r.integrity_verified,
    r.verification_status,
    r.signed_at,
    r.timestamp_at,
    r.last_verification_at,
    CASE 
        WHEN r.digital_signature IS NOT NULL AND r.timestamp_token IS NOT NULL THEN 'FULL'
        WHEN r.digital_signature IS NOT NULL THEN 'SIGNATURE_ONLY'
        WHEN r.timestamp_token IS NOT NULL THEN 'TIMESTAMP_ONLY'
        ELSE 'NONE'
    END as integrity_level,
    iv.overall_status as last_verification_result,
    iv.verified_at as last_full_verification
FROM recordings r
LEFT JOIN integrity_verifications iv ON r.id = iv.recording_id 
    AND iv.id = (
        SELECT MAX(id) 
        FROM integrity_verifications 
        WHERE recording_id = r.id
    );

-- Comentários para documentação
COMMENT ON TABLE icp_certificates IS 'Armazena certificados ICP-Brasil utilizados para assinatura digital';
COMMENT ON TABLE digital_signature_logs IS 'Logs de operações de assinatura digital';
COMMENT ON TABLE timestamp_records IS 'Registros de carimbos de tempo de autoridades confiáveis';
COMMENT ON TABLE integrity_verifications IS 'Histórico de verificações de integridade';
COMMENT ON TABLE integrity_audit_logs IS 'Logs de auditoria de integridade para compliance';

COMMENT ON FUNCTION verify_recording_integrity IS 'Verifica integridade de uma gravação específica';
COMMENT ON FUNCTION get_integrity_statistics IS 'Retorna estatísticas gerais de integridade';
COMMENT ON FUNCTION schedule_integrity_check IS 'Agenda verificação de integridade para uma gravação';

COMMENT ON VIEW integrity_report IS 'View consolidada para relatórios de integridade';

-- Inserir dados de exemplo para desenvolvimento (opcional)
-- INSERT INTO icp_certificates (certificate_id, subject_name, issuer_name, serial_number, valid_from, valid_to, certificate_pem, fingerprint, key_usage)
-- VALUES ('cert_demo_001', 'CartorioTech Demo', 'AC Demo ICP-Brasil', '123456789', '2024-01-01', '2025-12-31', '-----BEGIN CERTIFICATE-----...', 'abc123...', ARRAY['digitalSignature', 'nonRepudiation']);

COMMIT;
