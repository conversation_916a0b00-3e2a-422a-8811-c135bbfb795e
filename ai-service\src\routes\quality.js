const express = require('express');
const QualityService = require('../services/qualityService');
const queueManager = require('../services/queueManager');

const router = express.Router();
const qualityService = new QualityService();

/**
 * GET /api/ai/quality
 * Informações sobre o serviço de análise de qualidade
 */
router.get('/', (req, res) => {
  res.json({
    success: true,
    service: 'Serviço de Análise de Qualidade',
    version: '1.0.0',
    availableEndpoints: [
      'POST /api/ai/quality/analyze - Análise de qualidade de gravação',
      'POST /api/ai/quality/audio - Análise de qualidade de áudio',
      'POST /api/ai/quality/video - Análise de qualidade de vídeo',
      'GET /api/ai/quality/report/:id - Relatório de qualidade'
    ],
    metrics: [
      'Qualidade de áudio (SNR, distorção)',
      '<PERSON><PERSON> da fala',
      'Ruí<PERSON> de fundo',
      'Volume e equalização',
      'Inteligibilidade',
      'Duração e continuidade'
    ],
    features: [
      'Análise automática de qualidade',
      'Detecção de problemas técnicos',
      'Sugestões de melhoria',
      'Scoring de qualidade',
      'Relatórios detalhados',
      'Alertas de qualidade baixa'
    ],
    timestamp: new Date().toISOString()
  });
});

/**
 * POST /api/ai/quality/analyze
 * Analisar qualidade de uma gravação
 */
router.post('/analyze', async (req, res) => {
  try {
    const { recordingId, filePath, metadata } = req.body;

    if (!recordingId && !filePath) {
      return res.status(400).json({
        success: false,
        message: 'ID da gravação ou caminho do arquivo é obrigatório'
      });
    }

    // Adicionar à fila de processamento
    const jobId = await queueManager.addJob('quality-analysis', {
      recordingId,
      filePath,
      metadata,
      timestamp: new Date().toISOString()
    });

    res.json({
      success: true,
      message: 'Análise de qualidade iniciada',
      jobId,
      estimatedTime: '2-5 minutos'
    });

  } catch (error) {
    console.error('Erro na análise de qualidade:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
});

/**
 * POST /api/ai/quality/audio
 * Analisar especificamente a qualidade do áudio
 */
router.post('/audio', async (req, res) => {
  try {
    const { audioPath, options = {} } = req.body;

    if (!audioPath) {
      return res.status(400).json({
        success: false,
        message: 'Caminho do arquivo de áudio é obrigatório'
      });
    }

    const result = await qualityService.analyzeAudioQuality(audioPath, options);

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Erro na análise de áudio:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
});

/**
 * POST /api/ai/quality/video
 * Analisar especificamente a qualidade do vídeo
 */
router.post('/video', async (req, res) => {
  try {
    const { videoPath, options = {} } = req.body;

    if (!videoPath) {
      return res.status(400).json({
        success: false,
        message: 'Caminho do arquivo de vídeo é obrigatório'
      });
    }

    const result = await qualityService.analyzeVideoQuality(videoPath, options);

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Erro na análise de vídeo:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
});

/**
 * GET /api/ai/quality/report/:id
 * Obter relatório de qualidade
 */
router.get('/report/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const report = await qualityService.getQualityReport(id);

    if (!report) {
      return res.status(404).json({
        success: false,
        message: 'Relatório de qualidade não encontrado'
      });
    }

    res.json({
      success: true,
      data: report
    });

  } catch (error) {
    console.error('Erro ao obter relatório:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
});

/**
 * GET /api/ai/quality/stats
 * Estatísticas de qualidade
 */
router.get('/stats', async (req, res) => {
  try {
    const stats = await qualityService.getQualityStats();

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('Erro ao obter estatísticas:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
});

module.exports = router;
