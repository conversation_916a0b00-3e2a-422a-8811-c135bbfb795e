#!/usr/bin/env node

/**
 * Script para migrar arquivos de gravação para o diretório correto
 * e corrigir caminhos no banco de dados
 */

const fs = require('fs');
const path = require('path');
const { getAllRecordings, initializeDatabase } = require('../database');

async function migrateRecordings() {
  console.log('🔄 Iniciando migração de arquivos de gravação...');

  try {
    // Inicializar banco de dados primeiro
    console.log('🔄 Inicializando conexão com banco de dados...');
    await initializeDatabase();
    console.log('✅ Banco de dados inicializado');

    // Aguardar um pouco para garantir que a conexão está estável
    await new Promise(resolve => setTimeout(resolve, 2000));
    // Criar diretório de destino se não existir
    const recordingsDir = path.join(__dirname, '../../storage/recordings');
    if (!fs.existsSync(recordingsDir)) {
      fs.mkdirSync(recordingsDir, { recursive: true });
      console.log('📁 Diretório de gravações criado:', recordingsDir);
    }
    
    // Buscar todas as gravações no banco
    const result = await getAllRecordings();
    const recordings = result.recordings || result || [];
    console.log(`📊 Encontradas ${recordings.length} gravações no banco de dados`);
    
    let migratedCount = 0;
    let errorCount = 0;
    
    for (const recording of recordings) {
      try {
        const currentPath = recording.file_path;
        const filename = recording.filename;
        const correctPath = path.join(recordingsDir, filename);
        
        console.log(`🔍 Verificando gravação ${recording.id}:`);
        console.log(`   Caminho atual: ${currentPath}`);
        console.log(`   Caminho correto: ${correctPath}`);
        
        // Verificar se o arquivo existe no caminho atual
        if (fs.existsSync(currentPath)) {
          // Se o caminho atual é diferente do correto, mover o arquivo
          if (currentPath !== correctPath) {
            fs.copyFileSync(currentPath, correctPath);
            console.log(`✅ Arquivo copiado para: ${correctPath}`);
            
            // Remover arquivo antigo se a cópia foi bem-sucedida
            if (fs.existsSync(correctPath)) {
              fs.unlinkSync(currentPath);
              console.log(`🗑️ Arquivo antigo removido: ${currentPath}`);
            }
            
            migratedCount++;
          } else {
            console.log(`✅ Arquivo já está no local correto`);
          }
        } else {
          console.log(`❌ Arquivo não encontrado: ${currentPath}`);
          errorCount++;
        }
        
        console.log('---');
        
      } catch (error) {
        console.error(`❌ Erro ao processar gravação ${recording.id}:`, error.message);
        errorCount++;
      }
    }
    
    console.log('\n📊 Resumo da migração:');
    console.log(`✅ Arquivos migrados: ${migratedCount}`);
    console.log(`❌ Erros encontrados: ${errorCount}`);
    console.log(`📁 Diretório de destino: ${recordingsDir}`);
    
    if (migratedCount > 0) {
      console.log('\n🔄 Recomendação: Reinicie o backend para aplicar as mudanças');
    }
    
  } catch (error) {
    console.error('❌ Erro durante a migração:', error);
    process.exit(1);
  }
}

// Executar migração se chamado diretamente
if (require.main === module) {
  migrateRecordings()
    .then(() => {
      console.log('✅ Migração concluída');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Migração falhou:', error);
      process.exit(1);
    });
}

module.exports = { migrateRecordings };
