const express = require('express');
const EncryptionService = require('../services/encryptionService');
const { Pool } = require('pg');
const path = require('path');
const fs = require('fs');
const router = express.Router();

// Pool de conexões PostgreSQL
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'cartorio_db',
  user: process.env.DB_USER || 'cartorio_user',
  password: process.env.DB_PASSWORD || 'cartorio_secure_pass',
});

const encryptionService = new EncryptionService();

/**
 * @swagger
 * /api/encryption/encrypt/{recordingId}:
 *   post:
 *     summary: Criptografar gravação
 *     tags: [Encryption]
 *     parameters:
 *       - in: path
 *         name: recordingId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID da gravação
 *     responses:
 *       200:
 *         description: Gravação criptografada com sucesso
 *       404:
 *         description: Gravação não encontrada
 *       500:
 *         description: Erro interno do servidor
 */
router.post('/encrypt/:recordingId', async (req, res) => {
  const { recordingId } = req.params;
  
  try {
    // Buscar gravação no banco
    const recordingQuery = 'SELECT * FROM recordings WHERE id = $1';
    const recordingResult = await pool.query(recordingQuery, [recordingId]);
    
    if (recordingResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Gravação não encontrada'
      });
    }
    
    const recording = recordingResult.rows[0];
    const inputPath = recording.file_path;
    
    if (!fs.existsSync(inputPath)) {
      return res.status(404).json({
        success: false,
        error: 'Arquivo da gravação não encontrado'
      });
    }
    
    // Verificar se já está criptografado
    if (recording.encrypted) {
      return res.status(400).json({
        success: false,
        error: 'Gravação já está criptografada'
      });
    }
    
    // Definir caminho do arquivo criptografado
    const outputPath = `${inputPath}.enc`;
    
    const startTime = Date.now();
    
    // Criptografar arquivo
    const encryptionResult = await encryptionService.encryptFile(inputPath, outputPath, recordingId);
    
    const processingTime = Date.now() - startTime;
    
    // Atualizar registro no banco
    const updateQuery = `
      UPDATE recordings SET 
        encrypted = true,
        encrypted_path = $1,
        encryption_key_id = $2,
        encryption_iv = $3,
        encryption_tag = $4,
        encrypted_hash = $5,
        encryption_algorithm = $6,
        encrypted_at = $7
      WHERE id = $8
    `;
    
    await pool.query(updateQuery, [
      outputPath,
      encryptionResult.keyId,
      encryptionResult.iv,
      encryptionResult.tag,
      encryptionResult.encryptedHash,
      encryptionResult.algorithm,
      encryptionResult.encryptedAt,
      recordingId
    ]);
    
    // Log da operação
    await pool.query(
      `INSERT INTO encryption_logs 
       (recording_id, operation, status, details, processing_time_ms) 
       VALUES ($1, $2, $3, $4, $5)`,
      [
        recordingId,
        'ENCRYPT',
        'SUCCESS',
        JSON.stringify({
          inputPath,
          outputPath,
          algorithm: encryptionResult.algorithm,
          keyId: encryptionResult.keyId
        }),
        processingTime
      ]
    );
    
    // Opcional: remover arquivo original após criptografia bem-sucedida
    if (process.env.REMOVE_ORIGINAL_AFTER_ENCRYPTION === 'true') {
      fs.unlinkSync(inputPath);
    }
    
    res.json({
      success: true,
      message: 'Gravação criptografada com sucesso',
      data: {
        recordingId,
        encryptedPath: outputPath,
        algorithm: encryptionResult.algorithm,
        encryptedAt: encryptionResult.encryptedAt,
        processingTimeMs: processingTime
      }
    });
    
  } catch (error) {
    console.error('[Encryption] Erro ao criptografar gravação:', error);
    
    // Log erro da operação
    await pool.query(
      `INSERT INTO encryption_logs 
       (recording_id, operation, status, error_message) 
       VALUES ($1, $2, $3, $4)`,
      [recordingId, 'ENCRYPT', 'FAILED', error.message]
    );
    
    res.status(500).json({
      success: false,
      error: 'Erro ao criptografar gravação',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/encryption/decrypt/{recordingId}:
 *   post:
 *     summary: Descriptografar gravação
 *     tags: [Encryption]
 *     parameters:
 *       - in: path
 *         name: recordingId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID da gravação
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               outputPath:
 *                 type: string
 *                 description: Caminho de saída (opcional)
 *     responses:
 *       200:
 *         description: Gravação descriptografada com sucesso
 *       404:
 *         description: Gravação não encontrada
 *       500:
 *         description: Erro interno do servidor
 */
router.post('/decrypt/:recordingId', async (req, res) => {
  const { recordingId } = req.params;
  const { outputPath } = req.body;
  
  try {
    // Buscar gravação no banco
    const recordingQuery = 'SELECT * FROM recordings WHERE id = $1';
    const recordingResult = await pool.query(recordingQuery, [recordingId]);
    
    if (recordingResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Gravação não encontrada'
      });
    }
    
    const recording = recordingResult.rows[0];
    
    if (!recording.encrypted) {
      return res.status(400).json({
        success: false,
        error: 'Gravação não está criptografada'
      });
    }
    
    const inputPath = recording.encrypted_path;
    const finalOutputPath = outputPath || `${recording.file_path}.decrypted`;
    
    if (!fs.existsSync(inputPath)) {
      return res.status(404).json({
        success: false,
        error: 'Arquivo criptografado não encontrado'
      });
    }
    
    const startTime = Date.now();
    
    // Descriptografar arquivo
    const decryptionResult = await encryptionService.decryptFile(inputPath, finalOutputPath, recordingId);
    
    const processingTime = Date.now() - startTime;
    
    // Log da operação
    await pool.query(
      `INSERT INTO encryption_logs 
       (recording_id, operation, status, details, processing_time_ms) 
       VALUES ($1, $2, $3, $4, $5)`,
      [
        recordingId,
        'DECRYPT',
        'SUCCESS',
        JSON.stringify({
          inputPath,
          outputPath: finalOutputPath,
          decryptedAt: decryptionResult.decryptedAt
        }),
        processingTime
      ]
    );
    
    res.json({
      success: true,
      message: 'Gravação descriptografada com sucesso',
      data: {
        recordingId,
        decryptedPath: finalOutputPath,
        decryptedAt: decryptionResult.decryptedAt,
        processingTimeMs: processingTime
      }
    });
    
  } catch (error) {
    console.error('[Encryption] Erro ao descriptografar gravação:', error);
    
    // Log erro da operação
    await pool.query(
      `INSERT INTO encryption_logs 
       (recording_id, operation, status, error_message) 
       VALUES ($1, $2, $3, $4)`,
      [recordingId, 'DECRYPT', 'FAILED', error.message]
    );
    
    res.status(500).json({
      success: false,
      error: 'Erro ao descriptografar gravação',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/encryption/verify/{recordingId}:
 *   get:
 *     summary: Verificar integridade de arquivo criptografado
 *     tags: [Encryption]
 *     parameters:
 *       - in: path
 *         name: recordingId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID da gravação
 *     responses:
 *       200:
 *         description: Verificação concluída
 *       404:
 *         description: Gravação não encontrada
 *       500:
 *         description: Erro interno do servidor
 */
router.get('/verify/:recordingId', async (req, res) => {
  const { recordingId } = req.params;
  
  try {
    // Buscar gravação no banco
    const recordingQuery = 'SELECT * FROM recordings WHERE id = $1';
    const recordingResult = await pool.query(recordingQuery, [recordingId]);
    
    if (recordingResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Gravação não encontrada'
      });
    }
    
    const recording = recordingResult.rows[0];
    
    if (!recording.encrypted) {
      return res.status(400).json({
        success: false,
        error: 'Gravação não está criptografada'
      });
    }
    
    // Verificar integridade
    const verificationResult = await encryptionService.verifyEncryptedFile(
      recording.encrypted_path,
      recording.encrypted_hash
    );
    
    // Atualizar status no banco
    await pool.query(
      'UPDATE recordings SET last_encryption_verification = $1 WHERE id = $2',
      [new Date(), recordingId]
    );
    
    // Log da operação
    await pool.query(
      `INSERT INTO encryption_logs 
       (recording_id, operation, status, details) 
       VALUES ($1, $2, $3, $4)`,
      [
        recordingId,
        'VERIFY',
        verificationResult.valid ? 'SUCCESS' : 'WARNING',
        JSON.stringify(verificationResult)
      ]
    );
    
    res.json({
      success: true,
      message: 'Verificação de integridade concluída',
      data: verificationResult
    });
    
  } catch (error) {
    console.error('[Encryption] Erro na verificação:', error);
    res.status(500).json({
      success: false,
      error: 'Erro na verificação de integridade',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/encryption/rotate-key/{recordingId}:
 *   post:
 *     summary: Rotacionar chave de criptografia
 *     tags: [Encryption]
 *     parameters:
 *       - in: path
 *         name: recordingId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID da gravação
 *     responses:
 *       200:
 *         description: Chave rotacionada com sucesso
 *       404:
 *         description: Gravação não encontrada
 *       500:
 *         description: Erro interno do servidor
 */
router.post('/rotate-key/:recordingId', async (req, res) => {
  const { recordingId } = req.params;
  
  try {
    // Buscar gravação no banco
    const recordingQuery = 'SELECT * FROM recordings WHERE id = $1';
    const recordingResult = await pool.query(recordingQuery, [recordingId]);
    
    if (recordingResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Gravação não encontrada'
      });
    }
    
    const recording = recordingResult.rows[0];
    
    if (!recording.encrypted) {
      return res.status(400).json({
        success: false,
        error: 'Gravação não está criptografada'
      });
    }
    
    const oldPath = recording.encrypted_path;
    const newPath = `${oldPath}.rotated`;
    
    const startTime = Date.now();
    
    // Rotacionar chave
    const rotationResult = await encryptionService.rotateKey(recordingId, oldPath, newPath);
    
    const processingTime = Date.now() - startTime;
    
    // Atualizar registro no banco
    await pool.query(
      `UPDATE recordings SET 
        encrypted_path = $1,
        encryption_key_id = $2,
        encryption_iv = $3,
        encryption_tag = $4,
        encrypted_hash = $5,
        key_rotated_at = $6
       WHERE id = $7`,
      [
        newPath,
        rotationResult.keyId,
        rotationResult.iv,
        rotationResult.tag,
        rotationResult.encryptedHash,
        new Date(),
        recordingId
      ]
    );
    
    // Remover arquivo antigo
    if (fs.existsSync(oldPath)) {
      fs.unlinkSync(oldPath);
    }
    
    // Log da operação
    await pool.query(
      `INSERT INTO encryption_logs 
       (recording_id, operation, status, details, processing_time_ms) 
       VALUES ($1, $2, $3, $4, $5)`,
      [
        recordingId,
        'ROTATE_KEY',
        'SUCCESS',
        JSON.stringify({
          oldPath,
          newPath,
          newKeyId: rotationResult.keyId
        }),
        processingTime
      ]
    );
    
    res.json({
      success: true,
      message: 'Chave rotacionada com sucesso',
      data: {
        recordingId,
        newPath,
        newKeyId: rotationResult.keyId,
        rotatedAt: new Date().toISOString(),
        processingTimeMs: processingTime
      }
    });
    
  } catch (error) {
    console.error('[Encryption] Erro na rotação de chave:', error);
    res.status(500).json({
      success: false,
      error: 'Erro na rotação de chave',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/encryption/statistics:
 *   get:
 *     summary: Obter estatísticas de criptografia
 *     tags: [Encryption]
 *     responses:
 *       200:
 *         description: Estatísticas de criptografia
 *       500:
 *         description: Erro interno do servidor
 */
router.get('/statistics', async (req, res) => {
  try {
    // Estatísticas do serviço
    const serviceStats = encryptionService.getEncryptionStats();
    
    // Estatísticas do banco
    const dbStats = await pool.query(`
      SELECT 
        COUNT(*) as total_recordings,
        COUNT(CASE WHEN encrypted = true THEN 1 END) as encrypted_recordings,
        COUNT(CASE WHEN key_rotated_at IS NOT NULL THEN 1 END) as rotated_keys,
        AVG(EXTRACT(EPOCH FROM (encrypted_at - created_at))) as avg_encryption_time_seconds
      FROM recordings
    `);
    
    const recentLogs = await pool.query(`
      SELECT operation, status, COUNT(*) as count
      FROM encryption_logs 
      WHERE created_at > NOW() - INTERVAL '24 hours'
      GROUP BY operation, status
      ORDER BY operation, status
    `);
    
    res.json({
      success: true,
      data: {
        service: serviceStats,
        database: dbStats.rows[0],
        recentActivity: recentLogs.rows,
        generatedAt: new Date().toISOString()
      }
    });
    
  } catch (error) {
    console.error('[Encryption] Erro ao obter estatísticas:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao obter estatísticas de criptografia',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
