const crypto = require('crypto');
const fs = require('fs');
const path = require('path');
const axios = require('axios');
const forge = require('node-forge');

class DigitalSignatureService {
  constructor() {
    this.certificatePath = process.env.ICP_CERTIFICATE_PATH || './certificates/icp-certificate.p12';
    this.certificatePassword = process.env.ICP_CERTIFICATE_PASSWORD || 'default_password';
    this.timestampUrl = process.env.TIMESTAMP_AUTHORITY_URL || 'http://timestamp.iti.gov.br/';
    this.timestampUser = process.env.TIMESTAMP_USERNAME;
    this.timestampPassword = process.env.TIMESTAMP_PASSWORD;
  }

  /**
   * Gerar hash SHA-256 de um arquivo
   */
  generateFileHash(filePath) {
    try {
      const fileBuffer = fs.readFileSync(filePath);
      return crypto.createHash('sha256').update(fileBuffer).digest('hex');
    } catch (error) {
      console.error('[DigitalSignature] Erro ao gerar hash:', error);
      throw new Error('Erro ao gerar hash do arquivo');
    }
  }

  /**
   * Carregar certificado ICP-Brasil
   */
  loadCertificate() {
    try {
      if (!fs.existsSync(this.certificatePath)) {
        throw new Error('Certificado ICP-Brasil não encontrado');
      }

      const p12Buffer = fs.readFileSync(this.certificatePath);
      const p12Asn1 = forge.asn1.fromDer(p12Buffer.toString('binary'));
      const p12 = forge.pkcs12.pkcs12FromAsn1(p12Asn1, this.certificatePassword);

      // Extrair chave privada e certificado
      const keyBags = p12.getBags({ bagType: forge.pki.oids.pkcs8ShroudedKeyBag });
      const certBags = p12.getBags({ bagType: forge.pki.oids.certBag });

      const privateKey = keyBags[forge.pki.oids.pkcs8ShroudedKeyBag][0].key;
      const certificate = certBags[forge.pki.oids.certBag][0].cert;

      return { privateKey, certificate };
    } catch (error) {
      console.error('[DigitalSignature] Erro ao carregar certificado:', error);
      throw new Error('Erro ao carregar certificado ICP-Brasil');
    }
  }

  /**
   * Assinar arquivo com certificado ICP-Brasil
   */
  async signFile(filePath, recordingId) {
    try {
      const fileHash = this.generateFileHash(filePath);
      const { privateKey, certificate } = this.loadCertificate();

      // Criar assinatura PKCS#7
      const p7 = forge.pkcs7.createSignedData();
      p7.content = forge.util.createBuffer(fileHash, 'hex');
      
      p7.addCertificate(certificate);
      p7.addSigner({
        key: privateKey,
        certificate: certificate,
        digestAlgorithm: forge.pki.oids.sha256,
        authenticatedAttributes: [
          {
            type: forge.pki.oids.contentTypes,
            value: forge.pki.oids.data
          },
          {
            type: forge.pki.oids.messageDigest
          },
          {
            type: forge.pki.oids.signingTime,
            value: new Date()
          }
        ]
      });

      p7.sign();

      const signature = forge.asn1.toDer(p7.toAsn1()).getBytes();
      const signatureBase64 = forge.util.encode64(signature);

      // Salvar arquivo de assinatura
      const signaturePath = `${filePath}.p7s`;
      fs.writeFileSync(signaturePath, signature, 'binary');

      console.log(`[DigitalSignature] Arquivo assinado: ${recordingId}`);
      
      return {
        hash: fileHash,
        signature: signatureBase64,
        signaturePath,
        certificate: forge.pki.certificateToPem(certificate),
        signedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('[DigitalSignature] Erro ao assinar arquivo:', error);
      throw new Error('Erro ao assinar arquivo digitalmente');
    }
  }

  /**
   * Obter carimbo de tempo de autoridade confiável
   */
  async getTimestamp(hash) {
    try {
      // Criar requisição TSA (Time Stamp Authority)
      const tsaRequest = {
        version: 1,
        messageImprint: {
          hashAlgorithm: 'sha256',
          hashedMessage: hash
        },
        reqPolicy: '1.3.6.1.4.1.311.3.2.1', // Política padrão
        nonce: crypto.randomBytes(8).toString('hex'),
        certReq: true
      };

      const response = await axios.post(this.timestampUrl, tsaRequest, {
        headers: {
          'Content-Type': 'application/timestamp-query',
          'User-Agent': 'CartorioTech-TSA-Client/1.0'
        },
        auth: this.timestampUser && this.timestampPassword ? {
          username: this.timestampUser,
          password: this.timestampPassword
        } : undefined,
        timeout: 30000,
        responseType: 'arraybuffer'
      });

      if (response.status !== 200) {
        throw new Error(`Erro na autoridade de carimbo de tempo: ${response.status}`);
      }

      const timestampToken = Buffer.from(response.data).toString('base64');
      
      console.log(`[DigitalSignature] Carimbo de tempo obtido para hash: ${hash.substring(0, 16)}...`);
      
      return {
        token: timestampToken,
        authority: this.timestampUrl,
        timestamp: new Date().toISOString(),
        nonce: tsaRequest.nonce
      };
    } catch (error) {
      console.error('[DigitalSignature] Erro ao obter carimbo de tempo:', error);
      
      // Fallback: carimbo de tempo local (menos confiável)
      console.warn('[DigitalSignature] Usando carimbo de tempo local como fallback');
      return {
        token: crypto.createHash('sha256').update(`${hash}${Date.now()}`).digest('base64'),
        authority: 'local-fallback',
        timestamp: new Date().toISOString(),
        nonce: crypto.randomBytes(8).toString('hex'),
        fallback: true
      };
    }
  }

  /**
   * Verificar assinatura digital
   */
  async verifySignature(filePath, signaturePath) {
    try {
      const fileHash = this.generateFileHash(filePath);
      const signatureBuffer = fs.readFileSync(signaturePath, 'binary');
      
      const p7 = forge.pkcs7.messageFromAsn1(forge.asn1.fromDer(signatureBuffer));
      
      // Verificar assinatura
      const verified = p7.verify();
      
      if (!verified) {
        throw new Error('Assinatura digital inválida');
      }

      // Extrair informações do certificado
      const signerCert = p7.certificates[0];
      const signerInfo = {
        subject: signerCert.subject.getField('CN').value,
        issuer: signerCert.issuer.getField('CN').value,
        validFrom: signerCert.validity.notBefore,
        validTo: signerCert.validity.notAfter,
        serialNumber: signerCert.serialNumber
      };

      console.log(`[DigitalSignature] Assinatura verificada com sucesso`);
      
      return {
        valid: true,
        hash: fileHash,
        signer: signerInfo,
        verifiedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('[DigitalSignature] Erro ao verificar assinatura:', error);
      return {
        valid: false,
        error: error.message,
        verifiedAt: new Date().toISOString()
      };
    }
  }

  /**
   * Processo completo: assinar arquivo e obter carimbo de tempo
   */
  async signAndTimestamp(filePath, recordingId) {
    try {
      console.log(`[DigitalSignature] Iniciando processo completo para: ${recordingId}`);
      
      // 1. Assinar arquivo
      const signatureResult = await this.signFile(filePath, recordingId);
      
      // 2. Obter carimbo de tempo
      const timestampResult = await this.getTimestamp(signatureResult.hash);
      
      // 3. Combinar resultados
      const result = {
        recordingId,
        filePath,
        hash: signatureResult.hash,
        signature: {
          data: signatureResult.signature,
          path: signatureResult.signaturePath,
          certificate: signatureResult.certificate,
          signedAt: signatureResult.signedAt
        },
        timestamp: timestampResult,
        processedAt: new Date().toISOString(),
        integrity: {
          signed: true,
          timestamped: !timestampResult.fallback,
          verified: false // Será verificado posteriormente
        }
      };

      console.log(`[DigitalSignature] Processo completo concluído para: ${recordingId}`);
      return result;
    } catch (error) {
      console.error('[DigitalSignature] Erro no processo completo:', error);
      throw error;
    }
  }

  /**
   * Verificar integridade completa (assinatura + carimbo de tempo)
   */
  async verifyIntegrity(filePath, signaturePath, timestampData) {
    try {
      // Verificar assinatura
      const signatureVerification = await this.verifySignature(filePath, signaturePath);
      
      // Verificar carimbo de tempo (implementação simplificada)
      const timestampValid = timestampData && timestampData.token && timestampData.timestamp;
      
      return {
        signatureValid: signatureVerification.valid,
        timestampValid,
        overallValid: signatureVerification.valid && timestampValid,
        details: {
          signature: signatureVerification,
          timestamp: timestampData
        },
        verifiedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('[DigitalSignature] Erro na verificação de integridade:', error);
      return {
        signatureValid: false,
        timestampValid: false,
        overallValid: false,
        error: error.message,
        verifiedAt: new Date().toISOString()
      };
    }
  }

  /**
   * Gerar relatório de integridade
   */
  generateIntegrityReport(verificationResult) {
    const report = {
      status: verificationResult.overallValid ? 'VÁLIDO' : 'INVÁLIDO',
      timestamp: verificationResult.verifiedAt,
      checks: {
        assinatura_digital: verificationResult.signatureValid ? 'VÁLIDA' : 'INVÁLIDA',
        carimbo_tempo: verificationResult.timestampValid ? 'VÁLIDO' : 'INVÁLIDO'
      },
      details: verificationResult.details,
      recommendations: []
    };

    if (!verificationResult.signatureValid) {
      report.recommendations.push('Verificar integridade do arquivo original');
      report.recommendations.push('Validar certificado digital utilizado');
    }

    if (!verificationResult.timestampValid) {
      report.recommendations.push('Verificar conectividade com autoridade de carimbo de tempo');
      report.recommendations.push('Validar token de carimbo de tempo');
    }

    return report;
  }
}

module.exports = DigitalSignatureService;
