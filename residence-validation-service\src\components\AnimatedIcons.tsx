'use client';

import { useState, useEffect } from 'react';

// Componente de ícone animado para sucesso
export const AnimatedSuccess = ({ className = "w-6 h-6" }: { className?: string }) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  return (
    <svg 
      className={`${className} text-green-600 transition-all duration-700 ${isVisible ? 'scale-100 opacity-100' : 'scale-0 opacity-0'}`}
      fill="none" 
      stroke="currentColor" 
      viewBox="0 0 24 24"
    >
      <path 
        strokeLinecap="round" 
        strokeLinejoin="round" 
        strokeWidth={2} 
        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
        className="animate-pulse"
      />
    </svg>
  );
};

// Componente de loading com animação
export const AnimatedLoading = ({ className = "w-6 h-6" }: { className?: string }) => {
  return (
    <div className={`${className} animate-spin rounded-full border-2 border-gray-300 border-t-blue-600`} />
  );
};

// Componente de ícone de segurança com animação
export const AnimatedSecurity = ({ className = "w-6 h-6" }: { className?: string }) => {
  return (
    <svg 
      className={`${className} text-green-600 animate-pulse`}
      fill="none" 
      stroke="currentColor" 
      viewBox="0 0 24 24"
    >
      <path 
        strokeLinecap="round" 
        strokeLinejoin="round" 
        strokeWidth={2} 
        d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" 
      />
    </svg>
  );
};

// Componente de ícone de validação com animação
export const AnimatedValidation = ({ className = "w-6 h-6" }: { className?: string }) => {
  return (
    <svg 
      className={`${className} text-blue-600 animate-bounce`}
      fill="none" 
      stroke="currentColor" 
      viewBox="0 0 24 24"
    >
      <path 
        strokeLinecap="round" 
        strokeLinejoin="round" 
        strokeWidth={2} 
        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" 
      />
    </svg>
  );
};

// Componente de contador animado
export const AnimatedCounter = ({ 
  target, 
  suffix = "", 
  duration = 2000,
  className = "text-2xl font-bold" 
}: { 
  target: number; 
  suffix?: string; 
  duration?: number;
  className?: string;
}) => {
  const [count, setCount] = useState(0);
  useEffect(() => {
    let startTime: number;

    const updateCount = (timestamp: number) => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / duration, 1);
      
      setCount(Math.floor(progress * target));
      
      if (progress < 1) {
        requestAnimationFrame(updateCount);
      }
    };

    requestAnimationFrame(updateCount);
  }, [target, duration]);

  return (
    <span className={className}>
      {count}{suffix}
    </span>
  );
};

// Componente de progresso com animação
export const AnimatedProgress = ({ 
  progress, 
  className = "w-full h-2" 
}: { 
  progress: number; 
  className?: string;
}) => {
  return (
    <div className={`${className} bg-gray-200 rounded-full overflow-hidden`}>
      <div 
        className="h-full bg-blue-600 rounded-full transition-all duration-1000 ease-out"
        style={{ width: `${Math.min(Math.max(progress, 0), 100)}%` }}
      />
    </div>
  );
};

// Componente de card com hover animado
export const AnimatedCard = ({ 
  children, 
  className = "" 
}: { 
  children: React.ReactNode; 
  className?: string;
}) => {
  return (
    <div className={`
      ${className} 
      transition-all duration-300 ease-in-out 
      hover:shadow-lg hover:-translate-y-1 hover:scale-105
      cursor-pointer
    `}>
      {children}
    </div>
  );
};

// Componente de fade in animado
export const FadeInSection = ({ 
  children, 
  delay = 0,
  className = "" 
}: { 
  children: React.ReactNode; 
  delay?: number;
  className?: string;
}) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, delay);

    return () => clearTimeout(timer);
  }, [delay]);

  return (
    <div className={`
      ${className}
      transition-all duration-1000 ease-out
      ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}
    `}>
      {children}
    </div>
  );
};

// Componente de botão com animação de clique
export const AnimatedButton = ({ 
  children, 
  onClick,
  className = "",
  disabled = false,
  ...props 
}: { 
  children: React.ReactNode; 
  onClick?: () => void;
  className?: string;
  disabled?: boolean;
  [key: string]: any;
}) => {
  const [isClicked, setIsClicked] = useState(false);

  const handleClick = () => {
    if (disabled) return;
    
    setIsClicked(true);
    setTimeout(() => setIsClicked(false), 150);
    
    if (onClick) onClick();
  };

  return (
    <button 
      className={`
        ${className}
        transition-all duration-150 ease-in-out
        ${isClicked ? 'scale-95' : 'hover:scale-105'}
        ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-lg'}
        focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
      `}
      onClick={handleClick}
      disabled={disabled}
      {...props}
    >
      {children}
    </button>
  );
};
