# Plano de Integração - Sistema de Validação de Residência

## Análise da Situação Atual

### <PERSON><PERSON><PERSON> Principal (Cartório)
- **Arquitetura**: Microserviços com Docker
- **Backend**: Node.js/Express (porta 3001)
- **Frontend**: React (porta 80)
- **Banco**: PostgreSQL (porta 5433)
- **Serviços**: AI Service (3003), Signature Service (3004)

### Sistema de Validação de Residência
- **Tipo**: Next.js 15 completo (standalone)
- **Localização**: `residence-validation-service/`
- **Recursos**: OAuth2 Google, Google Timeline, PDF Generation, LGPD

## Estratégia de Integração

### 1. MANTER no Sistema Principal
✅ **Já Integrado**:
- `backend/src/routes/residence/` - Rotas de API
- `web-app/src/components/residence/` - Componentes React
- Configurações do nginx.conf
- Dependências no package.json

### 2. MIGRAR do Sistema Independente
📋 **Arquivos a Mover**:

#### APIs (Next.js → Express)
- `residence-validation-service/src/app/api/timeline/collect/route.ts`
- `residence-validation-service/src/app/api/certificate/generate/route.ts`
- `residence-validation-service/src/app/api/auth/google/route.ts`
- `residence-validation-service/src/app/api/debug/route.ts`

#### Componentes React
- `residence-validation-service/src/components/ConsentModal.tsx`
- `residence-validation-service/src/components/AnimatedIcons.tsx`
- `residence-validation-service/src/app/dashboard/page.tsx` (lógica)

#### Utilities e Libs
- `residence-validation-service/src/lib/google-auth.ts`
- `residence-validation-service/src/lib/config.ts`
- `residence-validation-service/src/utils/validation.ts`
- `residence-validation-service/src/types/index.ts`

#### Documentação
- `residence-validation-service/DOCUMENTACAO_COMPLETA_SISTEMA.md`
- `residence-validation-service/GOOGLE_TIMELINE_GUIDE.md`
- `residence-validation-service/OAUTH_SETUP_GUIDE.md`

### 3. REMOVER (Duplicados/Desnecessários)
❌ **Arquivos para Deletar**:
- `residence-validation-service/Dockerfile` (já temos no backend)
- `residence-validation-service/docker-compose.prod.yml` (já temos principal)
- `residence-validation-service/nginx.conf` (já configurado)
- `residence-validation-service/package.json` (deps já integradas)
- `residence-validation-service/.next/` (build do Next.js)
- `residence-validation-service/node_modules/` (deps já no backend)

### 4. ARQUIVAR (Referência Futura)
📁 **Mover para `archived/residence-validation-backup/`**:
- Todo o conteúdo restante do `residence-validation-service/`
- Manter como backup e referência

## Próximos Passos

### Fase 1: Migração de Componentes
1. Migrar componentes React faltantes
2. Migrar utilitários e tipos TypeScript
3. Atualizar imports e dependências

### Fase 2: Migração de APIs
1. Converter rotas Next.js para Express
2. Integrar com sistema de autenticação existente
3. Testar endpoints

### Fase 3: Testes e Validação
1. Testar funcionalidades integradas
2. Validar OAuth2 flow
3. Testar geração de certificados

### Fase 4: Limpeza
1. Remover arquivos duplicados
2. Arquivar sistema original
3. Atualizar documentação

## Dependências Necessárias

### Backend (já adicionadas)
- googleapis
- google-auth-library
- jsonwebtoken
- jspdf
- pdfkit
- qrcode

### Frontend (já adicionadas)
- @mui/material
- @mui/x-date-pickers
- date-fns
- lottie-react

## Configuração OAuth2

### Variáveis de Ambiente Necessárias
```env
GOOGLE_CLIENT_ID=your_client_id
GOOGLE_CLIENT_SECRET=your_client_secret
GOOGLE_REDIRECT_URI=http://localhost:3001/api/residence/auth/callback
JWT_SECRET=your_jwt_secret
```

### Configuração Google Cloud Console
1. Habilitar Google Maps Timeline API
2. Configurar OAuth2 consent screen
3. Adicionar domínio autorizado

## Status da Integração

- ✅ **Concluído**: Estrutura básica, rotas principais, componentes base
- 🔄 **Em Progresso**: Migração de componentes avançados
- ⏳ **Pendente**: Testes finais, limpeza, documentação

## Próxima Ação
Executar migração dos componentes restantes e testar integração completa.
