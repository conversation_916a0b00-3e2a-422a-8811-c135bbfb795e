import { NextRequest, NextResponse } from 'next/server';
import { generateCertificateNumber, formatDate } from '@/utils/validation';
import { ResidenceCertificate } from '@/types';
import jwt from 'jsonwebtoken';
import { ENV } from '@/lib/config';
import jsPDF from 'jspdf';

// Middleware para verificar autenticação
function verifyToken(request: NextRequest) {
  const token = request.cookies.get('auth-token')?.value;
  
  if (!token) {
    throw new Error('Token não encontrado');
  }

  try {
    const decoded = jwt.verify(token, ENV.JWT_SECRET) as any;
    return decoded;
  } catch (error) {
    throw new Error('Token inválido');
  }
}

// Função para gerar PDF do certificado
function generateCertificatePDF(certificate: ResidenceCertificate): Buffer {
  const doc = new jsPDF();
  
  // Configurações do documento
  const pageWidth = doc.internal.pageSize.getWidth();
  const pageHeight = doc.internal.pageSize.getHeight();
  
  // Cabeçalho
  doc.setFontSize(20);
  doc.setFont('helvetica', 'bold');
  doc.text('CERTIFICADO DE COMPROVAÇÃO DE RESIDÊNCIA', pageWidth / 2, 30, { align: 'center' });
  
  // Linha decorativa
  doc.setLineWidth(0.5);
  doc.line(20, 40, pageWidth - 20, 40);
  
  // Informações do certificado
  doc.setFontSize(12);
  doc.setFont('helvetica', 'normal');
  
  let yPosition = 60;
  
  doc.text(`Certificado Nº: ${certificate.certificateNumber}`, 20, yPosition);
  yPosition += 20;
  
  doc.text(`Data de Emissão: ${formatDate(certificate.issueDate)}`, 20, yPosition);
  yPosition += 10;
  
  doc.text(`Válido até: ${formatDate(certificate.validUntil)}`, 20, yPosition);
  yPosition += 20;
  
  // Declaração principal
  doc.setFont('helvetica', 'bold');
  doc.text('DECLARAMOS PARA OS DEVIDOS FINS QUE:', 20, yPosition);
  yPosition += 20;
  
  doc.setFont('helvetica', 'normal');
  const declaration = `Com base na análise de dados de localização coletados de forma autorizada 
e processados através de algoritmos de validação, foi comprovado que o usuário 
permaneceu regularmente no endereço informado durante o período noturno 
(das 18:00 às 06:00) por ${certificate.validDays} dias consecutivos.`;
  
  const lines = doc.splitTextToSize(declaration, pageWidth - 40);
  doc.text(lines, 20, yPosition);
  yPosition += lines.length * 7 + 20;
  
  // Endereço
  doc.setFont('helvetica', 'bold');
  doc.text('Endereço Validado:', 20, yPosition);
  yPosition += 10;
  
  doc.setFont('helvetica', 'normal');
  doc.text(certificate.address, 20, yPosition);
  yPosition += 20;
  
  // Período de validação
  doc.setFont('helvetica', 'bold');
  doc.text('Período de Validação:', 20, yPosition);
  yPosition += 10;
  
  doc.setFont('helvetica', 'normal');
  doc.text(
    `De ${formatDate(certificate.validationPeriod.startDate)} até ${formatDate(certificate.validationPeriod.endDate)}`,
    20,
    yPosition
  );
  yPosition += 20;
  
  // Score de validação
  doc.text(`Score de Validação: ${(certificate.validationScore * 100).toFixed(1)}%`, 20, yPosition);
  yPosition += 30;
  
  // Informações técnicas
  doc.setFontSize(10);
  doc.setFont('helvetica', 'italic');
  doc.text('Este certificado foi gerado automaticamente através de sistema de validação', 20, yPosition);
  yPosition += 7;
  doc.text('baseado em dados de geolocalização, seguindo protocolos de privacidade LGPD.', 20, yPosition);
  yPosition += 15;
  
  // QR Code (simulado - em produção usaria uma biblioteca de QR Code)
  doc.setFont('helvetica', 'normal');
  doc.text('QR Code de Verificação:', 20, yPosition);
  doc.rect(20, yPosition + 5, 30, 30); // Placeholder para QR Code
  doc.text(certificate.qrCode, 60, yPosition + 20);
  
  // Rodapé
  doc.setFontSize(8);
  doc.text(
    `Documento gerado em ${formatDate(new Date())} - Sistema de Validação de Residência`,
    pageWidth / 2,
    pageHeight - 20,
    { align: 'center' }
  );
  
  return Buffer.from(doc.output('arraybuffer'));
}

export async function POST(request: NextRequest) {
  try {
    // Verifica autenticação
    const userToken = verifyToken(request);
    
    // Parse do body da requisição
    const { validationId } = await request.json();

    if (!validationId) {
      return NextResponse.json(
        { success: false, error: 'ID da validação é obrigatório' },
        { status: 400 }
      );
    }

    // TODO: Buscar dados da validação no banco de dados
    // const validation = await getValidationById(validationId);
    
    // Mock dos dados da validação para demonstração
    const validation = {
      id: validationId,
      userId: userToken.userId,
      address: 'Rua das Flores, 123, Centro, São Paulo - SP',
      validationPeriod: {
        startDate: '2024-12-01',
        endDate: '2024-12-31',
      },
      validationScore: 0.92,
      status: 'APPROVED',
      validDays: 28,
    };

    if (validation.status !== 'APPROVED') {
      return NextResponse.json(
        { success: false, error: 'Validação não foi aprovada' },
        { status: 400 }
      );
    }

    // Gera certificado
    const certificate: ResidenceCertificate = {
      id: `cert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId: userToken.userId,
      validationId: validationId,
      certificateNumber: generateCertificateNumber(),
      issueDate: new Date().toISOString(),
      validUntil: new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000).toISOString(), // 6 meses
      address: validation.address,
      validationPeriod: validation.validationPeriod,
      validationScore: validation.validationScore,
      validDays: validation.validDays,
      qrCode: `CERT-${Date.now().toString(36).toUpperCase()}`,
      status: 'ACTIVE',
    };

    // Gera PDF
    const pdfBuffer = generateCertificatePDF(certificate);

    // TODO: Salvar certificado no banco de dados
    // await saveCertificate(certificate);

    // TODO: Salvar PDF no storage (AWS S3, etc.)
    // const pdfUrl = await uploadPDF(pdfBuffer, certificate.certificateNumber);
    // certificate.pdfUrl = pdfUrl;

    return NextResponse.json({
      success: true,
      data: {
        certificate,
        pdfSize: pdfBuffer.length,
      },
    });

  } catch (error) {
    console.error('Erro ao gerar certificado:', error);
    
    if (error instanceof Error && error.message.includes('Token')) {
      return NextResponse.json(
        { success: false, error: 'Não autorizado' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Verifica autenticação
    const userToken = verifyToken(request);
    
    const { searchParams } = new URL(request.url);
    const certificateId = searchParams.get('id');

    if (!certificateId) {
      return NextResponse.json(
        { success: false, error: 'ID do certificado é obrigatório' },
        { status: 400 }
      );
    }

    // TODO: Buscar certificado no banco de dados
    // const certificate = await getCertificateById(certificateId);

    // Mock para demonstração
    const certificate: ResidenceCertificate = {
      id: certificateId,
      userId: userToken.userId,
      validationId: 'val_123',
      certificateNumber: 'RES-ABC123-XYZ789',
      issueDate: new Date().toISOString(),
      validUntil: new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000).toISOString(),
      address: 'Rua das Flores, 123, Centro, São Paulo - SP',
      validationPeriod: {
        startDate: '2024-12-01',
        endDate: '2024-12-31',
      },
      validationScore: 0.92,
      validDays: 28,
      qrCode: 'CERT-ABC123',
      status: 'ACTIVE',
    };

    // Gera PDF para download
    const pdfBuffer = generateCertificatePDF(certificate);

    return new NextResponse(pdfBuffer, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="certificado-${certificate.certificateNumber}.pdf"`,
      },
    });

  } catch (error) {
    console.error('Erro ao baixar certificado:', error);
    
    if (error instanceof Error && error.message.includes('Token')) {
      return NextResponse.json(
        { success: false, error: 'Não autorizado' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
