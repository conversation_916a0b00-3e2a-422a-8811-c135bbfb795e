import React from 'react';
import {
  Box,
  Container,
  Grid,
  Paper,
  useTheme,
  useMediaQuery,
  Breakpoint
} from '@mui/material';

interface ResponsiveLayoutProps {
  children: React.ReactNode;
  maxWidth?: Breakpoint | false;
  spacing?: number;
  padding?: number | { xs?: number; sm?: number; md?: number; lg?: number; xl?: number };
  background?: 'default' | 'paper' | 'transparent';
  elevation?: number;
  fullHeight?: boolean;
  centerContent?: boolean;
}

const ResponsiveLayout: React.FC<ResponsiveLayoutProps> = ({
  children,
  maxWidth = 'lg',
  spacing = 3,
  padding,
  background = 'default',
  elevation = 0,
  fullHeight = false,
  centerContent = false
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));

  const getResponsivePadding = () => {
    if (typeof padding === 'number') {
      return { xs: padding, sm: padding, md: padding, lg: padding, xl: padding };
    }
    
    if (padding) {
      return padding;
    }
    
    // Padding padrão responsivo
    return {
      xs: 1,
      sm: 2,
      md: 3,
      lg: 4,
      xl: 4
    };
  };

  const getResponsiveSpacing = () => {
    if (isMobile) return Math.max(1, spacing - 1);
    if (isTablet) return spacing;
    return spacing;
  };

  const containerProps = {
    maxWidth,
    sx: {
      py: getResponsivePadding(),
      px: getResponsivePadding(),
      ...(fullHeight && {
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column' as const
      }),
      ...(centerContent && {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      })
    }
  };

  const content = (
    <Box
      sx={{
        ...(fullHeight && {
          flex: 1,
          display: 'flex',
          flexDirection: 'column'
        })
      }}
    >
      {React.Children.map(children, (child, index) => {
        if (React.isValidElement(child) && child.type === ResponsiveGrid) {
          return React.cloneElement(child, {
            spacing: getResponsiveSpacing(),
            ...child.props
          });
        }
        return child;
      })}
    </Box>
  );

  if (background === 'paper') {
    return (
      <Paper
        elevation={elevation}
        sx={{
          ...(fullHeight && { minHeight: '100vh' }),
          borderRadius: { xs: 0, sm: 2 }
        }}
      >
        <Container {...containerProps}>
          {content}
        </Container>
      </Paper>
    );
  }

  if (background === 'transparent') {
    return (
      <Box
        sx={{
          ...(fullHeight && { minHeight: '100vh' }),
          ...containerProps.sx
        }}
      >
        {content}
      </Box>
    );
  }

  return (
    <Container {...containerProps}>
      {content}
    </Container>
  );
};

interface ResponsiveGridProps {
  children: React.ReactNode;
  spacing?: number;
  alignItems?: 'flex-start' | 'center' | 'flex-end' | 'stretch' | 'baseline';
  justifyContent?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';
  direction?: 'row' | 'column' | 'row-reverse' | 'column-reverse';
  wrap?: 'nowrap' | 'wrap' | 'wrap-reverse';
}

const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  spacing = 3,
  alignItems = 'stretch',
  justifyContent = 'flex-start',
  direction = 'row',
  wrap = 'wrap'
}) => {
  return (
    <Grid
      container
      spacing={spacing}
      alignItems={alignItems}
      justifyContent={justifyContent}
      direction={direction}
      wrap={wrap}
      sx={{
        '& .MuiGrid-item': {
          display: 'flex',
          flexDirection: 'column'
        }
      }}
    >
      {children}
    </Grid>
  );
};

interface ResponsiveGridItemProps {
  children: React.ReactNode;
  xs?: boolean | 'auto' | number;
  sm?: boolean | 'auto' | number;
  md?: boolean | 'auto' | number;
  lg?: boolean | 'auto' | number;
  xl?: boolean | 'auto' | number;
  order?: number | { xs?: number; sm?: number; md?: number; lg?: number; xl?: number };
  alignSelf?: 'auto' | 'flex-start' | 'center' | 'flex-end' | 'stretch' | 'baseline';
  fullHeight?: boolean;
}

const ResponsiveGridItem: React.FC<ResponsiveGridItemProps> = ({
  children,
  xs = 12,
  sm,
  md,
  lg,
  xl,
  order,
  alignSelf,
  fullHeight = false
}) => {
  return (
    <Grid
      item
      xs={xs}
      sm={sm}
      md={md}
      lg={lg}
      xl={xl}
      sx={{
        ...(order && (typeof order === 'number' ? { order } : { order })),
        ...(alignSelf && { alignSelf }),
        ...(fullHeight && { height: '100%' })
      }}
    >
      <Box
        sx={{
          height: fullHeight ? '100%' : 'auto',
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        {children}
      </Box>
    </Grid>
  );
};

interface ResponsiveCardProps {
  children: React.ReactNode;
  elevation?: number;
  padding?: number | { xs?: number; sm?: number; md?: number; lg?: number; xl?: number };
  fullHeight?: boolean;
  hover?: boolean;
  onClick?: () => void;
}

const ResponsiveCard: React.FC<ResponsiveCardProps> = ({
  children,
  elevation = 1,
  padding,
  fullHeight = false,
  hover = false,
  onClick
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const getCardPadding = () => {
    if (typeof padding === 'number') {
      return padding;
    }
    
    if (padding) {
      return padding;
    }
    
    return isMobile ? 2 : 3;
  };

  return (
    <Paper
      elevation={elevation}
      onClick={onClick}
      sx={{
        p: getCardPadding(),
        height: fullHeight ? '100%' : 'auto',
        display: 'flex',
        flexDirection: 'column',
        borderRadius: 2,
        transition: theme.transitions.create(['elevation', 'transform'], {
          duration: theme.transitions.duration.short
        }),
        ...(hover && {
          '&:hover': {
            elevation: elevation + 2,
            transform: 'translateY(-2px)'
          }
        }),
        ...(onClick && {
          cursor: 'pointer'
        })
      }}
    >
      {children}
    </Paper>
  );
};

interface ResponsiveStackProps {
  children: React.ReactNode;
  spacing?: number | { xs?: number; sm?: number; md?: number; lg?: number; xl?: number };
  direction?: 'row' | 'column' | { xs?: 'row' | 'column'; sm?: 'row' | 'column'; md?: 'row' | 'column'; lg?: 'row' | 'column'; xl?: 'row' | 'column' };
  alignItems?: 'flex-start' | 'center' | 'flex-end' | 'stretch' | 'baseline';
  justifyContent?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';
  divider?: React.ReactElement;
  wrap?: boolean;
}

const ResponsiveStack: React.FC<ResponsiveStackProps> = ({
  children,
  spacing = 2,
  direction = 'column',
  alignItems = 'stretch',
  justifyContent = 'flex-start',
  divider,
  wrap = false
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const getSpacing = () => {
    if (typeof spacing === 'number') {
      return isMobile ? Math.max(1, spacing - 1) : spacing;
    }
    // Para objetos responsivos, retorna um valor padrão
    return 2;
  };

  const getDirection = () => {
    if (typeof direction === 'string') {
      return direction;
    }
    
    // Responsive direction
    if (isMobile && direction.xs) return direction.xs;
    return direction.sm || direction.md || direction.lg || direction.xl || 'column';
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: getDirection(),
        alignItems,
        justifyContent,
        gap: theme.spacing(typeof getSpacing() === 'number' ? getSpacing() : 2),
        ...(wrap && { flexWrap: 'wrap' })
      }}
    >
      {divider
        ? React.Children.toArray(children).reduce((acc: React.ReactNode[], child, index) => {
            if (index > 0) {
              acc.push(React.cloneElement(divider, { key: `divider-${index}` }));
            }
            acc.push(child);
            return acc;
          }, [])
        : children}
    </Box>
  );
};

// Exportar componentes
ResponsiveLayout.Grid = ResponsiveGrid;
ResponsiveLayout.GridItem = ResponsiveGridItem;
ResponsiveLayout.Card = ResponsiveCard;
ResponsiveLayout.Stack = ResponsiveStack;

export default ResponsiveLayout;
export { ResponsiveGrid, ResponsiveGridItem, ResponsiveCard, ResponsiveStack };
