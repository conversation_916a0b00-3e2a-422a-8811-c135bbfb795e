const { Pool } = require('pg');

// Logger simplificado usando console
const logger = {
  info: (msg, data = {}) => console.log(`[INFO] ${msg}`, data),
  error: (msg, data = {}) => console.error(`[ERROR] ${msg}`, data),
  warn: (msg, data = {}) => console.warn(`[WARN] ${msg}`, data),
  debug: (msg, data = {}) => console.debug(`[DEBUG] ${msg}`, data)
};

class PostgreSQLAdapter {
  constructor() {
    this.pool = new Pool({
      host: process.env.POSTGRES_HOST || 'postgres',
      port: process.env.POSTGRES_PORT || 5432,
      database: process.env.POSTGRES_DB || 'cartorio_db',
      user: process.env.POSTGRES_USER || 'cartorio_user',
      password: process.env.POSTGRES_PASSWORD || 'cartorio_secure_pass',
      max: 20, // máximo de conexões no pool
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
      ssl: false // Desabilitado para desenvolvimento local
    });

    this.pool.on('error', (err) => {
      logger.error('Erro inesperado no pool PostgreSQL:', err);
    });

    this.pool.on('connect', () => {
      logger.info('Nova conexão PostgreSQL estabelecida');
    });
  }

  /**
   * Inicializar conexão e verificar saúde
   */
  async initialize() {
    try {
      const client = await this.pool.connect();
      const result = await client.query('SELECT NOW() as current_time, version() as version');
      client.release();
      
      logger.info('PostgreSQL conectado com sucesso:', {
        time: result.rows[0].current_time,
        version: result.rows[0].version.split(' ')[0]
      });
      
      return true;
    } catch (error) {
      logger.error('Erro ao conectar PostgreSQL:', error);
      throw error;
    }
  }

  /**
   * Executar query com parâmetros
   */
  async query(text, params = []) {
    const start = Date.now();
    try {
      const result = await this.pool.query(text, params);
      const duration = Date.now() - start;
      
      logger.debug('Query executada:', {
        text: text.substring(0, 100),
        duration,
        rows: result.rowCount
      });
      
      return result;
    } catch (error) {
      logger.error('Erro na query PostgreSQL:', {
        text: text.substring(0, 100),
        params,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Executar transação
   */
  async transaction(callback) {
    const client = await this.pool.connect();
    try {
      await client.query('BEGIN');
      const result = await callback(client);
      await client.query('COMMIT');
      return result;
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  // ===== MÉTODOS PARA GRAVAÇÕES =====

  /**
   * Criar nova gravação
   */
  async createRecording(recordingData) {
    const query = `
      INSERT INTO recordings (
        filename, file_path, file_size, duration, hash, user_id, user_name,
        consent_timestamp, consent_ip, consent_user_agent, consent_name, 
        consent_document, consent_purpose, consent_given, device_info, 
        orientation, additional_data
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
      RETURNING id, created_at
    `;

    const values = [
      recordingData.filename,
      recordingData.file_path,
      recordingData.file_size,
      recordingData.duration,
      recordingData.hash,
      recordingData.user_id,
      recordingData.user_name,
      recordingData.consent_timestamp,
      recordingData.consent_ip,
      recordingData.consent_user_agent,
      recordingData.consent_name,
      recordingData.consent_document,
      recordingData.consent_purpose,
      recordingData.consent_given,
      JSON.stringify(recordingData.device_info || {}),
      recordingData.orientation,
      JSON.stringify(recordingData.additional_data || {})
    ];

    const result = await this.query(query, values);
    return result.rows[0];
  }

  /**
   * Buscar gravação por ID
   */
  async getRecordingById(id) {
    const query = `
      SELECT r.*, u.full_name as user_full_name
      FROM recordings r
      LEFT JOIN users u ON r.user_id = u.id
      WHERE r.id = $1 AND r.deleted_at IS NULL
    `;
    
    const result = await this.query(query, [id]);
    return result.rows[0];
  }

  /**
   * Listar gravações com paginação
   */
  async getRecordings(options = {}) {
    const {
      page = 1,
      limit = 20,
      userId,
      startDate,
      endDate,
      search
    } = options;

    const offset = (page - 1) * limit;
    let whereConditions = ['r.deleted_at IS NULL'];
    let params = [];
    let paramCount = 0;

    if (userId) {
      paramCount++;
      whereConditions.push(`r.user_id = $${paramCount}`);
      params.push(userId);
    }

    if (startDate) {
      paramCount++;
      whereConditions.push(`r.created_at >= $${paramCount}`);
      params.push(startDate);
    }

    if (endDate) {
      paramCount++;
      whereConditions.push(`r.created_at <= $${paramCount}`);
      params.push(endDate);
    }

    if (search) {
      paramCount++;
      whereConditions.push(`(r.filename ILIKE $${paramCount} OR r.consent_name ILIKE $${paramCount} OR r.consent_purpose ILIKE $${paramCount})`);
      params.push(`%${search}%`);
    }

    const whereClause = whereConditions.join(' AND ');

    // Query principal
    const query = `
      SELECT r.*, u.full_name as user_full_name,
             COUNT(*) OVER() as total_count
      FROM recordings r
      LEFT JOIN users u ON r.user_id = u.id
      WHERE ${whereClause}
      ORDER BY r.created_at DESC
      LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `;

    params.push(limit, offset);
    const result = await this.query(query, params);

    return {
      recordings: result.rows,
      totalCount: result.rows[0]?.total_count || 0,
      page,
      limit,
      totalPages: Math.ceil((result.rows[0]?.total_count || 0) / limit)
    };
  }

  /**
   * Atualizar gravação
   */
  async updateRecording(id, updateData) {
    const fields = [];
    const values = [];
    let paramCount = 0;

    Object.keys(updateData).forEach(key => {
      if (updateData[key] !== undefined) {
        paramCount++;
        fields.push(`${key} = $${paramCount}`);
        values.push(updateData[key]);
      }
    });

    if (fields.length === 0) {
      throw new Error('Nenhum campo para atualizar');
    }

    paramCount++;
    values.push(id);

    const query = `
      UPDATE recordings 
      SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE id = $${paramCount} AND deleted_at IS NULL
      RETURNING *
    `;

    const result = await this.query(query, values);
    return result.rows[0];
  }

  /**
   * Salvar gravação
   */
  async saveRecording(recordingData) {
    const query = `
      INSERT INTO recordings (
        user_id, filename, original_filename, file_path, file_size, 
        duration, mime_type, file_hash, consent_name, consent_document, 
        consent_purpose, consent_timestamp, consent_ip, metadata
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
      RETURNING *
    `;

    const values = [
      recordingData.user_id,
      recordingData.filename,
      recordingData.original_filename,
      recordingData.file_path,
      recordingData.file_size,
      recordingData.duration,
      recordingData.mime_type,
      recordingData.file_hash,
      recordingData.consent_name,
      recordingData.consent_document,
      recordingData.consent_purpose,
      recordingData.consent_timestamp,
      recordingData.consent_ip,
      JSON.stringify(recordingData.metadata || {})
    ];

    const result = await this.query(query, values);
    return result.rows[0];
  }

  /**
   * Deletar gravação (soft delete)
   */
  async deleteRecording(id) {
    const query = `
      UPDATE recordings 
      SET deleted_at = CURRENT_TIMESTAMP
      WHERE id = $1 AND deleted_at IS NULL
      RETURNING id
    `;

    const result = await this.query(query, [id]);
    return result.rows[0];
  }

  /**
   * Salvar relatório
   */
  async saveReport(reportData) {
    const query = `
      INSERT INTO reports (
        recording_id, user_id, report_type, title, content, 
        format, file_path, metadata, generated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      RETURNING *
    `;

    const values = [
      reportData.recording_id,
      reportData.user_id,
      reportData.report_type,
      reportData.title,
      reportData.content,
      reportData.format,
      reportData.file_path,
      JSON.stringify(reportData.metadata || {}),
      reportData.generated_at || new Date()
    ];

    const result = await this.query(query, values);
    return result.rows[0];
  }

  /**
   * Atualizar informações do cartório no relatório
   */
  async updateNotaryInfo(reportId, notaryInfo) {
    const query = `
      UPDATE reports 
      SET notary_info = $2, updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `;

    const values = [
      reportId,
      JSON.stringify(notaryInfo)
    ];

    const result = await this.query(query, values);
    return result.rows[0];
  }

  // ===== MÉTODOS PARA USUÁRIOS =====

  /**
   * Criar usuário
   */
  async createUser(userData) {
    const query = `
      INSERT INTO users (username, email, password_hash, full_name, role)
      VALUES ($1, $2, crypt($3, gen_salt('bf')), $4, $5)
      RETURNING id, username, email, full_name, role, created_at
    `;

    const values = [
      userData.username,
      userData.email,
      userData.password,
      userData.full_name,
      userData.role || 'visualizador'
    ];

    const result = await this.query(query, values);
    return result.rows[0];
  }

  /**
   * Buscar usuário por username/email
   */
  async getUserByCredentials(identifier) {
    const query = `
      SELECT id, username, email, password_hash, full_name, role, is_active, last_login
      FROM users 
      WHERE (username = $1 OR email = $1) AND is_active = true
    `;

    const result = await this.query(query, [identifier]);
    return result.rows[0];
  }

  /**
   * Atualizar último login
   */
  async updateLastLogin(userId) {
    const query = `
      UPDATE users 
      SET last_login = CURRENT_TIMESTAMP
      WHERE id = $1
    `;

    await this.query(query, [userId]);
  }

  // ===== MÉTODOS PARA SESSÕES JWT =====

  /**
   * Criar sessão
   */
  async createSession(sessionData) {
    const query = `
      INSERT INTO user_sessions (
        user_id, token_hash, refresh_token_hash, expires_at, 
        refresh_expires_at, ip_address, user_agent
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING id, created_at
    `;

    const values = [
      sessionData.user_id,
      sessionData.token_hash,
      sessionData.refresh_token_hash,
      sessionData.expires_at,
      sessionData.refresh_expires_at,
      sessionData.ip_address,
      sessionData.user_agent
    ];

    const result = await this.query(query, values);
    return result.rows[0];
  }

  /**
   * Validar sessão
   */
  async validateSession(tokenHash) {
    const query = `
      SELECT s.*, u.username, u.email, u.full_name, u.role, u.is_active
      FROM user_sessions s
      JOIN users u ON s.user_id = u.id
      WHERE s.token_hash = $1 
        AND s.is_active = true 
        AND s.expires_at > CURRENT_TIMESTAMP
        AND u.is_active = true
    `;

    const result = await this.query(query, [tokenHash]);
    return result.rows[0];
  }

  /**
   * Invalidar sessão
   */
  async invalidateSession(tokenHash) {
    const query = `
      UPDATE user_sessions 
      SET is_active = false
      WHERE token_hash = $1
    `;

    await this.query(query, [tokenHash]);
  }

  // ===== MÉTODOS PARA AUDITORIA =====

  /**
   * Registrar log de auditoria
   */
  async logAudit(auditData) {
    const query = `
      INSERT INTO audit_logs (
        user_id, action, resource_type, resource_id, 
        old_values, new_values, ip_address, user_agent
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING id, created_at
    `;

    const values = [
      auditData.user_id,
      auditData.action,
      auditData.resource_type,
      auditData.resource_id,
      JSON.stringify(auditData.old_values || {}),
      JSON.stringify(auditData.new_values || {}),
      auditData.ip_address,
      auditData.user_agent
    ];

    const result = await this.query(query, values);
    return result.rows[0];
  }

  // ===== MÉTODOS DE SAÚDE E ESTATÍSTICAS =====

  /**
   * Verificar saúde do banco
   */
  async healthCheck() {
    try {
      const result = await this.query('SELECT 1 as health');
      return result.rows[0].health === 1;
    } catch (error) {
      return false;
    }
  }

  /**
   * Obter gravações por período
   */
  async getRecordingsByPeriod(period = 'month') {
    try {
      let dateFilter = '';
      
      switch (period) {
        case 'day':
          dateFilter = "WHERE created_at >= CURRENT_DATE AND deleted_at IS NULL";
          break;
        case 'week':
          dateFilter = "WHERE created_at >= CURRENT_DATE - INTERVAL '7 days' AND deleted_at IS NULL";
          break;
        case 'month':
          dateFilter = "WHERE created_at >= CURRENT_DATE - INTERVAL '30 days' AND deleted_at IS NULL";
          break;
        case 'year':
          dateFilter = "WHERE created_at >= CURRENT_DATE - INTERVAL '365 days' AND deleted_at IS NULL";
          break;
        default:
          dateFilter = "WHERE created_at >= CURRENT_DATE - INTERVAL '30 days' AND deleted_at IS NULL";
      }

      const result = await this.query(`
        SELECT 
          id,
          filename,
          file_path,
          duration,
          file_size,
          status,
          created_at,
          updated_at,
          metadata
        FROM recordings 
        ${dateFilter}
        ORDER BY created_at DESC
      `);

      return result.rows;
    } catch (error) {
      logger.error('Erro ao obter gravações por período:', error);
      throw error;
    }
  }

  /**
   * Obter estatísticas gerais
   */
  async getStats() {
    try {
      const queries = [
        // Estatísticas básicas
        'SELECT COUNT(*) as total_recordings FROM recordings WHERE deleted_at IS NULL',
        'SELECT COUNT(*) as total_users FROM users WHERE is_active = true',
        'SELECT COUNT(*) as active_sessions FROM user_sessions WHERE is_active = true AND expires_at > CURRENT_TIMESTAMP',

        // Estatísticas de gravações por status
        'SELECT COUNT(*) as completed FROM recordings WHERE status = \'completed\' AND deleted_at IS NULL',
        'SELECT COUNT(*) as processing FROM recordings WHERE status = \'processing\' AND deleted_at IS NULL',
        'SELECT COUNT(*) as failed FROM recordings WHERE status = \'failed\' AND deleted_at IS NULL',
        'SELECT COUNT(*) as archived FROM recordings WHERE status = \'archived\' AND deleted_at IS NULL',

        // Estatísticas de tamanho e duração
        'SELECT COALESCE(SUM(file_size), 0) as total_size FROM recordings WHERE deleted_at IS NULL',
        'SELECT COALESCE(AVG(duration), 0) as avg_duration FROM recordings WHERE duration > 0 AND deleted_at IS NULL',

        // Primeira e última gravação
        'SELECT MIN(created_at) as first_recording FROM recordings WHERE deleted_at IS NULL',
        'SELECT MAX(created_at) as last_recording FROM recordings WHERE deleted_at IS NULL',

        // Estatísticas de IA (com fallback para tabelas que podem não existir)
        'SELECT COUNT(*) as total_transcriptions FROM ai_transcriptions',
        'SELECT COUNT(*) as total_embeddings FROM ai_embeddings'
      ];

      // Executar queries com tratamento de erro individual
      const results = await Promise.allSettled(queries.map(query => this.query(query)));

      // Processar resultados com fallbacks
      const safeGetResult = (index, field, defaultValue = 0) => {
        if (results[index].status === 'fulfilled' && results[index].value.rows[0]) {
          return results[index].value.rows[0][field] || defaultValue;
        }
        return defaultValue;
      };

      return {
        totalRecordings: parseInt(safeGetResult(0, 'total_recordings', 0)),
        totalUsers: parseInt(safeGetResult(1, 'total_users', 0)),
        activeSessions: parseInt(safeGetResult(2, 'active_sessions', 0)),
        completedRecordings: parseInt(safeGetResult(3, 'completed', 0)),
        processingRecordings: parseInt(safeGetResult(4, 'processing', 0)),
        failedRecordings: parseInt(safeGetResult(5, 'failed', 0)),
        archivedRecordings: parseInt(safeGetResult(6, 'archived', 0)),
        totalSize: parseInt(safeGetResult(7, 'total_size', 0)),
        avgDuration: parseFloat(safeGetResult(8, 'avg_duration', 0)),
        firstRecording: safeGetResult(9, 'first_recording', null),
        lastRecording: safeGetResult(10, 'last_recording', null),
        totalTranscriptions: parseInt(safeGetResult(11, 'total_transcriptions', 0)),
        totalEmbeddings: parseInt(safeGetResult(12, 'total_embeddings', 0))
      };
    } catch (error) {
      logger.error('Erro ao obter estatísticas:', error);

      // Retornar estatísticas padrão em caso de erro
      return {
        totalRecordings: 0,
        totalUsers: 0,
        activeSessions: 0,
        completedRecordings: 0,
        processingRecordings: 0,
        failedRecordings: 0,
        archivedRecordings: 0,
        totalSize: 0,
        avgDuration: 0,
        firstRecording: null,
        lastRecording: null,
        totalTranscriptions: 0,
        totalEmbeddings: 0
      };
    }
  }

  /**
   * Fechar pool de conexões
   */
  async close() {
    await this.pool.end();
    logger.info('Pool PostgreSQL fechado');
  }
}

// Singleton instance
let postgresInstance = null;

module.exports = {
  initialize: async () => {
    if (!postgresInstance) {
      postgresInstance = new PostgreSQLAdapter();
      await postgresInstance.initialize();
    }
    return postgresInstance;
  },
  getInstance: () => postgresInstance
};
