FROM node:18-alpine

WORKDIR /app

COPY package*.json ./

RUN npm install

COPY . .

# Criar diretórios de armazenamento necessários
RUN mkdir -p /app/storage/recordings \
    && mkdir -p /app/storage/temp \
    && mkdir -p /app/storage/certificates \
    && mkdir -p /app/storage/signatures \
    && mkdir -p /app/storage/backups \
    && chown -R node:node /app/storage

# Criar script de inicialização para migração automática
RUN echo '#!/bin/sh' > /app/init-storage.sh \
    && echo 'echo "🔄 Verificando estrutura de armazenamento..."' >> /app/init-storage.sh \
    && echo 'mkdir -p /app/storage/recordings' >> /app/init-storage.sh \
    && echo 'mkdir -p /app/storage/temp' >> /app/init-storage.sh \
    && echo 'mkdir -p /app/storage/certificates' >> /app/init-storage.sh \
    && echo 'mkdir -p /app/storage/signatures' >> /app/init-storage.sh \
    && echo 'mkdir -p /app/storage/backups' >> /app/init-storage.sh \
    && echo 'chown -R node:node /app/storage' >> /app/init-storage.sh \
    && echo 'echo "✅ Estrutura de armazenamento verificada"' >> /app/init-storage.sh \
    && echo 'exec "$@"' >> /app/init-storage.sh \
    && chmod +x /app/init-storage.sh

VOLUME /app/storage

EXPOSE 3001

# Usar script de inicialização como entrypoint
ENTRYPOINT ["/app/init-storage.sh"]
CMD ["node", "src/server.js"]
