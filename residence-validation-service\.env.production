# ==============================================================================
# VARIÁVEIS DE AMBIENTE PARA PRODUÇÃO
# ==============================================================================

# IMPORTANTE: Substitua todos os valores por valores reais de produção
# Nunca commite este arquivo com valores reais!

# Configurações do Google OAuth2
GOOGLE_CLIENT_ID=seu_google_client_id_de_producao
GOOGLE_CLIENT_SECRET=seu_google_client_secret_de_producao
NEXT_PUBLIC_GOOGLE_REDIRECT_URI=https://seu-dominio.com/api/auth/callback

# JWT Secret (use um valor complexo e único)
JWT_SECRET=sua_chave_jwt_super_secreta_e_complexa_para_producao_123456789

# Chave de criptografia (exatamente 32 caracteres)
ENCRYPTION_KEY=sua_chave_de_32_caracteres_aqui!!

# URL base da aplicação
NEXT_PUBLIC_BASE_URL=https://seu-dominio.com

# Configurações do banco de dados PostgreSQL
DATABASE_URL=*************************************************************/residencia_db
POSTGRES_PASSWORD=senha_super_segura_do_postgres

# Redis
REDIS_URL=redis://:senha_redis@redis:6379
REDIS_PASSWORD=senha_redis_super_segura

# Configurações de ambiente
NODE_ENV=production
NEXT_TELEMETRY_DISABLED=1

# Configurações de segurança
CORS_ORIGIN=https://seu-dominio.com
SESSION_SECRET=sua_session_secret_super_complexa

# Configurações de email (opcional - para notificações)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=sua_senha_de_app

# Configurações de storage (AWS S3 ou similar)
AWS_ACCESS_KEY_ID=sua_aws_access_key
AWS_SECRET_ACCESS_KEY=sua_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=sistema-residencia-certificados

# Configurações de monitoramento
SENTRY_DSN=https://sua-sentry-dsn.ingest.sentry.io/projeto
LOG_LEVEL=info

# Rate Limiting
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW_MS=900000

# Configurações LGPD
DATA_RETENTION_DAYS=90
CONSENT_REQUIRED=true
