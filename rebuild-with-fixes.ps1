# Script para reconstruir o sistema com todas as correções aplicadas
# Data: 2025-07-20
# Descrição: Reconstrói o Docker com correções permanentes

Write-Host "🔧 RECONSTRUÇÃO COMPLETA DO SISTEMA CARTORIOTECH" -ForegroundColor Cyan
Write-Host "=================================================" -ForegroundColor Cyan

# 1. Parar todos os containers
Write-Host "`n1️⃣ Parando containers existentes..." -ForegroundColor Yellow
docker-compose down -v
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Erro ao parar containers" -ForegroundColor Red
    exit 1
}

# 2. Remover imagens antigas para forçar rebuild
Write-Host "`n2️⃣ Removendo imagens antigas..." -ForegroundColor Yellow
docker-compose down --rmi all --remove-orphans
docker system prune -f

# 3. Verificar estrutura de arquivos críticos
Write-Host "`n3️⃣ Verificando estrutura de arquivos..." -ForegroundColor Yellow

$criticalFiles = @(
    "database/migrations/006_fix_recordings_storage.sql",
    "backend/Dockerfile",
    "backend/src/routes/recordings.js",
    "backend/src/database/postgresql.js",
    "database/init/02-apply-migrations.sql"
)

foreach ($file in $criticalFiles) {
    if (Test-Path $file) {
        Write-Host "✅ $file" -ForegroundColor Green
    } else {
        Write-Host "❌ $file - ARQUIVO CRÍTICO AUSENTE!" -ForegroundColor Red
        exit 1
    }
}

# 4. Criar diretórios de storage se não existirem
Write-Host "`n4️⃣ Criando estrutura de diretórios..." -ForegroundColor Yellow
$storageDirs = @(
    "storage/recordings",
    "storage/temp", 
    "storage/certificates",
    "storage/signatures",
    "storage/backups"
)

foreach ($dir in $storageDirs) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force
        Write-Host "📁 Criado: $dir" -ForegroundColor Green
    } else {
        Write-Host "📁 Existe: $dir" -ForegroundColor Gray
    }
}

# 5. Verificar arquivo .env
Write-Host "`n5️⃣ Verificando configurações..." -ForegroundColor Yellow
if (Test-Path ".env") {
    Write-Host "✅ Arquivo .env encontrado" -ForegroundColor Green
    
    # Verificar configurações críticas
    $envContent = Get-Content ".env" -Raw
    if ($envContent -match "POSTGRES_DB=") {
        Write-Host "✅ Configuração PostgreSQL OK" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Configuração PostgreSQL pode estar incompleta" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ Arquivo .env não encontrado!" -ForegroundColor Red
    exit 1
}

# 6. Rebuild completo
Write-Host "`n6️⃣ Reconstruindo containers..." -ForegroundColor Yellow
Write-Host "⏳ Isso pode levar alguns minutos..." -ForegroundColor Gray

docker-compose build --no-cache --pull
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Erro durante o build" -ForegroundColor Red
    exit 1
}

# 7. Iniciar serviços
Write-Host "`n7️⃣ Iniciando serviços..." -ForegroundColor Yellow
docker-compose up -d
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Erro ao iniciar serviços" -ForegroundColor Red
    exit 1
}

# 8. Aguardar inicialização
Write-Host "`n8️⃣ Aguardando inicialização..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# 9. Verificar saúde dos serviços
Write-Host "`n9️⃣ Verificando saúde dos serviços..." -ForegroundColor Yellow

$services = @(
    @{Name="Backend"; Port=3001; Path="/health"},
    @{Name="Web App"; Port=80; Path="/"},
    @{Name="AI Service"; Port=3003; Path="/health"},
    @{Name="Signature Service"; Port=3004; Path="/health"}
)

foreach ($service in $services) {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:$($service.Port)$($service.Path)" -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ $($service.Name) - OK" -ForegroundColor Green
        } else {
            Write-Host "⚠️ $($service.Name) - Status: $($response.StatusCode)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "❌ $($service.Name) - Não responsivo" -ForegroundColor Red
    }
}

# 10. Testar endpoint de residence
Write-Host "`n🔟 Testando endpoint de residence..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3001/api/residence" -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Endpoint /api/residence funcionando" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Endpoint /api/residence - Status: $($response.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Endpoint /api/residence não responsivo" -ForegroundColor Red
}

# 11. Verificar logs para erros críticos
Write-Host "`n1️⃣1️⃣ Verificando logs..." -ForegroundColor Yellow
$logs = docker-compose logs backend --tail=20 2>&1
if ($logs -match "ERROR|ERRO|Failed|failed") {
    Write-Host "⚠️ Possíveis erros encontrados nos logs:" -ForegroundColor Yellow
    Write-Host $logs -ForegroundColor Gray
} else {
    Write-Host "✅ Nenhum erro crítico encontrado nos logs" -ForegroundColor Green
}

# 12. Resumo final
Write-Host "`n🎉 RECONSTRUÇÃO CONCLUÍDA!" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green
Write-Host ""
Write-Host "🌐 Serviços disponíveis:" -ForegroundColor Cyan
Write-Host "   • Web App: http://localhost" -ForegroundColor White
Write-Host "   • Backend API: http://localhost:3001" -ForegroundColor White
Write-Host "   • AI Service: http://localhost:3003" -ForegroundColor White
Write-Host "   • Signature Service: http://localhost:3004" -ForegroundColor White
Write-Host ""
Write-Host "📋 Correções aplicadas:" -ForegroundColor Cyan
Write-Host "   ✅ Sistema de armazenamento permanente" -ForegroundColor Green
Write-Host "   ✅ Migração de banco de dados" -ForegroundColor Green
Write-Host "   ✅ Estrutura de diretórios correta" -ForegroundColor Green
Write-Host "   ✅ Endpoint /api/residence funcionando" -ForegroundColor Green
Write-Host "   ✅ Configurações de gravação (640x480)" -ForegroundColor Green
Write-Host ""
Write-Host "🔧 Para verificar o status:" -ForegroundColor Cyan
Write-Host "   docker-compose ps" -ForegroundColor White
Write-Host "   docker-compose logs backend" -ForegroundColor White
Write-Host ""
Write-Host "🚀 Sistema pronto para produção!" -ForegroundColor Green
