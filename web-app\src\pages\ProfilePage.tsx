// ProfilePage.tsx
import React, { useState, useEffect, useReducer, useCallback } from 'react';
import {
  Container, Typography, Box, Grid, Card, CardContent, CardHeader,
  TextField, Button, Avatar, Divider, List, ListItem, ListItemIcon,
  ListItemText, Chip, Alert, Dialog, DialogTitle, DialogContent,
  DialogActions, Tabs, Tab, Switch, FormControlLabel, FormControl,
  InputLabel, Select, MenuItem, Table, TableBody, TableCell,
  TableContainer, TableHead, TableRow, Paper, IconButton, LinearProgress
} from '@mui/material';
import {
  Person, Email, Phone, Business, Security, History, Save, Edit,
  PhotoCamera, Key, Settings, Notifications, Language, Palette, Shield,
  VpnKey, Smartphone, Computer, Delete, Timeline, CloudDownload, QrCode,
  LightMode, DarkMode
} from '@mui/icons-material';
import { useToast } from '../components/Toast';
import * as yup from 'yup';

// Interfaces
interface UserProfile {
  id: string;
  name: string;
  email: string;
  phone: string;
  company: string;
  role: string;
  avatar?: string;
  createdAt: string;
  lastLogin: string;
}

interface ActivityLog {
  id: string;
  action: string;
  timestamp: string;
  details: string;
}

interface Preferences {
  theme: 'light' | 'dark' | 'auto';
  language: 'pt-BR' | 'en-US' | 'es-ES';
  notifications: {
    email: boolean;
    push: boolean;
    sms: boolean;
  };
  privacy: {
    showEmail: boolean;
    showPhone: boolean;
    allowAnalytics: boolean;
  };
  accessibility: {
    fontSize: 'small' | 'medium' | 'large';
    highContrast: boolean;
    screenReader: boolean;
  };
}

interface SecuritySettings {
  twoFactorEnabled: boolean;
  loginAlerts: boolean;
  sessionTimeout: number;
  trustedDevices: Array<{
    id: string;
    name: string;
    lastUsed: string;
    location: string;
  }>;
}

interface DataExport {
  isExporting: boolean;
  lastExport: string | null;
  exportTypes: {
    profile: boolean;
    activities: boolean;
    recordings: boolean;
    settings: boolean;
  };
}

interface Session {
  id: string;
  device: string;
  location: string;
  lastActive: string;
  current: boolean;
}

interface PasswordData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

interface ProfileState {
  profile: UserProfile;
  editedProfile: UserProfile;
  errors: Partial<Record<keyof UserProfile, string>>;
  isEditing: boolean;
  isLoading: boolean;
}

type ProfileAction =
  | { type: 'SET_PROFILE'; payload: UserProfile }
  | { type: 'SET_EDITED_PROFILE'; payload: Partial<UserProfile> }
  | { type: 'SET_ERRORS'; payload: Partial<Record<keyof UserProfile, string>> }
  | { type: 'SET_EDITING'; payload: boolean }
  | { type: 'SET_LOADING'; payload: boolean };

// Validation Schema
const profileSchema = yup.object({
  name: yup.string().required('Nome é obrigatório').min(2, 'Nome muito curto'),
  email: yup.string().email('Email inválido').required('Email é obrigatório'),
  phone: yup.string().matches(/^\(\d{2}\)\s?\d{5}-?\d{4}$/, 'Telefone inválido').required('Telefone é obrigatório'),
  company: yup.string().required('Empresa é obrigatória'),
});

// Reducer for profile state
const profileReducer = (state: ProfileState, action: ProfileAction): ProfileState => {
  switch (action.type) {
    case 'SET_PROFILE':
      return { ...state, profile: action.payload, editedProfile: action.payload };
    case 'SET_EDITED_PROFILE':
      return { ...state, editedProfile: { ...state.editedProfile, ...action.payload } };
    case 'SET_ERRORS':
      return { ...state, errors: action.payload };
    case 'SET_EDITING':
      return { ...state, isEditing: action.payload };
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    default:
      return state;
  }
};

// Reusable ProfileInfo Component
const ProfileInfo: React.FC<{
  profile: UserProfile;
  editedProfile: UserProfile;
  errors: Partial<Record<keyof UserProfile, string>>;
  isEditing: boolean;
  setEditedProfile: (profile: Partial<UserProfile>) => void;
}> = React.memo(({ profile, editedProfile, errors, isEditing, setEditedProfile }) => (
  <Card className="mb-4">
    <CardHeader
      title="Informações Pessoais"
      titleTypographyProps={{ variant: 'h6' }}
      action={
        <Button
          startIcon={<Edit />}
          onClick={() => setEditedProfile({})}
          variant={isEditing ? 'outlined' : 'contained'}
          size="small"
          className="min-w-[120px]"
        >
          {isEditing ? 'Cancelar' : 'Editar'}
        </Button>
      }
    />
    <CardContent>
      <Box className="flex flex-col sm:flex-row items-center sm:items-start mb-6 gap-4">
        <Avatar
          src={profile.avatar}
          className="w-20 h-20 sm:w-24 sm:h-24 text-3xl sm:text-4xl"
          alt={`${profile.name}'s avatar`}
        >
          {profile.name.charAt(0)}
        </Avatar>
        {isEditing && (
          <Button startIcon={<PhotoCamera />} variant="outlined" size="small">
            Alterar Foto
          </Button>
        )}
      </Box>
      <Grid container spacing={2}>
        <Grid item xs={12} sm={6}>
          <TextField
            label="Nome Completo"
            fullWidth
            value={editedProfile.name}
            onChange={(e) => setEditedProfile({ name: e.target.value })}
            disabled={!isEditing}
            error={!!errors.name}
            helperText={errors.name}
            InputProps={{ startAdornment: <Person className="mr-2 text-gray-500" /> }}
            inputProps={{ 'aria-label': 'Nome Completo' }}
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <TextField
            label="Email"
            fullWidth
            value={editedProfile.email}
            onChange={(e) => setEditedProfile({ email: e.target.value })}
            disabled={!isEditing}
            error={!!errors.email}
            helperText={errors.email}
            InputProps={{ startAdornment: <Email className="mr-2 text-gray-500" /> }}
            inputProps={{ 'aria-label': 'Email' }}
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <TextField
            label="Telefone"
            fullWidth
            value={editedProfile.phone}
            onChange={(e) => setEditedProfile({ phone: e.target.value })}
            disabled={!isEditing}
            error={!!errors.phone}
            helperText={errors.phone}
            InputProps={{ startAdornment: <Phone className="mr-2 text-gray-500" /> }}
            inputProps={{ 'aria-label': 'Telefone' }}
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <TextField
            label="Empresa"
            fullWidth
            value={editedProfile.company}
            onChange={(e) => setEditedProfile({ company: e.target.value })}
            disabled={!isEditing}
            error={!!errors.company}
            helperText={errors.company}
            InputProps={{ startAdornment: <Business className="mr-2 text-gray-500" /> }}
            inputProps={{ 'aria-label': 'Empresa' }}
          />
        </Grid>
      </Grid>
    </CardContent>
  </Card>
));

// Main Component
const ProfilePage: React.FC = () => {
  const initialProfile: UserProfile = {
    id: '1',
    name: 'João Silva',
    email: '<EMAIL>',
    phone: '(11) 99999-9999',
    company: 'Cartório Central',
    role: 'Administrador',
    createdAt: '2024-01-01',
    lastLogin: '2024-01-15 10:30',
  };

  const [state, dispatch] = useReducer(profileReducer, {
    profile: initialProfile,
    editedProfile: initialProfile,
    errors: {},
    isEditing: false,
    isLoading: false,
  });

  const [activities] = useState<ActivityLog[]>([
    {
      id: '1',
      action: 'Login realizado',
      timestamp: '2024-01-15 10:30',
      details: 'Acesso via web',
    },
    {
      id: '2',
      action: 'Gravação iniciada',
      timestamp: '2024-01-15 09:15',
      details: 'Documento: Certidão de Nascimento',
    },
    {
      id: '3',
      action: 'Configurações alteradas',
      timestamp: '2024-01-14 16:45',
      details: 'Tema alterado para escuro',
    },
  ]);

  const [currentTab, setCurrentTab] = useState(0);
  const [openPasswordDialog, setOpenPasswordDialog] = useState(false);
  const [passwordData, setPasswordData] = useState<PasswordData>({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  const [preferences, setPreferences] = useState<Preferences>({
    theme: 'light',
    language: 'pt-BR',
    notifications: { email: true, push: true, sms: false },
    privacy: { showEmail: false, showPhone: false, allowAnalytics: true },
    accessibility: { fontSize: 'medium', highContrast: false, screenReader: false },
  });
  const [securitySettings, setSecuritySettings] = useState<SecuritySettings>({
    twoFactorEnabled: false,
    loginAlerts: true,
    sessionTimeout: 30,
    trustedDevices: [],
  });
  const [dataExport, setDataExport] = useState<DataExport>({
    isExporting: false,
    lastExport: null,
    exportTypes: { profile: true, activities: true, recordings: false, settings: true },
  });
  const [sessions, setSessions] = useState<Session[]>([
    {
      id: '1',
      device: 'Chrome - Windows',
      location: 'São Paulo, SP',
      lastActive: '2025-01-18 10:30',
      current: true,
    },
    {
      id: '2',
      device: 'Mobile App - Android',
      location: 'São Paulo, SP',
      lastActive: '2025-01-17 18:45',
      current: false,
    },
  ]);

  const { showSuccess, showError } = useToast();

  const validateProfile = useCallback(async (profileData: UserProfile) => {
    try {
      await profileSchema.validate(profileData, { abortEarly: false });
      dispatch({ type: 'SET_ERRORS', payload: {} });
      return true;
    } catch (err) {
      const errors = (err as yup.ValidationError).inner.reduce(
        (acc, curr) => ({ ...acc, [curr.path!]: curr.message }),
        {}
      );
      dispatch({ type: 'SET_ERRORS', payload: errors });
      return false;
    }
  }, []);

  const handleSaveProfile = useCallback(async () => {
    if (!(await validateProfile(state.editedProfile))) {
      showError('Por favor, corrija os erros nos campos');
      return;
    }
    dispatch({ type: 'SET_LOADING', payload: true });
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));
      dispatch({ type: 'SET_PROFILE', payload: state.editedProfile });
      dispatch({ type: 'SET_EDITING', payload: false });
      showSuccess('Perfil atualizado com sucesso!');
    } catch (error) {
      showError('Erro ao atualizar perfil');
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, [state.editedProfile, showError, showSuccess]);

  const handlePasswordChange = useCallback(async () => {
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      showError('As senhas não coincidem');
      return;
    }
    if (passwordData.newPassword.length < 8) {
      showError('A senha deve ter pelo menos 8 caracteres');
      return;
    }
    dispatch({ type: 'SET_LOADING', payload: true });
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));
      setOpenPasswordDialog(false);
      setPasswordData({ currentPassword: '', newPassword: '', confirmPassword: '' });
      showSuccess('Senha alterada com sucesso!');
    } catch (error) {
      showError('Erro ao alterar senha');
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, [passwordData, showError, showSuccess]);

  const handleExportData = useCallback(async () => {
    dispatch({ type: 'SET_LOADING', payload: true });
    try {
      // Simulate export
      await new Promise((resolve) => setTimeout(resolve, 2000));
      setDataExport((prev) => ({
        ...prev,
        lastExport: new Date().toLocaleString('pt-BR'),
        isExporting: false,
      }));
      showSuccess('Dados exportados com sucesso!');
    } catch (error) {
      showError('Erro ao exportar dados');
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, [showError, showSuccess]);

  return (
    <Container maxWidth="lg" className="py-8">
      {state.isLoading && <LinearProgress />}
      <Typography variant="h4" className="flex items-center mb-6">
        <Person className="mr-2" /> Meu Perfil
      </Typography>

      <Paper className="mb-6">
        <Tabs
          value={currentTab}
          onChange={(e, newValue) => setCurrentTab(newValue)}
          variant="scrollable"
          scrollButtons="auto"
          allowScrollButtonsMobile
          aria-label="Abas de navegação do perfil"
        >
          {[
            { icon: <Person />, label: 'Perfil' },
            { icon: <Settings />, label: 'Preferências' },
            { icon: <Security />, label: 'Segurança' },
            { icon: <History />, label: 'Atividades' },
            { icon: <CloudDownload />, label: 'Dados' },
          ].map((tab, index) => (
            <Tab
              key={index}
              icon={tab.icon}
              label={tab.label}
              id={`profile-tab-${index}`}
              aria-controls={`profile-tabpanel-${index}`}
              className="min-w-[100px] sm:min-w-[120px]"
            />
          ))}
        </Tabs>
      </Paper>

      {currentTab === 0 && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <ProfileInfo
              profile={state.profile}
              editedProfile={state.editedProfile}
              errors={state.errors}
              isEditing={state.isEditing}
              setEditedProfile={(updates) => dispatch({ type: 'SET_EDITED_PROFILE', payload: updates })}
            />
            {state.isEditing && (
              <Box className="mt-4 flex gap-4">
                <Button
                  variant="contained"
                  startIcon={<Save />}
                  onClick={handleSaveProfile}
                  disabled={state.isLoading}
                >
                  Salvar Alterações
                </Button>
                <Button
                  variant="outlined"
                  onClick={() => {
                    dispatch({ type: 'SET_EDITED_PROFILE', payload: state.profile });
                    dispatch({ type: 'SET_EDITING', payload: false });
                  }}
                  disabled={state.isLoading}
                >
                  Cancelar
                </Button>
              </Box>
            )}
          </Grid>
          <Grid item xs={12} md={4}>
            <Card className="mb-4">
              <CardHeader title="Informações da Conta" />
              <CardContent>
                <List>
                  <ListItem>
                    <ListItemIcon><Security /></ListItemIcon>
                    <ListItemText
                      primary="Função"
                      secondary={<Chip label={state.profile.role} color="primary" size="small" />}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Membro desde"
                      secondary={new Date(state.profile.createdAt).toLocaleDateString('pt-BR')}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="Último acesso" secondary={state.profile.lastLogin} />
                  </ListItem>
                </List>
                <Divider className="my-4" />
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<Key />}
                  onClick={() => setOpenPasswordDialog(true)}
                  aria-label="Alterar senha"
                >
                  Alterar Senha
                </Button>
              </CardContent>
            </Card>
            <Card>
              <CardHeader title="Atividades Recentes" />
              <CardContent>
                <List dense>
                  {activities.map((activity) => (
                    <ListItem key={activity.id}>
                      <ListItemIcon><History /></ListItemIcon>
                      <ListItemText
                        primary={activity.action}
                        secondary={
                          <>
                            <Typography variant="caption" display="block">
                              {activity.timestamp}
                            </Typography>
                            <Typography variant="caption" color="textSecondary">
                              {activity.details}
                            </Typography>
                          </>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {currentTab === 1 && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardHeader title="Tema e Aparência" avatar={<Palette color="primary" />} />
              <CardContent>
                <FormControl fullWidth className="mb-4">
                  <InputLabel>Tema</InputLabel>
                  <Select
                    value={preferences.theme}
                    onChange={(e) => setPreferences({ ...preferences, theme: e.target.value as Preferences['theme'] })}
                    label="Tema"
                    aria-label="Seleção de tema"
                  >
                    <MenuItem value="light"><Box className="flex items-center gap-2"><LightMode /> Claro</Box></MenuItem>
                    <MenuItem value="dark"><Box className="flex items-center gap-2"><DarkMode /> Escuro</Box></MenuItem>
                    <MenuItem value="auto"><Box className="flex items-center gap-2"><Settings /> Automático</Box></MenuItem>
                  </Select>
                </FormControl>
                <FormControl fullWidth className="mb-4">
                  <InputLabel>Idioma</InputLabel>
                  <Select
                    value={preferences.language}
                    onChange={(e) => setPreferences({ ...preferences, language: e.target.value as Preferences['language'] })}
                    label="Idioma"
                    aria-label="Seleção de idioma"
                  >
                    <MenuItem value="pt-BR">Português (Brasil)</MenuItem>
                    <MenuItem value="en-US">English (US)</MenuItem>
                    <MenuItem value="es-ES">Español</MenuItem>
                  </Select>
                </FormControl>
                <FormControl fullWidth>
                  <InputLabel>Tamanho da Fonte</InputLabel>
                  <Select
                    value={preferences.accessibility.fontSize}
                    onChange={(e) =>
                      setPreferences({
                        ...preferences,
                        accessibility: { ...preferences.accessibility, fontSize: e.target.value as Preferences['accessibility']['fontSize'] },
                      })
                    }
                    label="Tamanho da Fonte"
                    aria-label="Seleção de tamanho da fonte"
                  >
                    <MenuItem value="small">Pequena</MenuItem>
                    <MenuItem value="medium">Média</MenuItem>
                    <MenuItem value="large">Grande</MenuItem>
                  </Select>
                </FormControl>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card>
              <CardHeader title="Notificações" avatar={<Notifications color="primary" />} />
              <CardContent>
                <FormControlLabel
                  control={
                    <Switch
                      checked={preferences.notifications.email}
                      onChange={(e) =>
                        setPreferences({
                          ...preferences,
                          notifications: { ...preferences.notifications, email: e.target.checked },
                        })
                      }
                    />
                  }
                  label="Notificações por Email"
                />
                <FormControlLabel
                  control={
                    <Switch
                      checked={preferences.notifications.push}
                      onChange={(e) =>
                        setPreferences({
                          ...preferences,
                          notifications: { ...preferences.notifications, push: e.target.checked },
                        })
                      }
                    />
                  }
                  label="Notificações Push"
                />
                <FormControlLabel
                  control={
                    <Switch
                      checked={preferences.notifications.sms}
                      onChange={(e) =>
                        setPreferences({
                          ...preferences,
                          notifications: { ...preferences.notifications, sms: e.target.checked },
                        })
                      }
                    />
                  }
                  label="Notificações por SMS"
                />
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      <Dialog
        open={openPasswordDialog}
        onClose={() => setOpenPasswordDialog(false)}
        maxWidth="sm"
        fullWidth
        aria-labelledby="password-dialog-title"
      >
        <DialogTitle id="password-dialog-title">Alterar Senha</DialogTitle>
        <DialogContent>
          <Alert severity="info" className="mb-4">
            Sua nova senha deve ter pelo menos 8 caracteres e incluir letras maiúsculas, minúsculas e números.
          </Alert>
          <Box className="flex flex-col gap-4">
            <TextField
              label="Senha Atual"
              type="password"
              fullWidth
              required
              value={passwordData.currentPassword}
              onChange={(e) => setPasswordData({ ...passwordData, currentPassword: e.target.value })}
              aria-label="Senha atual"
            />
            <TextField
              label="Nova Senha"
              type="password"
              fullWidth
              required
              value={passwordData.newPassword}
              onChange={(e) => setPasswordData({ ...passwordData, newPassword: e.target.value })}
              aria-label="Nova senha"
            />
            <TextField
              label="Confirmar Nova Senha"
              type="password"
              fullWidth
              required
              value={passwordData.confirmPassword}
              onChange={(e) => setPasswordData({ ...passwordData, confirmPassword: e.target.value })}
              aria-label="Confirmar nova senha"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenPasswordDialog(false)} disabled={state.isLoading}>
            Cancelar
          </Button>
          <Button variant="contained" onClick={handlePasswordChange} disabled={state.isLoading}>
            Alterar Senha
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default ProfilePage;