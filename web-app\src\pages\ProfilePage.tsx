import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  <PERSON>,
  Grid,
  Card,
  CardContent,
  CardHeader,
  TextField,
  Button,
  Avatar,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tabs,
  Tab,
  Switch,
  FormControlLabel,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Badge,
  LinearProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Stepper,
  Step,
  StepLabel,
  StepContent
} from '@mui/material';
import {
  Person,
  Email,
  Phone,
  Business,
  Security,
  History,
  Save,
  Edit,
  PhotoCamera,
  Key,
  Settings,
  Notifications,
  Language,
  Palette,
  Shield,
  VpnKey,
  Fingerprint,
  Smartphone,
  Computer,
  Visibility,
  VisibilityOff,
  Download,
  Upload,
  Delete,
  Add,
  ExpandMore,
  CheckCircle,
  Error,
  Warning,
  Info,
  Timeline,
  Assessment,
  Storage,
  CloudDownload,
  QrCode,
  Backup,
  RestoreFromTrash,
  DarkMode,
  LightMode,
  Schedule,
  LocationOn,
  DeviceHub,
  NetworkCheck
} from '@mui/icons-material';
import { useToast } from '../components/Toast';

interface UserProfile {
  id: string;
  name: string;
  email: string;
  phone: string;
  company: string;
  role: string;
  avatar?: string;
  createdAt: string;
  lastLogin: string;
}

interface ActivityLog {
  id: string;
  action: string;
  timestamp: string;
  details: string;
}

const ProfilePage: React.FC = () => {
  const [profile, setProfile] = useState<UserProfile>({
    id: '1',
    name: 'João Silva',
    email: '<EMAIL>',
    phone: '(11) 99999-9999',
    company: 'Cartório Central',
    role: 'Administrador',
    createdAt: '2024-01-01',
    lastLogin: '2024-01-15 10:30'
  });

  const [activities] = useState<ActivityLog[]>([
    {
      id: '1',
      action: 'Login realizado',
      timestamp: '2024-01-15 10:30',
      details: 'Acesso via web'
    },
    {
      id: '2',
      action: 'Gravação iniciada',
      timestamp: '2024-01-15 09:15',
      details: 'Documento: Certidão de Nascimento'
    },
    {
      id: '3',
      action: 'Configurações alteradas',
      timestamp: '2024-01-14 16:45',
      details: 'Tema alterado para escuro'
    }
  ]);

  const [isEditing, setIsEditing] = useState(false);
  const [editedProfile, setEditedProfile] = useState<UserProfile>(profile);
  const [openPasswordDialog, setOpenPasswordDialog] = useState(false);

  // Novos estados para funcionalidades expandidas
  const [currentTab, setCurrentTab] = useState(0);
  const [preferences, setPreferences] = useState({
    theme: 'light',
    language: 'pt-BR',
    notifications: {
      email: true,
      push: true,
      sms: false
    },
    privacy: {
      showEmail: false,
      showPhone: false,
      allowAnalytics: true
    },
    accessibility: {
      fontSize: 'medium',
      highContrast: false,
      screenReader: false
    }
  });

  const [securitySettings, setSecuritySettings] = useState({
    twoFactorEnabled: false,
    loginAlerts: true,
    sessionTimeout: 30,
    trustedDevices: [] as Array<{
      id: string;
      name: string;
      lastUsed: string;
      location: string;
    }>
  });

  const [dataExport, setDataExport] = useState({
    isExporting: false,
    lastExport: null as string | null,
    exportTypes: {
      profile: true,
      activities: true,
      recordings: false,
      settings: true
    }
  });

  const [sessions, setSessions] = useState([
    {
      id: '1',
      device: 'Chrome - Windows',
      location: 'São Paulo, SP',
      lastActive: '2025-01-18 10:30',
      current: true
    },
    {
      id: '2',
      device: 'Mobile App - Android',
      location: 'São Paulo, SP',
      lastActive: '2025-01-17 18:45',
      current: false
    }
  ]);

  const { showSuccess, showError } = useToast();

  useEffect(() => {
    setEditedProfile(profile);
  }, [profile]);

  const handleSaveProfile = async () => {
    try {
      setProfile(editedProfile);
      setIsEditing(false);
      showSuccess('Perfil atualizado com sucesso!');
    } catch (error) {
      showError('Erro ao atualizar perfil');
    }
  };

  const handleCancelEdit = () => {
    setEditedProfile(profile);
    setIsEditing(false);
  };

  const handleChangePassword = () => {
    setOpenPasswordDialog(false);
    showSuccess('Senha alterada com sucesso!');
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ mt: 4, mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          <Person sx={{ mr: 1, verticalAlign: 'middle' }} />
          Meu Perfil
        </Typography>

        {/* Tabs de Navegação */}
        <Paper sx={{ mb: 3 }}>
          <Tabs
            value={currentTab}
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
          >
            <Tab icon={<Person />} label="Perfil" />
            <Tab icon={<Settings />} label="Preferências" />
            <Tab icon={<Security />} label="Segurança" />
            <Tab icon={<History />} label="Atividades" />
            <Tab icon={<Storage />} label="Dados" />
          </Tabs>
        </Paper>

        {/* Aba 0: Perfil */}
        {currentTab === 0 && (
          <Grid container spacing={3}>
          {/* Informações do Perfil */}
          <Grid item xs={12} md={8}>
            <Card>
              <CardHeader
                title="Informações Pessoais"
                action={
                  <Button
                    startIcon={<Edit />}
                    onClick={() => setIsEditing(!isEditing)}
                    variant={isEditing ? "outlined" : "contained"}
                  >
                    {isEditing ? 'Cancelar' : 'Editar'}
                  </Button>
                }
              />
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <Avatar
                    sx={{ width: 80, height: 80, mr: 2 }}
                    src={profile.avatar}
                  >
                    {profile.name.charAt(0)}
                  </Avatar>
                  {isEditing && (
                    <Button
                      startIcon={<PhotoCamera />}
                      variant="outlined"
                      size="small"
                    >
                      Alterar Foto
                    </Button>
                  )}
                </Box>

                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Nome Completo"
                      fullWidth
                      value={editedProfile.name}
                      onChange={(e) => setEditedProfile({...editedProfile, name: e.target.value})}
                      disabled={!isEditing}
                      InputProps={{
                        startAdornment: <Person sx={{ mr: 1, color: 'action.active' }} />
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Email"
                      fullWidth
                      value={editedProfile.email}
                      onChange={(e) => setEditedProfile({...editedProfile, email: e.target.value})}
                      disabled={!isEditing}
                      InputProps={{
                        startAdornment: <Email sx={{ mr: 1, color: 'action.active' }} />
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Telefone"
                      fullWidth
                      value={editedProfile.phone}
                      onChange={(e) => setEditedProfile({...editedProfile, phone: e.target.value})}
                      disabled={!isEditing}
                      InputProps={{
                        startAdornment: <Phone sx={{ mr: 1, color: 'action.active' }} />
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Empresa"
                      fullWidth
                      value={editedProfile.company}
                      onChange={(e) => setEditedProfile({...editedProfile, company: e.target.value})}
                      disabled={!isEditing}
                      InputProps={{
                        startAdornment: <Business sx={{ mr: 1, color: 'action.active' }} />
                      }}
                    />
                  </Grid>
                </Grid>

                {isEditing && (
                  <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
                    <Button
                      variant="contained"
                      startIcon={<Save />}
                      onClick={handleSaveProfile}
                    >
                      Salvar Alterações
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={handleCancelEdit}
                    >
                      Cancelar
                    </Button>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Informações da Conta */}
          <Grid item xs={12} md={4}>
            <Card sx={{ mb: 2 }}>
              <CardHeader title="Informações da Conta" />
              <CardContent>
                <List>
                  <ListItem>
                    <ListItemIcon>
                      <Security />
                    </ListItemIcon>
                    <ListItemText
                      primary="Função"
                      secondary={
                        <Chip
                          label={profile.role}
                          color="primary"
                          size="small"
                        />
                      }
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Membro desde"
                      secondary={new Date(profile.createdAt).toLocaleDateString('pt-BR')}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Último acesso"
                      secondary={profile.lastLogin}
                    />
                  </ListItem>
                </List>

                <Divider sx={{ my: 2 }} />

                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<Key />}
                  onClick={() => setOpenPasswordDialog(true)}
                >
                  Alterar Senha
                </Button>
              </CardContent>
            </Card>

            {/* Atividades Recentes */}
            <Card>
              <CardHeader title="Atividades Recentes" />
              <CardContent>
                <List dense>
                  {activities.map((activity) => (
                    <ListItem key={activity.id}>
                      <ListItemIcon>
                        <History />
                      </ListItemIcon>
                      <ListItemText
                        primary={activity.action}
                        secondary={
                          <Box>
                            <Typography variant="caption" display="block">
                              {activity.timestamp}
                            </Typography>
                            <Typography variant="caption" color="textSecondary">
                              {activity.details}
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
        )}

        {/* Aba 1: Preferências */}
        {currentTab === 1 && (
          <Grid container spacing={3}>
            {/* Tema e Aparência */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader
                  title="Tema e Aparência"
                  avatar={<Palette color="primary" />}
                />
                <CardContent>
                  <FormControl fullWidth sx={{ mb: 2 }}>
                    <InputLabel>Tema</InputLabel>
                    <Select
                      value={preferences.theme}
                      onChange={(e) => setPreferences(prev => ({
                        ...prev,
                        theme: e.target.value
                      }))}
                      label="Tema"
                    >
                      <MenuItem value="light">
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <LightMode /> Claro
                        </Box>
                      </MenuItem>
                      <MenuItem value="dark">
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <DarkMode /> Escuro
                        </Box>
                      </MenuItem>
                      <MenuItem value="auto">
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Settings /> Automático
                        </Box>
                      </MenuItem>
                    </Select>
                  </FormControl>

                  <FormControl fullWidth sx={{ mb: 2 }}>
                    <InputLabel>Idioma</InputLabel>
                    <Select
                      value={preferences.language}
                      onChange={(e) => setPreferences(prev => ({
                        ...prev,
                        language: e.target.value
                      }))}
                      label="Idioma"
                    >
                      <MenuItem value="pt-BR">Português (Brasil)</MenuItem>
                      <MenuItem value="en-US">English (US)</MenuItem>
                      <MenuItem value="es-ES">Español</MenuItem>
                    </Select>
                  </FormControl>

                  <FormControl fullWidth>
                    <InputLabel>Tamanho da Fonte</InputLabel>
                    <Select
                      value={preferences.accessibility.fontSize}
                      onChange={(e) => setPreferences(prev => ({
                        ...prev,
                        accessibility: {
                          ...prev.accessibility,
                          fontSize: e.target.value
                        }
                      }))}
                      label="Tamanho da Fonte"
                    >
                      <MenuItem value="small">Pequena</MenuItem>
                      <MenuItem value="medium">Média</MenuItem>
                      <MenuItem value="large">Grande</MenuItem>
                    </Select>
                  </FormControl>
                </CardContent>
              </Card>
            </Grid>

            {/* Notificações */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader
                  title="Notificações"
                  avatar={<Notifications color="primary" />}
                />
                <CardContent>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={preferences.notifications.email}
                        onChange={(e) => setPreferences(prev => ({
                          ...prev,
                          notifications: {
                            ...prev.notifications,
                            email: e.target.checked
                          }
                        }))}
                      />
                    }
                    label="Notificações por Email"
                  />

                  <FormControlLabel
                    control={
                      <Switch
                        checked={preferences.notifications.push}
                        onChange={(e) => setPreferences(prev => ({
                          ...prev,
                          notifications: {
                            ...prev.notifications,
                            push: e.target.checked
                          }
                        }))}
                      />
                    }
                    label="Notificações Push"
                  />

                  <FormControlLabel
                    control={
                      <Switch
                        checked={preferences.notifications.sms}
                        onChange={(e) => setPreferences(prev => ({
                          ...prev,
                          notifications: {
                            ...prev.notifications,
                            sms: e.target.checked
                          }
                        }))}
                      />
                    }
                    label="Notificações por SMS"
                  />
                </CardContent>
              </Card>
            </Grid>

            {/* Privacidade */}
            <Grid item xs={12}>
              <Card>
                <CardHeader
                  title="Configurações de Privacidade"
                  avatar={<Shield color="primary" />}
                />
                <CardContent>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={4}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={preferences.privacy.showEmail}
                            onChange={(e) => setPreferences(prev => ({
                              ...prev,
                              privacy: {
                                ...prev.privacy,
                                showEmail: e.target.checked
                              }
                            }))}
                          />
                        }
                        label="Mostrar Email Publicamente"
                      />
                    </Grid>

                    <Grid item xs={12} md={4}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={preferences.privacy.showPhone}
                            onChange={(e) => setPreferences(prev => ({
                              ...prev,
                              privacy: {
                                ...prev.privacy,
                                showPhone: e.target.checked
                              }
                            }))}
                          />
                        }
                        label="Mostrar Telefone Publicamente"
                      />
                    </Grid>

                    <Grid item xs={12} md={4}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={preferences.privacy.allowAnalytics}
                            onChange={(e) => setPreferences(prev => ({
                              ...prev,
                              privacy: {
                                ...prev.privacy,
                                allowAnalytics: e.target.checked
                              }
                            }))}
                          />
                        }
                        label="Permitir Analytics"
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )}

        {/* Aba 2: Segurança */}
        {currentTab === 2 && (
          <Grid container spacing={3}>
            {/* Configurações de Segurança */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader
                  title="Configurações de Segurança"
                  avatar={<Security color="primary" />}
                />
                <CardContent>
                  <Box sx={{ mb: 3 }}>
                    <Button
                      variant="outlined"
                      startIcon={<Key />}
                      fullWidth
                      onClick={() => setOpenPasswordDialog(true)}
                      sx={{ mb: 2 }}
                    >
                      Alterar Senha
                    </Button>

                    <FormControlLabel
                      control={
                        <Switch
                          checked={securitySettings.twoFactorEnabled}
                          onChange={(e) => setSecuritySettings(prev => ({
                            ...prev,
                            twoFactorEnabled: e.target.checked
                          }))}
                        />
                      }
                      label="Autenticação de Dois Fatores"
                    />

                    <FormControlLabel
                      control={
                        <Switch
                          checked={securitySettings.loginAlerts}
                          onChange={(e) => setSecuritySettings(prev => ({
                            ...prev,
                            loginAlerts: e.target.checked
                          }))}
                        />
                      }
                      label="Alertas de Login"
                    />

                    <TextField
                      fullWidth
                      label="Timeout de Sessão (minutos)"
                      type="number"
                      value={securitySettings.sessionTimeout}
                      onChange={(e) => setSecuritySettings(prev => ({
                        ...prev,
                        sessionTimeout: parseInt(e.target.value)
                      }))}
                      sx={{ mt: 2 }}
                    />
                  </Box>

                  {securitySettings.twoFactorEnabled && (
                    <Alert severity="success">
                      Autenticação de dois fatores está ativa. Seu QR Code:
                      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                        <QrCode sx={{ fontSize: 100 }} />
                      </Box>
                    </Alert>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* Sessões Ativas */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader
                  title="Sessões Ativas"
                  avatar={<DeviceHub color="primary" />}
                />
                <CardContent>
                  <List>
                    {sessions.map((session) => (
                      <ListItem key={session.id}>
                        <ListItemIcon>
                          {session.current ? (
                            <Computer color="primary" />
                          ) : (
                            <Smartphone />
                          )}
                        </ListItemIcon>
                        <ListItemText
                          primary={session.device}
                          secondary={
                            <Box>
                              <Typography variant="body2" color="text.secondary">
                                {session.location}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                Último acesso: {session.lastActive}
                              </Typography>
                            </Box>
                          }
                        />
                        {session.current ? (
                          <Chip label="Atual" color="primary" size="small" />
                        ) : (
                          <IconButton
                            color="error"
                            size="small"
                            onClick={() => setSessions(prev =>
                              prev.filter(s => s.id !== session.id)
                            )}
                          >
                            <Delete />
                          </IconButton>
                        )}
                      </ListItem>
                    ))}
                  </List>

                  <Button
                    variant="outlined"
                    color="error"
                    fullWidth
                    sx={{ mt: 2 }}
                    onClick={() => setSessions(prev => prev.filter(s => s.current))}
                  >
                    Encerrar Todas as Outras Sessões
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )}

        {/* Aba 3: Atividades */}
        {currentTab === 3 && (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Card>
                <CardHeader
                  title="Histórico de Atividades"
                  avatar={<Timeline color="primary" />}
                />
                <CardContent>
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Data/Hora</TableCell>
                          <TableCell>Ação</TableCell>
                          <TableCell>Detalhes</TableCell>
                          <TableCell>Status</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {activities.map((activity) => (
                          <TableRow key={activity.id}>
                            <TableCell>{activity.timestamp}</TableCell>
                            <TableCell>{activity.action}</TableCell>
                            <TableCell>{activity.details}</TableCell>
                            <TableCell>
                              <Chip
                                label="Sucesso"
                                color="success"
                                size="small"
                                icon={<CheckCircle />}
                              />
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )}

        {/* Aba 4: Dados */}
        {currentTab === 4 && (
          <Grid container spacing={3}>
            {/* Exportar Dados */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader
                  title="Exportar Dados"
                  avatar={<Download color="primary" />}
                />
                <CardContent>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Baixe uma cópia dos seus dados pessoais em formato JSON.
                  </Typography>

                  <FormControlLabel
                    control={
                      <Switch
                        checked={dataExport.exportTypes.profile}
                        onChange={(e) => setDataExport(prev => ({
                          ...prev,
                          exportTypes: {
                            ...prev.exportTypes,
                            profile: e.target.checked
                          }
                        }))}
                      />
                    }
                    label="Dados do Perfil"
                  />

                  <FormControlLabel
                    control={
                      <Switch
                        checked={dataExport.exportTypes.activities}
                        onChange={(e) => setDataExport(prev => ({
                          ...prev,
                          exportTypes: {
                            ...prev.exportTypes,
                            activities: e.target.checked
                          }
                        }))}
                      />
                    }
                    label="Histórico de Atividades"
                  />

                  <FormControlLabel
                    control={
                      <Switch
                        checked={dataExport.exportTypes.settings}
                        onChange={(e) => setDataExport(prev => ({
                          ...prev,
                          exportTypes: {
                            ...prev.exportTypes,
                            settings: e.target.checked
                          }
                        }))}
                      />
                    }
                    label="Configurações"
                  />

                  <Button
                    variant="contained"
                    startIcon={<CloudDownload />}
                    fullWidth
                    sx={{ mt: 2 }}
                    disabled={dataExport.isExporting}
                  >
                    {dataExport.isExporting ? 'Exportando...' : 'Exportar Dados'}
                  </Button>

                  {dataExport.lastExport && (
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                      Último export: {dataExport.lastExport}
                    </Typography>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* Excluir Conta */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader
                  title="Zona de Perigo"
                  avatar={<Warning color="error" />}
                />
                <CardContent>
                  <Alert severity="warning" sx={{ mb: 2 }}>
                    As ações abaixo são irreversíveis. Proceda com cuidado.
                  </Alert>

                  <Button
                    variant="outlined"
                    color="warning"
                    startIcon={<RestoreFromTrash />}
                    fullWidth
                    sx={{ mb: 2 }}
                  >
                    Limpar Histórico de Atividades
                  </Button>

                  <Button
                    variant="outlined"
                    color="error"
                    startIcon={<Delete />}
                    fullWidth
                  >
                    Excluir Conta Permanentemente
                  </Button>

                  <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                    A exclusão da conta removerá todos os seus dados permanentemente.
                    Esta ação não pode ser desfeita.
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )}

        {/* Dialog para Alterar Senha */}
        <Dialog open={openPasswordDialog} onClose={() => setOpenPasswordDialog(false)} maxWidth="sm" fullWidth>
          <DialogTitle>Alterar Senha</DialogTitle>
          <DialogContent>
            <Alert severity="info" sx={{ mb: 2 }}>
              Sua nova senha deve ter pelo menos 8 caracteres e incluir letras maiúsculas, minúsculas e números.
            </Alert>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
              <TextField
                label="Senha Atual"
                type="password"
                fullWidth
                required
              />
              <TextField
                label="Nova Senha"
                type="password"
                fullWidth
                required
              />
              <TextField
                label="Confirmar Nova Senha"
                type="password"
                fullWidth
                required
              />
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenPasswordDialog(false)}>
              Cancelar
            </Button>
            <Button
              variant="contained"
              onClick={handleChangePassword}
            >
              Alterar Senha
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </Container>
  );
};

export default ProfilePage;
