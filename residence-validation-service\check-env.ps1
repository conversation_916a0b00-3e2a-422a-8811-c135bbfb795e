# ==============================================================================
# Script de Verificação de Variáveis de Ambiente (PowerShell)
# ==============================================================================
# 
# Este script verifica se todas as variáveis de ambiente necessárias
# estão configuradas corretamente antes de executar o Docker
# 
# Uso: .\check-env.ps1
# ==============================================================================

Write-Host "🔍 Verificando configuração de variáveis de ambiente..." -ForegroundColor Cyan
Write-Host "================================================" -ForegroundColor Cyan

# Função para verificar se uma variável está definida
function Check-Variable {
    param(
        [string]$VarName,
        [string]$VarValue
    )
    
    if ([string]::IsNullOrWhiteSpace($VarValue)) {
        Write-Host "❌ $VarName : NÃO CONFIGURADO" -ForegroundColor Red
        return $false
    }
    elseif ($VarValue -eq "your_google_client_id_here" -or 
            $VarValue -eq "your_google_client_secret_here" -or 
            $VarValue -eq "your_super_secret_jwt_key_here_change_in_production" -or
            $VarValue -eq "your_32_char_encryption_key_here!!") {
        Write-Host "⚠️  $VarName : VALOR PADRÃO (precisa ser alterado)" -ForegroundColor Yellow
        return $false
    }
    else {
        Write-Host "✅ $VarName : CONFIGURADO" -ForegroundColor Green
        return $true
    }
}

# Verificar se o arquivo .env.production existe
Write-Host ""
Write-Host "📁 Verificando arquivo .env.production..." -ForegroundColor Blue

$envFileExists = Test-Path ".env.production"
if (-not $envFileExists) {
    Write-Host "❌ Arquivo .env.production não encontrado!" -ForegroundColor Red
    Write-Host "   Copie o arquivo .env.production.example para .env.production" -ForegroundColor Yellow
    Write-Host "   e configure os valores reais." -ForegroundColor Yellow
    $envFileOk = $false
} else {
    Write-Host "✅ Arquivo .env.production encontrado" -ForegroundColor Green
    $envFileOk = $true
}

# Carregar variáveis do arquivo .env.production se existir
$envVars = @{}
if ($envFileExists) {
    Get-Content ".env.production" | ForEach-Object {
        if ($_ -match "^([^#][^=]*?)=(.*)$") {
            $envVars[$matches[1].Trim()] = $matches[2].Trim()
        }
    }
}

Write-Host ""
Write-Host "🔐 Verificando variáveis obrigatórias..." -ForegroundColor Blue

$googleIdOk = Check-Variable "GOOGLE_CLIENT_ID" $envVars["GOOGLE_CLIENT_ID"]
$googleSecretOk = Check-Variable "GOOGLE_CLIENT_SECRET" $envVars["GOOGLE_CLIENT_SECRET"]
$redirectUriOk = Check-Variable "NEXT_PUBLIC_GOOGLE_REDIRECT_URI" $envVars["NEXT_PUBLIC_GOOGLE_REDIRECT_URI"]
$jwtOk = Check-Variable "JWT_SECRET" $envVars["JWT_SECRET"]
$encryptionOk = Check-Variable "ENCRYPTION_KEY" $envVars["ENCRYPTION_KEY"]
$baseUrlOk = Check-Variable "NEXT_PUBLIC_BASE_URL" $envVars["NEXT_PUBLIC_BASE_URL"]

Write-Host ""
Write-Host "📊 Verificações adicionais..." -ForegroundColor Blue

# Verificar se a ENCRYPTION_KEY tem 32 caracteres
if ($envVars["ENCRYPTION_KEY"] -and $envVars["ENCRYPTION_KEY"].Length -ne 32) {
    Write-Host "⚠️  ENCRYPTION_KEY deve ter exatamente 32 caracteres (atual: $($envVars['ENCRYPTION_KEY'].Length))" -ForegroundColor Yellow
    $encryptionOk = $false
}

# Verificar se JWT_SECRET tem pelo menos 32 caracteres
if ($envVars["JWT_SECRET"] -and $envVars["JWT_SECRET"].Length -lt 32) {
    Write-Host "⚠️  JWT_SECRET deve ter pelo menos 32 caracteres (atual: $($envVars['JWT_SECRET'].Length))" -ForegroundColor Yellow
    $jwtOk = $false
}

# Verificar se REDIRECT_URI corresponde à BASE_URL
if ($envVars["NEXT_PUBLIC_GOOGLE_REDIRECT_URI"] -and $envVars["NEXT_PUBLIC_BASE_URL"]) {
    if (-not $envVars["NEXT_PUBLIC_GOOGLE_REDIRECT_URI"].StartsWith($envVars["NEXT_PUBLIC_BASE_URL"])) {
        Write-Host "⚠️  REDIRECT_URI deve começar com BASE_URL" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "================================================" -ForegroundColor Cyan

# Calcular resultado final
$totalErrors = 0
if (-not $envFileOk) { $totalErrors++ }
if (-not $googleIdOk) { $totalErrors++ }
if (-not $googleSecretOk) { $totalErrors++ }
if (-not $redirectUriOk) { $totalErrors++ }
if (-not $jwtOk) { $totalErrors++ }
if (-not $encryptionOk) { $totalErrors++ }
if (-not $baseUrlOk) { $totalErrors++ }

if ($totalErrors -eq 0) {
    Write-Host "🎉 Todas as verificações passaram!" -ForegroundColor Green
    Write-Host "   Você pode executar: docker-compose -f docker-compose.prod.yml up -d" -ForegroundColor Green
    Write-Host ""
    Write-Host "🔗 Após iniciar, verifique o endpoint de debug:" -ForegroundColor Cyan
    Write-Host "   Invoke-WebRequest http://localhost:3000/api/debug/env" -ForegroundColor Cyan
    exit 0
} else {
    Write-Host "❌ $totalErrors erro(s) encontrado(s)!" -ForegroundColor Red
    Write-Host ""
    Write-Host "📋 Passos para corrigir:" -ForegroundColor Yellow
    if (-not $envFileOk) {
        Write-Host "   1. Copie: Copy-Item .env.production.example .env.production" -ForegroundColor Yellow
    }
    Write-Host "   2. Edite o arquivo .env.production com valores reais" -ForegroundColor Yellow
    Write-Host "   3. Configure suas credenciais do Google OAuth2" -ForegroundColor Yellow
    Write-Host "   4. Execute este script novamente para verificar" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "📖 Veja o guia completo em: OAUTH_SETUP_GUIDE.md" -ForegroundColor Cyan
    exit 1
}
