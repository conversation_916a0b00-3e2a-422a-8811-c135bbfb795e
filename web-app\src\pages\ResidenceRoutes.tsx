import React from 'react';
import { Routes, Route } from 'react-router-dom';
import ResidenceValidationPage from '../components/residence/ResidenceValidationPage';
import { Container, Typography, Box } from '@mui/material';

const ResidenceRoutes: React.FC = () => {
  return (
    <Routes>
      <Route path="/" element={<ResidenceValidationPage />} />
      <Route path="/terms" element={<TermsPage />} />
      <Route path="/privacy" element={<PrivacyPage />} />
    </Routes>
  );
};

const TermsPage: React.FC = () => (
  <Container maxWidth="md" sx={{ py: 4 }}>
    <Typography variant="h4" gutterBottom>
      Termos de Uso - Validação de Residência
    </Typography>
    
    <Box sx={{ mt: 3 }}>
      <Typography variant="h6" gutterBottom>
        1. Natureza do Serviço
      </Typography>
      <Typography variant="body2" paragraph>
        O Sistema de Validação de Residência é uma ferramenta auxiliar que gera certificados 
        preliminares baseados em dados de geolocalização. Estes certificados NÃO possuem 
        validade legal por si só.
      </Typography>
      
      <Typography variant="h6" gutterBottom>
        2. Validade Legal
      </Typography>
      <Typography variant="body2" paragraph>
        Para ter validade legal, os certificados devem ser autenticados em cartório competente. 
        O usuário é responsável por este processo de autenticação.
      </Typography>
      
      <Typography variant="h6" gutterBottom>
        3. Responsabilidades do Usuário
      </Typography>
      <Typography variant="body2" paragraph>
        O usuário deve fornecer informações verdadeiras e é responsável pela autenticação 
        cartorária quando necessária validade legal.
      </Typography>
      
      <Typography variant="h6" gutterBottom>
        4. Limitações
      </Typography>
      <Typography variant="body2" paragraph>
        O sistema depende de dados do Google Timeline e pode não funcionar adequadamente 
        se o usuário não tiver histórico suficiente ativado.
      </Typography>
    </Box>
  </Container>
);

const PrivacyPage: React.FC = () => (
  <Container maxWidth="md" sx={{ py: 4 }}>
    <Typography variant="h4" gutterBottom>
      Política de Privacidade - Validação de Residência
    </Typography>
    
    <Box sx={{ mt: 3 }}>
      <Typography variant="h6" gutterBottom>
        1. Dados Coletados
      </Typography>
      <Typography variant="body2" paragraph>
        Coletamos apenas: histórico de localização (Google Timeline), informações básicas 
        do perfil (nome e email) e o endereço que você deseja validar.
      </Typography>
      
      <Typography variant="h6" gutterBottom>
        2. Uso dos Dados
      </Typography>
      <Typography variant="body2" paragraph>
        Os dados são usados exclusivamente para análise de presença noturna e geração 
        de certificados preliminares.
      </Typography>
      
      <Typography variant="h6" gutterBottom>
        3. Segurança
      </Typography>
      <Typography variant="body2" paragraph>
        Todos os dados são criptografados e armazenados com segurança. Não compartilhamos 
        com terceiros.
      </Typography>
      
      <Typography variant="h6" gutterBottom>
        4. Seus Direitos
      </Typography>
      <Typography variant="body2" paragraph>
        Você pode acessar, corrigir, excluir seus dados ou revogar o consentimento a 
        qualquer momento.
      </Typography>
    </Box>
  </Container>
);

export default ResidenceRoutes;
