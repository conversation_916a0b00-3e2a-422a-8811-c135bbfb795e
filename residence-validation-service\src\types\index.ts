// Tipos para dados de localização do Google Maps Timeline
export interface LocationData {
  timestamp: string;
  latitude: number;
  longitude: number;
  accuracy: number;
  address?: string;
  source: 'GPS' | 'NETWORK' | 'PASSIVE';
}

// Tipos para dados de atividade
export interface ActivityData {
  startTime: string;
  endTime: string;
  type: 'STILL' | 'WALKING' | 'DRIVING' | 'IN_VEHICLE' | 'ON_BICYCLE' | 'UNKNOWN';
  confidence: number;
  location: LocationData;
}

// Tipos para validação de residência
export interface ResidenceValidation {
  id: string;
  userId: string;
  address: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  validationPeriod: {
    startDate: string;
    endDate: string;
  };
  nightlyPresence: NightlyPresenceRecord[];
  status: 'PENDING' | 'VALIDATING' | 'APPROVED' | 'REJECTED';
  validationScore: number;
  createdAt: string;
  updatedAt: string;
}

// Registro de presença noturna
export interface NightlyPresenceRecord {
  date: string;
  startTime: string; // 18:00
  endTime: string;   // 06:00
  isPresent: boolean;
  confidence: number;
  locationPoints: LocationData[];
  distanceFromHome: number; // em metros
}

// Dados do usuário
export interface User {
  id: string;
  email: string;
  name: string;
  googleId: string;
  phone?: string;
  document?: string; // CPF/RG
  createdAt: string;
  updatedAt: string;
}

// Configurações de validação
export interface ValidationConfig {
  minimumDays: number; // 15 dias
  nightStartTime: string; // "18:00"
  nightEndTime: string;   // "06:00"
  maxDistanceFromHome: number; // 100 metros
  minimumPresenceHours: number; // 8 horas
  confidenceThreshold: number; // 0.7
}

// Certificado de residência
export interface ResidenceCertificate {
  id: string;
  userId: string;
  validationId: string;
  certificateNumber: string;
  issueDate: string;
  validUntil: string;
  address: string;
  validationPeriod: {
    startDate: string;
    endDate: string;
  };
  validationScore: number;
  validDays: number;
  pdfUrl?: string;
  qrCode: string;
  status: 'ACTIVE' | 'EXPIRED' | 'REVOKED';
}

// Resposta da API do Google
export interface GoogleTimelineResponse {
  timelineObjects: TimelineObject[];
}

export interface TimelineObject {
  activitySegment?: ActivitySegment;
  placeVisit?: PlaceVisit;
}

export interface ActivitySegment {
  startLocation: GoogleLocation;
  endLocation: GoogleLocation;
  duration: {
    startTimestamp: string;
    endTimestamp: string;
  };
  distance: number;
  activityType: string;
  confidence: string;
}

export interface PlaceVisit {
  location: GoogleLocation;
  duration: {
    startTimestamp: string;
    endTimestamp: string;
  };
  placeConfidence: string;
  visitConfidence: number;
}

export interface GoogleLocation {
  latitudeE7: number;
  longitudeE7: number;
  placeId?: string;
  address?: string;
  name?: string;
}

// Estados da aplicação
export interface AppState {
  user: User | null;
  currentValidation: ResidenceValidation | null;
  certificates: ResidenceCertificate[];
  isLoading: boolean;
  error: string | null;
}

// Props de componentes
export interface AuthButtonProps {
  onSuccess: (user: User) => void;
  onError: (error: string) => void;
}

export interface ValidationStepProps {
  step: number;
  totalSteps: number;
  title: string;
  description: string;
  isActive: boolean;
  isCompleted: boolean;
}

export interface CertificateCardProps {
  certificate: ResidenceCertificate;
  onDownload: (certificateId: string) => void;
  onView: (certificateId: string) => void;
}

// Tipos de resposta da API
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface ValidationResult {
  isValid: boolean;
  score: number;
  validDays: number;
  totalDays: number;
  nightlyRecords: NightlyPresenceRecord[];
  averageDistance: number;
  confidence: number;
}
