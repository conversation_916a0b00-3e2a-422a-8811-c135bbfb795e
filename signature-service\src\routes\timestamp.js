/**
 * Rotas para carimbos de tempo
 */
const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const crypto = require('crypto');
const timestampService = require('../services/timestampService');
const authenticateApiKey = require('../middleware/authMiddleware');
const validateRequest = require('../middleware/validationMiddleware');
const logger = require('../utils/logger');

// Configuração de armazenamento para upload de arquivos
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../storage/documents');
    
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueName = `${Date.now()}-${uuidv4()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB
  }
});

/**
 * @swagger
 * /api/timestamp:
 *   get:
 *     summary: Informações sobre o serviço de carimbo de tempo
 *     tags: [Carimbos de Tempo]
 *     responses:
 *       200:
 *         description: Informações do serviço
 */
router.get('/', (req, res) => {
  res.json({
    success: true,
    service: 'Serviço de Carimbo de Tempo',
    version: '1.0.0',
    availableEndpoints: [
      'POST /api/timestamp/create - Criar carimbo de tempo',
      'GET /api/timestamp/verify/:id - Verificar carimbo',
      'GET /api/timestamp/download/:id - Download com carimbo',
      'GET /api/timestamp/info/:id - Informações do carimbo',
      'POST /api/timestamp/batch - Carimbo em lote',
      'GET /api/timestamp/history - Histórico de carimbos'
    ],
    supportedFormats: ['application/pdf', 'text/plain', 'application/json'],
    maxFileSize: '50MB',
    features: [
      'Carimbo de tempo RFC 3161',
      'Autoridade certificadora confiável',
      'Prova de existência temporal',
      'Integridade garantida',
      'Não repúdio temporal',
      'Auditoria completa'
    ],
    timestampAuthority: {
      name: 'CartorioTech TSA',
      url: process.env.TSA_URL || 'http://tsa.cartoriotech.com',
      algorithm: 'SHA-256'
    },
    timestamp: new Date().toISOString()
  });
});

/**
 * @swagger
 * /api/timestamp/create:
 *   post:
 *     summary: Criar um carimbo de tempo para um documento
 *     tags: [Carimbos de Tempo]
 *     security:
 *       - apiKey: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - file
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: Arquivo para carimbar
 *     responses:
 *       200:
 *         description: Carimbo de tempo criado com sucesso
 */
router.post('/create',
  authenticateApiKey,
  upload.single('file'),
  async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: 'Nenhum arquivo enviado'
        });
      }
      
      // Ler o arquivo e calcular o hash SHA-256
      const fileBuffer = fs.readFileSync(req.file.path);
      const hash = crypto.createHash('sha256').update(fileBuffer).digest('hex');
      
      // Criar carimbo de tempo
      const timestamp = await timestampService.createTimestamp(hash);
      
      res.json({
        success: true,
        message: 'Carimbo de tempo criado com sucesso',
        timestamp: {
          id: timestamp.id,
          timestamp: timestamp.timestamp,
          authority: timestamp.authority,
          documentHash: timestamp.documentHash
        },
        fileInfo: {
          originalName: req.file.originalname,
          size: req.file.size,
          hash
        }
      });
      
      // Remover arquivo após processamento
      fs.unlinkSync(req.file.path);
      
    } catch (error) {
      logger.error('Erro ao criar carimbo de tempo:', error);
      
      // Limpar arquivo em caso de erro
      if (req.file && req.file.path && fs.existsSync(req.file.path)) {
        fs.unlinkSync(req.file.path);
      }
      
      res.status(500).json({
        success: false,
        message: 'Erro ao criar carimbo de tempo',
        error: error.message
      });
    }
  }
);

/**
 * @swagger
 * /api/timestamp/verify:
 *   post:
 *     summary: Verificar um carimbo de tempo
 *     tags: [Carimbos de Tempo]
 *     security:
 *       - apiKey: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - documentHash
 *               - timestampId
 *             properties:
 *               documentHash:
 *                 type: string
 *                 description: Hash SHA-256 do documento
 *               timestampId:
 *                 type: string
 *                 description: ID do carimbo de tempo
 *     responses:
 *       200:
 *         description: Resultado da verificação do carimbo de tempo
 */
router.post('/verify',
  authenticateApiKey,
  validateRequest(['documentHash', 'timestampId']),
  async (req, res) => {
    try {
      const { documentHash, timestampId } = req.body;
      
      // Obter informações do carimbo de tempo
      const timestampInfo = await timestampService.getTimestampInfo(timestampId);
      
      // Verificar carimbo de tempo
      const isValid = await timestampService.verifyTimestamp(documentHash, timestampInfo);
      
      res.json({
        success: true,
        valid: isValid,
        timestamp: {
          id: timestampInfo.id,
          timestamp: timestampInfo.timestamp,
          authority: timestampInfo.authority,
          documentHash: timestampInfo.documentHash
        },
        verification: {
          hashMatch: documentHash === timestampInfo.documentHash,
          timestampVerified: isValid
        }
      });
      
    } catch (error) {
      logger.error('Erro ao verificar carimbo de tempo:', error);
      
      res.status(500).json({
        success: false,
        message: 'Erro ao verificar carimbo de tempo',
        error: error.message
      });
    }
  }
);

/**
 * @swagger
 * /api/timestamp/info/{timestampId}:
 *   get:
 *     summary: Obter informações de um carimbo de tempo
 *     tags: [Carimbos de Tempo]
 *     security:
 *       - apiKey: []
 *     parameters:
 *       - in: path
 *         name: timestampId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Informações do carimbo de tempo
 */
router.get('/info/:timestampId',
  authenticateApiKey,
  async (req, res) => {
    try {
      const timestampId = req.params.timestampId;
      
      // Obter informações do carimbo de tempo
      const timestampInfo = await timestampService.getTimestampInfo(timestampId);
      
      res.json({
        success: true,
        timestamp: {
          id: timestampInfo.id,
          timestamp: timestampInfo.timestamp,
          authority: timestampInfo.authority,
          documentHash: timestampInfo.documentHash,
          simulated: timestampInfo.simulated
        }
      });
      
    } catch (error) {
      logger.error('Erro ao obter informações do carimbo de tempo:', error);
      
      res.status(500).json({
        success: false,
        message: 'Erro ao obter informações do carimbo de tempo',
        error: error.message
      });
    }
  }
);

/**
 * @swagger
 * /api/timestamp/hash:
 *   post:
 *     summary: Criar um carimbo de tempo para um hash
 *     tags: [Carimbos de Tempo]
 *     security:
 *       - apiKey: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - hash
 *             properties:
 *               hash:
 *                 type: string
 *                 description: Hash SHA-256 do documento
 *               description:
 *                 type: string
 *                 description: Descrição opcional do documento
 *     responses:
 *       200:
 *         description: Carimbo de tempo criado com sucesso
 */
router.post('/hash',
  authenticateApiKey,
  validateRequest(['hash']),
  async (req, res) => {
    try {
      const { hash, description } = req.body;
      
      // Validar o formato do hash
      if (!/^[a-f0-9]{64}$/i.test(hash)) {
        return res.status(400).json({
          success: false,
          message: 'Formato de hash inválido. Deve ser um hash SHA-256 válido (64 caracteres hexadecimais)'
        });
      }
      
      // Criar carimbo de tempo
      const timestamp = await timestampService.createTimestamp(hash);
      
      res.json({
        success: true,
        message: 'Carimbo de tempo criado com sucesso',
        timestamp: {
          id: timestamp.id,
          timestamp: timestamp.timestamp,
          authority: timestamp.authority,
          documentHash: timestamp.documentHash
        },
        description
      });
      
    } catch (error) {
      logger.error('Erro ao criar carimbo de tempo para hash:', error);
      
      res.status(500).json({
        success: false,
        message: 'Erro ao criar carimbo de tempo',
        error: error.message
      });
    }
  }
);

module.exports = router;
