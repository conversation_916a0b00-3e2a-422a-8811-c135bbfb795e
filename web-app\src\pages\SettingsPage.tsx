import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  <PERSON>,
  Card,
  CardContent,
  CardHeader,
  Grid,
  Switch,
  FormControlLabel,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Slider,
  Divider,
  Alert,
  TextField,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tooltip,
  LinearProgress,
  Badge
} from '@mui/material';
import {
  Settings,
  Save,
  RestoreSharp,
  Palette,
  Language,
  Notifications,
  Security,
  Storage,
  Backup,
  CloudSync,
  Speed,
  Memory,
  NetworkCheck,
  Update,
  BugReport,
  Help,
  Info,
  Warning,
  Error,
  CheckCircle,
  ExpandMore,
  Refresh,
  Download,
  Upload,
  Delete,
  Add,
  Edit,
  Visibility,
  VisibilityOff,
  Schedule,
  Assessment,
  Timeline,
  Computer,
  Smartphone,
  Tablet,
  Router,
  Wifi,
  VpnKey,
  Shield,
  Lock,
  LockOpen,
  Key,
  Fingerprint,
  QrCode,
  Email,
  Sms,
  Phone,
  DarkMode,
  LightMode,
  AutoMode,
  VolumeUp,
  VolumeDown,
  Mic,
  MicOff,
  Videocam,
  VideocamOff
} from '@mui/icons-material';
import { useToast } from '../components/Toast';

interface AppSettings {
  // Aparência
  theme: 'light' | 'dark' | 'auto';
  language: 'pt-BR' | 'en-US' | 'es-ES';
  fontSize: 'small' | 'medium' | 'large';
  highContrast: boolean;

  // Sistema
  autoSave: boolean;
  autoBackup: boolean;
  backupFrequency: 'hourly' | 'daily' | 'weekly';
  maxFileSize: number; // MB
  compressionLevel: number; // 0-9

  // Gravação
  defaultQuality: 'low' | 'medium' | 'high' | 'ultra';
  maxRecordingTime: number; // em minutos
  autoCleanup: boolean;
  cleanupDays: number;
  recording: {
    defaultAudio: boolean;
    countdown: number; // segundos
    stopOnError: boolean;
    autoStart: boolean;
    pauseOnError: boolean;
    maxRetries: number;
  };

  // Notificações
  notifications: {
    enabled: boolean;
    uploads: boolean;
    reports: boolean;
    errors: boolean;
    maintenance: boolean;
    security: boolean;
    email: boolean;
    push: boolean;
    sound: boolean;
    volume: number;
  };

  // Segurança
  security: {
    sessionTimeout: number; // minutos
    autoLock: boolean;
    requirePassword: boolean;
    twoFactor: boolean;
    encryptFiles: boolean;
    auditLog: boolean;
  };

  // Performance
  performance: {
    enableCache: boolean;
    cacheSize: number; // MB
    preloadData: boolean;
    lazyLoading: boolean;
    compressionEnabled: boolean;
  };

  // Rede
  network: {
    timeout: number; // segundos
    retries: number;
    offlineMode: boolean;
    syncOnWifi: boolean;
    lowDataMode: boolean;
  };

  // Desenvolvedor
  developer: {
    debugMode: boolean;
    verboseLogging: boolean;
    showPerformance: boolean;
    enableExperiments: boolean;
  };
}

const defaultSettings: AppSettings = {
  // Aparência
  theme: 'light',
  language: 'pt-BR',
  fontSize: 'medium',
  highContrast: false,

  // Sistema
  autoSave: true,
  autoBackup: true,
  backupFrequency: 'daily',
  maxFileSize: 100,
  compressionLevel: 5,

  // Gravação
  defaultQuality: 'high',
  maxRecordingTime: 30,
  autoCleanup: false,
  cleanupDays: 30,
  recording: {
    defaultAudio: true,
    countdown: 3,
    stopOnError: false,
    autoStart: false,
    pauseOnError: true,
    maxRetries: 3
  },

  // Notificações
  notifications: {
    enabled: true,
    uploads: true,
    reports: true,
    errors: true,
    maintenance: true,
    security: true,
    email: true,
    push: true,
    sound: true,
    volume: 50
  },

  // Segurança
  security: {
    sessionTimeout: 30,
    autoLock: false,
    requirePassword: true,
    twoFactor: false,
    encryptFiles: true,
    auditLog: true
  },

  // Performance
  performance: {
    enableCache: true,
    cacheSize: 100,
    preloadData: true,
    lazyLoading: true,
    compressionEnabled: true
  },

  // Rede
  network: {
    timeout: 30,
    retries: 3,
    offlineMode: false,
    syncOnWifi: true,
    lowDataMode: false
  },

  // Desenvolvedor
  developer: {
    debugMode: false,
    verboseLogging: false,
    showPerformance: false,
    enableExperiments: false
  }
};

const SettingsPage: React.FC = () => {
  const [settings, setSettings] = useState<AppSettings>(defaultSettings);
  const [hasChanges, setHasChanges] = useState(false);
  const [currentTab, setCurrentTab] = useState(0);
  const [resetDialogOpen, setResetDialogOpen] = useState(false);
  const [exportDialogOpen, setExportDialogOpen] = useState(false);
  const [importDialogOpen, setImportDialogOpen] = useState(false);
  const [systemInfo, setSystemInfo] = useState({
    version: '2.1.0',
    buildDate: '2025-01-18',
    platform: 'Web',
    browser: navigator.userAgent.split(' ')[0],
    storage: {
      used: 0,
      total: 0,
      available: 0
    },
    performance: {
      memory: 0,
      cpu: 0,
      network: 'good' as 'good' | 'slow' | 'offline'
    }
  });

  const { showSuccess, showError } = useToast();

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = () => {
    try {
      const savedSettings = localStorage.getItem('cartorio-settings');
      if (savedSettings) {
        const parsed = JSON.parse(savedSettings);
        setSettings({ ...defaultSettings, ...parsed });
      }
    } catch (error) {
      console.error('Erro ao carregar configurações:', error);
      showError('Erro ao carregar configurações');
    }
  };

  const saveSettings = async () => {
    try {
      localStorage.setItem('cartorio-settings', JSON.stringify(settings));
      setHasChanges(false);
      showSuccess('Configurações salvas com sucesso!');
      
      // Aplicar configurações imediatamente
      applySettings();
    } catch (error) {
      console.error('Erro ao salvar configurações:', error);
      showError('Erro ao salvar configurações');
    }
  };

  const applySettings = () => {
    // Aplicar tema
    if (settings.theme !== 'auto') {
      document.documentElement.setAttribute('data-theme', settings.theme);
    }
    
    // Outras aplicações podem ser feitas aqui
  };

  const resetSettings = () => {
    setSettings(defaultSettings);
    setHasChanges(true);
    showSuccess('Configurações resetadas para padrão');
  };

  const updateSetting = (path: string, value: any) => {
    setSettings(prev => {
      const newSettings = { ...prev };
      const keys = path.split('.');
      let current = newSettings as any;
      
      for (let i = 0; i < keys.length - 1; i++) {
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]] = value;
      setHasChanges(true);
      return newSettings;
    });
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };

  const handleReset = () => {
    setSettings(defaultSettings);
    setHasChanges(true);
    setResetDialogOpen(false);
    showSuccess('Configurações restauradas para o padrão');
  };

  const handleExport = () => {
    const dataStr = JSON.stringify(settings, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'cartorio-settings.json';
    link.click();
    URL.revokeObjectURL(url);
    setExportDialogOpen(false);
    showSuccess('Configurações exportadas com sucesso');
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ mt: 4, mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          <Settings sx={{ mr: 1, verticalAlign: 'middle' }} />
          Configurações do Sistema
        </Typography>

        {/* Tabs de Navegação */}
        <Paper sx={{ mb: 3 }}>
          <Tabs
            value={currentTab}
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
          >
            <Tab icon={<Palette />} label="Aparência" />
            <Tab icon={<Computer />} label="Sistema" />
            <Tab icon={<Videocam />} label="Gravação" />
            <Tab icon={<Notifications />} label="Notificações" />
            <Tab icon={<Security />} label="Segurança" />
            <Tab icon={<Speed />} label="Performance" />
            <Tab icon={<NetworkCheck />} label="Rede" />
            <Tab icon={<BugReport />} label="Avançado" />
          </Tabs>
        </Paper>

        {/* Aba 0: Aparência */}
        {currentTab === 0 && (
          <Box
            role="tabpanel"
            id="settings-tabpanel-0"
            aria-labelledby="settings-tab-0"
          >
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card sx={{ height: 'fit-content' }}>
                  <CardHeader
                    title="Tema e Interface"
                    titleTypographyProps={{ variant: 'h6' }}
                    avatar={<Palette color="primary" />}
                  />
                  <CardContent>
                  <FormControl fullWidth sx={{ mb: 2 }}>
                    <InputLabel>Tema</InputLabel>
                    <Select
                      value={settings.theme}
                      onChange={(e) => updateSetting('theme', e.target.value)}
                      label="Tema"
                    >
                      <MenuItem value="light">
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <LightMode /> Claro
                        </Box>
                      </MenuItem>
                      <MenuItem value="dark">
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <DarkMode /> Escuro
                        </Box>
                      </MenuItem>
                      <MenuItem value="auto">
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <AutoMode /> Automático
                        </Box>
                      </MenuItem>
                    </Select>
                  </FormControl>

                  <FormControl fullWidth sx={{ mb: 2 }}>
                    <InputLabel>Idioma</InputLabel>
                    <Select
                      value={settings.language}
                      onChange={(e) => updateSetting('language', e.target.value)}
                      label="Idioma"
                    >
                      <MenuItem value="pt-BR">Português (Brasil)</MenuItem>
                      <MenuItem value="en-US">English (US)</MenuItem>
                      <MenuItem value="es-ES">Español</MenuItem>
                    </Select>
                  </FormControl>

                  <FormControl fullWidth sx={{ mb: 2 }}>
                    <InputLabel>Tamanho da Fonte</InputLabel>
                    <Select
                      value={settings.fontSize}
                      onChange={(e) => updateSetting('fontSize', e.target.value)}
                      label="Tamanho da Fonte"
                    >
                      <MenuItem value="small">Pequena</MenuItem>
                      <MenuItem value="medium">Média</MenuItem>
                      <MenuItem value="large">Grande</MenuItem>
                    </Select>
                  </FormControl>

                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.highContrast}
                        onChange={(e) => updateSetting('highContrast', e.target.checked)}
                      />
                    }
                    label="Alto Contraste"
                  />
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader title="Acessibilidade" />
                <CardContent>
                  <Alert severity="info" sx={{ mb: 2 }}>
                    Configurações para melhorar a acessibilidade do sistema.
                  </Alert>

                  <Typography gutterBottom>Tamanho da Fonte</Typography>
                  <Slider
                    value={settings.fontSize === 'small' ? 0 : settings.fontSize === 'medium' ? 1 : 2}
                    onChange={(_, value) => {
                      const sizes = ['small', 'medium', 'large'];
                      updateSetting('fontSize', sizes[value as number]);
                    }}
                    step={1}
                    marks
                    min={0}
                    max={2}
                    valueLabelDisplay="off"
                  />

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                    <Typography variant="caption">Pequena</Typography>
                    <Typography variant="caption">Média</Typography>
                    <Typography variant="caption">Grande</Typography>
                  </Box>

                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.highContrast}
                        onChange={(e) => updateSetting('highContrast', e.target.checked)}
                      />
                    }
                    label="Modo Alto Contraste"
                  />

                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    Melhora a visibilidade para usuários com deficiência visual.
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>
        )}

        {/* Aba 1: Sistema */}
        {currentTab === 1 && (
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader title="Configurações Gerais" />
                <CardContent>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.autoSave}
                        onChange={(e) => updateSetting('autoSave', e.target.checked)}
                      />
                    }
                    label="Salvamento Automático"
                  />

                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.autoBackup}
                        onChange={(e) => updateSetting('autoBackup', e.target.checked)}
                      />
                    }
                    label="Backup Automático"
                  />

                  <FormControl fullWidth sx={{ mt: 2, mb: 2 }}>
                    <InputLabel>Frequência de Backup</InputLabel>
                    <Select
                      value={settings.backupFrequency}
                      onChange={(e) => updateSetting('backupFrequency', e.target.value)}
                      label="Frequência de Backup"
                      disabled={!settings.autoBackup}
                    >
                      <MenuItem value="hourly">A cada hora</MenuItem>
                      <MenuItem value="daily">Diário</MenuItem>
                      <MenuItem value="weekly">Semanal</MenuItem>
                    </Select>
                  </FormControl>

                  <TextField
                    fullWidth
                    label="Tamanho Máximo de Arquivo (MB)"
                    type="number"
                    value={settings.maxFileSize}
                    onChange={(e) => updateSetting('maxFileSize', parseInt(e.target.value))}
                    sx={{ mb: 2 }}
                  />

                  <Typography gutterBottom>Nível de Compressão</Typography>
                  <Slider
                    value={settings.compressionLevel}
                    onChange={(_, value) => updateSetting('compressionLevel', value)}
                    step={1}
                    marks
                    min={0}
                    max={9}
                    valueLabelDisplay="auto"
                  />
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader title="Limpeza Automática" />
                <CardContent>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.autoCleanup}
                        onChange={(e) => updateSetting('autoCleanup', e.target.checked)}
                      />
                    }
                    label="Limpeza Automática"
                  />

                  <TextField
                    fullWidth
                    label="Dias para Limpeza"
                    type="number"
                    value={settings.cleanupDays}
                    onChange={(e) => updateSetting('cleanupDays', parseInt(e.target.value))}
                    disabled={!settings.autoCleanup}
                    sx={{ mt: 2 }}
                    helperText="Arquivos mais antigos que este período serão removidos automaticamente"
                  />

                  <Alert severity="warning" sx={{ mt: 2 }}>
                    A limpeza automática remove permanentemente arquivos antigos.
                  </Alert>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )}

        {/* Botões de Ação */}
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', gap: 2, justifyContent: 'space-between' }}>
                  <Button
                    variant="outlined"
                    startIcon={<RestoreSharp />}
                    onClick={resetSettings}
                    color="warning"
                  >
                    Resetar para Padrão
                  </Button>

                  <Box sx={{ display: 'flex', gap: 2 }}>
                    <Button
                      variant="contained"
                      startIcon={<Save />}
                      onClick={saveSettings}
                      disabled={!hasChanges}
                      color="primary"
                    >
                      Salvar Configurações
                    </Button>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    </Container>
  );
};

export default SettingsPage;
