import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  Box,
  Typography,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Stepper,
  Step,
  StepLabel,
  useMediaQuery,
  useTheme,
  IconButton,
  Chip
} from '@mui/material';
import {
  Google,
  Security,
  LocationOn,
  Smartphone,
  CheckCircle,
  Warning,
  Close,
  OpenInNew,
  Refresh
} from '@mui/icons-material';

interface MobileOAuthProps {
  open: boolean;
  onClose: () => void;
  onAuthSuccess?: (authData: any) => void;
  onAuthError?: (error: string) => void;
}

interface AuthState {
  status: 'idle' | 'authorizing' | 'processing' | 'success' | 'error';
  message?: string;
  authUrl?: string;
  userData?: any;
}

const MobileOAuth: React.FC<MobileOAuthProps> = ({
  open,
  onClose,
  onAuthSuccess,
  onAuthError
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [authState, setAuthState] = useState<AuthState>({ status: 'idle' });
  const [currentStep, setCurrentStep] = useState(0);

  useEffect(() => {
    if (open) {
      // Reset state when dialog opens
      setAuthState({ status: 'idle' });
      setCurrentStep(0);
    }
  }, [open]);

  const initiateGoogleAuth = async () => {
    setAuthState({ status: 'authorizing', message: 'Redirecionando para Google...' });
    setCurrentStep(1);

    try {
      // Gerar URL de autorização
      const response = await fetch('/api/residence/auth/google', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          scopes: [
            'https://www.googleapis.com/auth/userinfo.email',
            'https://www.googleapis.com/auth/userinfo.profile',
            'https://www.googleapis.com/auth/timeline.readonly'
          ],
          mobile: isMobile
        }),
      });

      if (!response.ok) {
        throw new Error('Erro ao gerar URL de autorização');
      }

      const data = await response.json();
      
      if (data.success && data.authUrl) {
        setAuthState({ 
          status: 'authorizing', 
          message: 'Abrindo Google para autorização...',
          authUrl: data.authUrl 
        });

        // Abrir URL de autorização
        if (isMobile) {
          // Em dispositivos móveis, abrir na mesma janela
          window.location.href = data.authUrl;
        } else {
          // Em desktop, abrir em nova janela
          const authWindow = window.open(
            data.authUrl,
            'google-auth',
            'width=500,height=600,scrollbars=yes,resizable=yes'
          );

          // Monitorar janela de autorização
          const checkClosed = setInterval(() => {
            if (authWindow?.closed) {
              clearInterval(checkClosed);
              checkAuthStatus();
            }
          }, 1000);
        }
      } else {
        throw new Error(data.error || 'Erro desconhecido');
      }
    } catch (error) {
      console.error('Erro na autorização:', error);
      setAuthState({ 
        status: 'error', 
        message: error instanceof Error ? error.message : 'Erro na autorização' 
      });
      onAuthError?.(error instanceof Error ? error.message : 'Erro na autorização');
    }
  };

  const checkAuthStatus = async () => {
    setAuthState({ status: 'processing', message: 'Verificando autorização...' });
    setCurrentStep(2);

    try {
      const response = await fetch('/api/residence/auth/status', {
        method: 'GET',
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Erro ao verificar status de autorização');
      }

      const data = await response.json();

      if (data.success && data.authorized) {
        setAuthState({ 
          status: 'success', 
          message: 'Autorização concluída com sucesso!',
          userData: data.user 
        });
        setCurrentStep(3);
        onAuthSuccess?.(data);
      } else {
        throw new Error('Autorização não concluída');
      }
    } catch (error) {
      console.error('Erro ao verificar status:', error);
      setAuthState({ 
        status: 'error', 
        message: error instanceof Error ? error.message : 'Erro ao verificar autorização' 
      });
      onAuthError?.(error instanceof Error ? error.message : 'Erro ao verificar autorização');
    }
  };

  const retryAuth = () => {
    setAuthState({ status: 'idle' });
    setCurrentStep(0);
  };

  const steps = [
    'Iniciar Autorização',
    'Autorizar no Google',
    'Processar Dados',
    'Concluído'
  ];

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      fullScreen={isMobile}
      PaperProps={{
        sx: {
          ...(isMobile && {
            margin: 0,
            width: '100%',
            height: '100%',
            maxHeight: '100%',
            borderRadius: 0
          })
        }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Google color="primary" />
            <Typography variant="h6">
              Autorização Google Maps
            </Typography>
          </Box>
          <IconButton onClick={onClose}>
            <Close />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        {/* Informações sobre a autorização */}
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="body2" sx={{ mb: 1 }}>
            <strong>O que será acessado:</strong>
          </Typography>
          <List dense>
            <ListItem sx={{ py: 0 }}>
              <ListItemIcon sx={{ minWidth: 32 }}>
                <LocationOn fontSize="small" />
              </ListItemIcon>
              <ListItemText 
                primary="Histórico de localização do Google Maps"
                primaryTypographyProps={{ variant: 'body2' }}
              />
            </ListItem>
            <ListItem sx={{ py: 0 }}>
              <ListItemIcon sx={{ minWidth: 32 }}>
                <Security fontSize="small" />
              </ListItemIcon>
              <ListItemText 
                primary="Informações básicas do perfil (nome, email)"
                primaryTypographyProps={{ variant: 'body2' }}
              />
            </ListItem>
          </List>
        </Alert>

        {/* Stepper de progresso */}
        <Stepper activeStep={currentStep} sx={{ mb: 3 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {/* Estado atual */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              {authState.status === 'authorizing' && (
                <CircularProgress size={24} />
              )}
              {authState.status === 'processing' && (
                <CircularProgress size={24} />
              )}
              {authState.status === 'success' && (
                <CheckCircle color="success" />
              )}
              {authState.status === 'error' && (
                <Warning color="error" />
              )}
              
              <Box sx={{ flex: 1 }}>
                <Typography variant="body1" sx={{ fontWeight: 500 }}>
                  {authState.status === 'idle' && 'Pronto para iniciar'}
                  {authState.status === 'authorizing' && 'Autorizando...'}
                  {authState.status === 'processing' && 'Processando...'}
                  {authState.status === 'success' && 'Sucesso!'}
                  {authState.status === 'error' && 'Erro'}
                </Typography>
                {authState.message && (
                  <Typography variant="body2" color="text.secondary">
                    {authState.message}
                  </Typography>
                )}
              </Box>
            </Box>

            {/* Dados do usuário (se disponível) */}
            {authState.userData && (
              <Box sx={{ mt: 2, pt: 2, borderTop: '1px solid', borderColor: 'divider' }}>
                <Typography variant="body2" sx={{ fontWeight: 500, mb: 1 }}>
                  Conta autorizada:
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Chip 
                    label={authState.userData.email} 
                    size="small" 
                    color="success"
                  />
                </Box>
              </Box>
            )}
          </CardContent>
        </Card>

        {/* Instruções específicas para mobile */}
        {isMobile && authState.status === 'authorizing' && (
          <Alert severity="warning" sx={{ mb: 2 }}>
            <Typography variant="body2">
              <strong>Importante:</strong> Você será redirecionado para o Google. 
              Após autorizar, volte para esta página para continuar.
            </Typography>
          </Alert>
        )}

        {/* Instruções para desktop */}
        {!isMobile && authState.status === 'authorizing' && (
          <Alert severity="info" sx={{ mb: 2 }}>
            <Typography variant="body2">
              Uma nova janela será aberta para autorização. 
              Após autorizar, feche a janela e aguarde a confirmação.
            </Typography>
          </Alert>
        )}

        {/* Botão para abrir URL manualmente (fallback) */}
        {authState.authUrl && authState.status === 'authorizing' && (
          <Box sx={{ textAlign: 'center', mb: 2 }}>
            <Button
              variant="outlined"
              startIcon={<OpenInNew />}
              href={authState.authUrl}
              target="_blank"
              rel="noopener noreferrer"
            >
              Abrir Google Manualmente
            </Button>
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>
          {authState.status === 'success' ? 'Fechar' : 'Cancelar'}
        </Button>
        
        {authState.status === 'idle' && (
          <Button
            variant="contained"
            onClick={initiateGoogleAuth}
            startIcon={<Google />}
          >
            Autorizar com Google
          </Button>
        )}
        
        {authState.status === 'error' && (
          <Button
            variant="contained"
            onClick={retryAuth}
            startIcon={<Refresh />}
          >
            Tentar Novamente
          </Button>
        )}
        
        {authState.status === 'authorizing' && !isMobile && (
          <Button
            variant="outlined"
            onClick={checkAuthStatus}
          >
            Verificar Status
          </Button>
        )}
        
        {authState.status === 'success' && (
          <Button
            variant="contained"
            onClick={() => {
              onClose();
              // Opcional: redirecionar para próxima etapa
            }}
            startIcon={<CheckCircle />}
          >
            Continuar
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default MobileOAuth;
