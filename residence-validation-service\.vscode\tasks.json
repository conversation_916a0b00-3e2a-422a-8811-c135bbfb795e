{"version": "2.0.0", "tasks": [{"type": "npm", "script": "dev", "group": {"kind": "build", "isDefault": true}, "label": "npm: dev - Executar servidor de desenvolvimento", "detail": "Inicia o servidor Next.js em modo desenvolvimento", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"type": "npm", "script": "build", "group": "build", "label": "npm: build - Build para produção", "detail": "Gera build otimizado para produção", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"type": "npm", "script": "start", "group": "build", "label": "npm: start - Executar build de produção", "detail": "Executa o servidor de produção (requer build)", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"type": "npm", "script": "lint", "group": "test", "label": "npm: lint - Verificar código", "detail": "Executa ESLint para verificar qualidade do código", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$eslint-stylish"]}, {"label": "Configu<PERSON>", "type": "shell", "command": "echo", "args": ["Para configurar o ambiente:"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "dependsOrder": "sequence", "dependsOn": ["Verificar Node.js", "Instalar Dependências", "Verificar Env"]}, {"label": "Verificar Node.js", "type": "shell", "command": "node", "args": ["--version"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}}, {"label": "Instalar Dependências", "type": "npm", "script": "install", "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}}, {"label": "Verificar Env", "type": "shell", "command": "echo", "args": ["Verifique se o arquivo .env.local está configurado com suas credenciais do Google"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}]}