const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const dotenv = require('dotenv');
const path = require('path');
const winston = require('winston');

// Carregar variáveis de ambiente
dotenv.config();

// Configurar logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'ai-service' },
  transports: [
    new winston.transports.File({ filename: '/app/logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: '/app/logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});

const app = express();
const PORT = process.env.PORT || 3003;

// Middlewares
app.use(helmet());
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Middleware de logging
app.use((req, res, next) => {
  logger.info(`${req.method} ${req.path}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });
  next();
});

// Importar rotas
const transcriptionRoutes = require('./routes/transcription');
const semanticSearchRoutes = require('./routes/semanticSearch');
const complianceRoutes = require('./routes/compliance');
const qualityRoutes = require('./routes/quality');
const analyticsRoutes = require('./routes/analytics');
const ocrRoutes = require('./routes/ocr');

// Usar rotas
app.use('/api/ai/transcription', transcriptionRoutes);
app.use('/api/ai/semantic-search', semanticSearchRoutes);
app.use('/api/ai/compliance', complianceRoutes);
app.use('/api/ai/quality', qualityRoutes);
app.use('/api/ai/analytics', analyticsRoutes);
app.use('/api/ai/ocr', ocrRoutes);

// Rota de health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: 'ai-service',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Rota de informações da API
app.get('/api', (req, res) => {
  res.json({
    success: true,
    service: 'CartorioTech AI Service',
    version: '1.0.0',
    endpoints: {
      transcription: '/api/ai/transcription',
      semanticSearch: '/api/ai/semantic-search',
      compliance: '/api/ai/compliance',
      quality: '/api/ai/quality',
      analytics: '/api/ai/analytics',
      ocr: '/api/ai/ocr'
    },
    health: '/health',
    timestamp: new Date().toISOString()
  });
});

// Rota de informações dos endpoints de IA
app.get('/api/ai', (req, res) => {
  res.json({
    success: true,
    message: 'Serviço de IA do CartorioTech',
    availableEndpoints: [
      'POST /api/ai/transcription/audio - Transcrição de áudio',
      'POST /api/ai/transcription/batch - Transcrição em lote',
      'GET /api/ai/transcription/status/:id - Status da transcrição',
      'POST /api/ai/semantic-search/search - Busca semântica',
      'POST /api/ai/semantic-search/embed - Gerar embeddings',
      'POST /api/ai/compliance/analyze - Análise de conformidade',
      'POST /api/ai/quality/analyze - Análise de qualidade',
      'GET /api/ai/analytics/summary - Resumo de analytics',
      'POST /api/ai/ocr/process - Processamento OCR'
    ],
    timestamp: new Date().toISOString()
  });
});

// Middleware de tratamento de erros
app.use((error, req, res, next) => {
  logger.error('Erro não tratado:', error);
  res.status(500).json({
    success: false,
    message: 'Erro interno do servidor de IA',
    error: process.env.NODE_ENV === 'development' ? error.message : 'Erro interno'
  });
});

// Middleware para rotas não encontradas
app.use('*', (req, res) => {
  logger.warn(`Rota não encontrada: ${req.method} ${req.originalUrl}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  res.status(404).json({
    success: false,
    message: 'Endpoint não encontrado no serviço de IA',
    requestedPath: req.originalUrl,
    method: req.method,
    availableEndpoints: {
      info: 'GET /api',
      aiInfo: 'GET /api/ai',
      health: 'GET /health',
      transcription: 'POST /api/ai/transcription/*',
      semanticSearch: 'POST /api/ai/semantic-search/*',
      compliance: 'POST /api/ai/compliance/*',
      quality: 'POST /api/ai/quality/*',
      analytics: 'GET /api/ai/analytics/*',
      ocr: 'POST /api/ai/ocr/*'
    },
    timestamp: new Date().toISOString()
  });
});

// Inicializar serviços
const initializeServices = async () => {
  try {
    // Inicializar Redis para filas
    const queueManager = require('./services/queueManager');
    await queueManager.initialize();
    
    // Inicializar banco de dados para IA
    const aiDatabase = require('./database/aiDatabase');
    await aiDatabase.initialize();
    
    // Inicializar o serviço OCR
    const ocrService = require('./services/ocrService');
    await ocrService.initialize().catch(err => {
      logger.warn('Erro ao inicializar OCR, tentará inicializar sob demanda:', err.message);
    });
    
    // Garantir que os diretórios necessários existam
    const fs = require('fs');
    const path = require('path');
    const dirs = [
      path.join(__dirname, '../uploads/ocr'),
      path.join(__dirname, '../results/ocr'),
      path.join(__dirname, '../temp'),
      path.join(__dirname, '../logs')
    ];
    
    dirs.forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        logger.info(`Diretório criado: ${dir}`);
      }
    });
    
    logger.info('Serviços de IA inicializados com sucesso');
  } catch (error) {
    logger.error('Erro ao inicializar serviços de IA:', error);
    process.exit(1);
  }
};

// Iniciar servidor
const startServer = async () => {
  await initializeServices();
  
  app.listen(PORT, () => {
    logger.info(`🤖 Serviço de IA rodando na porta ${PORT}`);
    logger.info(`🔗 Health check: http://localhost:${PORT}/health`);
  });
};

// Tratamento de sinais de sistema
process.on('SIGTERM', async () => {
  logger.info('SIGTERM recebido, encerrando servidor...');
  
  // Finalizar workers do OCR
  const ocrService = require('./services/ocrService');
  await ocrService.terminate().catch(err => {
    logger.warn('Erro ao finalizar OCR workers:', err.message);
  });
  
  process.exit(0);
});

process.on('SIGINT', async () => {
  logger.info('SIGINT recebido, encerrando servidor...');
  
  // Finalizar workers do OCR
  const ocrService = require('./services/ocrService');
  await ocrService.terminate().catch(err => {
    logger.warn('Erro ao finalizar OCR workers:', err.message);
  });
  
  process.exit(0);
});

startServer().catch(error => {
  logger.error('Erro ao iniciar servidor:', error);
  process.exit(1);
});

module.exports = app;
