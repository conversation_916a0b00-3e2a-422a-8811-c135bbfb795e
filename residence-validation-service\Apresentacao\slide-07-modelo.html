<!DOCTYPE html>
<html lang="pt-br">
<head>
  <meta charset="UTF-8">
  <title>ProvaSegura - Slide 7: <PERSON><PERSON></title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');
    
    body {
      margin: 0;
      padding: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      font-family: 'Inter', 'Segoe UI', Arial, sans-serif;
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }
      .slide {
      width: 90vw;
      max-width: 1280px;
      height: 80vh;
      max-height: 720px;
      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
      box-shadow: 0 20px 60px rgba(0,0,0,0.15), 0 8px 32px rgba(0,0,0,0.08);
      border-radius: 24px;
      display: flex;
      flex-direction: row;
      overflow: hidden;
      position: relative;
      border: 1px solid rgba(255,255,255,0.2);
    }
    
    .slide::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #10b981 0%, #059669 50%, #047857 100%);
      border-radius: 24px 24px 0 0;
    }
      .slide-content {
      padding: 40px 60px;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      position: relative;
      z-index: 2;
    }
      .slide-title {
      font-size: 2.5rem;
      font-weight: 800;
      background: linear-gradient(135deg, #059669 0%, #10b981 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-bottom: 30px;
      line-height: 1.1;
      letter-spacing: -0.02em;
    }
    
    .slide-text {
      font-size: 1.2rem;
      color: #334155;
      line-height: 1.7;
      font-weight: 400;
    }
    
    .slide-text ul {
      padding-left: 0;
      list-style: none;
    }
      .slide-text li {
      position: relative;
      padding: 18px 18px 18px 55px;
      margin-bottom: 16px;
      background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
      border-left: 4px solid #10b981;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(16, 185, 129, 0.1);
    }
      .slide-text li::before {
      position: absolute;
      left: 18px;
      top: 18px;
      font-size: 1.5rem;
    }
    
    .slide-text li:nth-child(1)::before { content: '💰'; }
    .slide-text li:nth-child(2)::before { content: '📊'; }
    .slide-text li:nth-child(3)::before { content: '🔗'; }
    .slide-text li:nth-child(4)::before { content: '🤝'; }
    
    .slide-text strong {
      color: #065f46;
      font-weight: 700;
    }
    
    .slide-img {
      flex: 0 0 400px;
      background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      overflow: hidden;
      order: -1;
    }
    
    .slide-img::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 70% 30%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
    }
    
    .slide-img svg {
      filter: drop-shadow(0 15px 35px rgba(16, 185, 129, 0.2));
      z-index: 1;
      position: relative;
    }
    
    .slide-number {
      position: absolute;
      top: 30px;
      right: 40px;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-weight: 700;
      font-size: 1.3rem;
      box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
    }    @media print {
      body { background: #fff; }
      .slide { box-shadow: none; border: 1px solid #e2e8f0; }
    }

    @media (max-width: 768px) {
      .slide {
        width: 95vw;
        height: 90vh;
        flex-direction: column;
        padding: 30px 20px;
      }
      
      .slide-content {
        padding: 20px;
        order: 2;
      }
      
      .slide-title {
        font-size: 2rem;
        margin-bottom: 20px;
      }
      
      .slide-text {
        font-size: 1rem;
      }
      
      .slide-text li {
        padding: 15px 15px 15px 50px;
        margin-bottom: 12px;
      }
      
      .slide-text li::before {
        font-size: 1.3rem;
        left: 15px;
        top: 15px;
      }
      
      .slide-img {
        flex: 0 0 200px;
        order: 1;
      }
      
      .slide-img svg {
        width: 250px;
        height: 200px;
      }
    }

    .navigation {
      position: fixed;
      bottom: 30px;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      gap: 15px;
      z-index: 1000;
    }

    .nav-button {
      background: rgba(255, 255, 255, 0.9);
      border: none;
      padding: 12px 24px;
      border-radius: 25px;
      color: #1e293b;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      text-decoration: none;
      display: inline-block;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .nav-button:hover {
      background: white;
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }
  </style>
</head>
<body>

<div class="slide">
  <div class="slide-number">7</div>
  <div class="slide-content">
    <div class="slide-title">Como Ganhamos Dinheiro</div>
    <div class="slide-text">
      <ul>
        <li><strong>Relatórios individuais:</strong> R$ 20-200 por validação (ex.: R$ 50 para RH, R$ 100 para bancos).</li>
        <li><strong>Assinaturas:</strong> R$ 5.000-100.000/mês para empresas (ex.: bancos, escritórios de advocacia).</li>
        <li><strong>Licenciamento de API:</strong> R$ 50.000-500.000/ano para integração com sistemas de RH, bancos e logística.</li>
        <li><strong>Parcerias:</strong> Acordos com bancos (ex.: Itaú), varejistas (ex.: Casas Bahia) e consultorias de RH.</li>
      </ul>
    </div>
  </div>
  <div class="slide-img">
    <!-- Fluxo de receita melhorado -->
    <svg width="350" height="300" viewBox="0 0 350 300">
      <!-- Cliente -->
      <g transform="translate(60, 150)">
        <circle r="40" fill="#e0f2fe"/>
        <circle r="30" fill="#0891b2"/>
        <text x="0" y="8" text-anchor="middle" font-size="32" fill="#fff">👤</text>
        <text x="0" y="70" text-anchor="middle" font-size="14" font-weight="bold" fill="#0c4a6e">Cliente</text>
      </g>
      
      <!-- App/Plataforma -->
      <g transform="translate(175, 150)">
        <rect x="-35" y="-35" width="70" height="70" rx="16" fill="#f0fdf4" stroke="#10b981" stroke-width="4"/>
        <text x="0" y="8" text-anchor="middle" font-size="32" fill="#10b981">📱</text>
        <text x="0" y="70" text-anchor="middle" font-size="14" font-weight="bold" fill="#065f46">ProvaSegura</text>
      </g>
      
      <!-- Receita -->
      <g transform="translate(290, 150)">
        <rect x="-25" y="-45" width="50" height="90" rx="16" fill="url(#gradient2)"/>
        <text x="0" y="8" text-anchor="middle" font-size="36" fill="#fff">💰</text>
        <text x="0" y="70" text-anchor="middle" font-size="14" font-weight="bold" fill="#1e293b">Receita</text>
      </g>
      
      <!-- Setas de fluxo -->
      <defs>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
          <polygon points="0 0, 10 3.5, 0 7" fill="#10b981" />
        </marker>
      </defs>
      
      <path d="M100 150 L140 150" stroke="#10b981" stroke-width="6" marker-end="url(#arrowhead)"/>
      <path d="M210 150 L250 150" stroke="#10b981" stroke-width="6" marker-end="url(#arrowhead)"/>
      
      <!-- Modelos de receita -->
      <g transform="translate(175, 50)">
        <rect x="-80" y="-20" width="160" height="35" rx="8" fill="#ecfdf5" stroke="#a7f3d0" stroke-width="2"/>
        <text x="0" y="-5" text-anchor="middle" font-size="12" font-weight="bold" fill="#065f46">R$ 20-200 por validação</text>
        <text x="0" y="10" text-anchor="middle" font-size="10" fill="#059669">Modelo Pay-per-Use</text>
      </g>
      
      <g transform="translate(175, 250)">
        <rect x="-90" y="-20" width="180" height="35" rx="8" fill="#ecfdf5" stroke="#a7f3d0" stroke-width="2"/>
        <text x="0" y="-5" text-anchor="middle" font-size="12" font-weight="bold" fill="#065f46">R$ 5K-100K/mês assinaturas</text>
        <text x="0" y="10" text-anchor="middle" font-size="10" fill="#059669">Modelo SaaS</text>
      </g>
      
      <!-- Ícones de parceiros -->
      <g transform="translate(30, 30)">
        <circle r="20" fill="#3b82f6"/>
        <text x="0" y="6" text-anchor="middle" font-size="20" fill="#fff">🏦</text>
      </g>
      
      <g transform="translate(320, 30)">
        <circle r="20" fill="#f59e0b"/>
        <text x="0" y="6" text-anchor="middle" font-size="20" fill="#fff">🏢</text>
      </g>
      
      <g transform="translate(30, 270)">
        <circle r="20" fill="#8b5cf6"/>
        <text x="0" y="6" text-anchor="middle" font-size="20" fill="#fff">⚖️</text>
      </g>
      
      <g transform="translate(320, 270)">
        <circle r="20" fill="#ef4444"/>
        <text x="0" y="6" text-anchor="middle" font-size="20" fill="#fff">🛒</text>
      </g>
      
      <!-- Definições -->
      <defs>
        <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
        </linearGradient>
      </defs>
    </svg>  </div>
</div>

<div class="navigation">
  <a href="slide-06-diferencial.html" class="nav-button">← Anterior</a>
  <a href="index.html" class="nav-button">Índice</a>
  <a href="slide-08-tracao.html" class="nav-button">Próximo →</a>
</div>

<script>
document.addEventListener('keydown', function(event) {
  if (event.key === 'ArrowLeft') {
    window.location.href = 'slide-06-diferencial.html';
  } else if (event.key === 'ArrowRight') {
    window.location.href = 'slide-08-tracao.html';
  } else if (event.key === 'Escape') {
    window.location.href = 'index.html';
  }
});
</script>

</body>
</html>
