// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

/**
 * @title IntegrityRegistry
 * @dev Contrato inteligente para registro imutável de hashes de gravações
 * <AUTHOR>
 */
contract IntegrityRegistry {
    
    // Estrutura para armazenar dados de integridade
    struct IntegrityRecord {
        string fileHash;        // Hash SHA-256 do arquivo
        uint256 timestamp;      // Timestamp do registro
        uint256 blockNumber;    // Número do bloco
        string metadata;        // Metadados em JSON
        address registeredBy;   // Endereço que registrou
        bool exists;           // Flag para verificar existência
    }
    
    // Mapeamento de ID da gravação para registro de integridade
    mapping(string => IntegrityRecord) private records;
    
    // Array para listar todas as gravações registradas
    string[] private recordingIds;
    
    // Mapeamento para verificar se uma gravação já foi registrada
    mapping(string => bool) private recordingExists;
    
    // Endereço do proprietário do contrato
    address public owner;
    
    // Contador de registros
    uint256 public totalRecords;
    
    // Eventos
    event HashStored(
        string indexed recordingId,
        string fileHash,
        uint256 timestamp,
        uint256 blockNumber,
        string metadata,
        address indexed registeredBy
    );
    
    event HashUpdated(
        string indexed recordingId,
        string oldHash,
        string newHash,
        uint256 timestamp,
        address indexed updatedBy
    );
    
    event OwnershipTransferred(
        address indexed previousOwner,
        address indexed newOwner
    );
    
    // Modificadores
    modifier onlyOwner() {
        require(msg.sender == owner, "Apenas o proprietario pode executar esta funcao");
        _;
    }
    
    modifier recordingNotExists(string memory recordingId) {
        require(!recordingExists[recordingId], "Gravacao ja registrada");
        _;
    }
    
    modifier recordingMustExist(string memory recordingId) {
        require(recordingExists[recordingId], "Gravacao nao encontrada");
        _;
    }
    
    modifier validHash(string memory hash) {
        require(bytes(hash).length == 64, "Hash deve ter 64 caracteres (SHA-256)");
        _;
    }
    
    // Construtor
    constructor() {
        owner = msg.sender;
        totalRecords = 0;
    }
    
    /**
     * @dev Armazenar hash de uma gravação
     * @param recordingId ID único da gravação
     * @param fileHash Hash SHA-256 do arquivo
     * @param metadata Metadados em formato JSON
     * @return success True se o registro foi bem-sucedido
     */
    function storeHash(
        string memory recordingId,
        string memory fileHash,
        string memory metadata
    ) 
        public 
        recordingNotExists(recordingId)
        validHash(fileHash)
        returns (bool success) 
    {
        require(bytes(recordingId).length > 0, "ID da gravacao nao pode ser vazio");
        
        // Criar registro
        records[recordingId] = IntegrityRecord({
            fileHash: fileHash,
            timestamp: block.timestamp,
            blockNumber: block.number,
            metadata: metadata,
            registeredBy: msg.sender,
            exists: true
        });
        
        // Adicionar à lista de gravações
        recordingIds.push(recordingId);
        recordingExists[recordingId] = true;
        totalRecords++;
        
        // Emitir evento
        emit HashStored(
            recordingId,
            fileHash,
            block.timestamp,
            block.number,
            metadata,
            msg.sender
        );
        
        return true;
    }
    
    /**
     * @dev Recuperar registro de uma gravação
     * @param recordingId ID da gravação
     * @return fileHash Hash do arquivo
     * @return timestamp Timestamp do registro
     * @return blockNumber Número do bloco
     * @return metadata Metadados
     */
    function getRecord(string memory recordingId)
        public
        view
        returns (
            string memory fileHash,
            uint256 timestamp,
            uint256 blockNumber,
            string memory metadata
        )
    {
        IntegrityRecord memory record = records[recordingId];
        
        return (
            record.fileHash,
            record.timestamp,
            record.blockNumber,
            record.metadata
        );
    }
    
    /**
     * @dev Verificar se uma gravação existe
     * @param recordingId ID da gravação
     * @return exists True se a gravação existe
     */
    function recordExists(string memory recordingId)
        public
        view
        returns (bool exists)
    {
        return recordingExists[recordingId];
    }
    
    /**
     * @dev Obter informações completas de um registro
     * @param recordingId ID da gravação
     * @return record Estrutura completa do registro
     */
    function getFullRecord(string memory recordingId)
        public
        view
        recordingMustExist(recordingId)
        returns (IntegrityRecord memory record)
    {
        return records[recordingId];
    }
    
    /**
     * @dev Verificar integridade comparando hashes
     * @param recordingId ID da gravação
     * @param currentHash Hash atual do arquivo
     * @return isValid True se os hashes coincidem
     * @return storedHash Hash armazenado na blockchain
     */
    function verifyIntegrity(string memory recordingId, string memory currentHash)
        public
        view
        recordingMustExist(recordingId)
        validHash(currentHash)
        returns (bool isValid, string memory storedHash)
    {
        IntegrityRecord memory record = records[recordingId];
        storedHash = record.fileHash;
        isValid = keccak256(abi.encodePacked(record.fileHash)) == keccak256(abi.encodePacked(currentHash));
        
        return (isValid, storedHash);
    }
    
    /**
     * @dev Atualizar hash de uma gravação (apenas proprietário)
     * @param recordingId ID da gravação
     * @param newHash Novo hash do arquivo
     * @param newMetadata Novos metadados
     * @return success True se a atualização foi bem-sucedida
     */
    function updateHash(
        string memory recordingId,
        string memory newHash,
        string memory newMetadata
    )
        public
        onlyOwner
        recordingMustExist(recordingId)
        validHash(newHash)
        returns (bool success)
    {
        IntegrityRecord storage record = records[recordingId];
        string memory oldHash = record.fileHash;
        
        // Atualizar registro
        record.fileHash = newHash;
        record.metadata = newMetadata;
        record.timestamp = block.timestamp;
        record.blockNumber = block.number;
        
        // Emitir evento
        emit HashUpdated(
            recordingId,
            oldHash,
            newHash,
            block.timestamp,
            msg.sender
        );
        
        return true;
    }
    
    /**
     * @dev Obter lista de todas as gravações registradas
     * @return ids Array com IDs de todas as gravações
     */
    function getAllRecordingIds()
        public
        view
        returns (string[] memory ids)
    {
        return recordingIds;
    }
    
    /**
     * @dev Obter gravações registradas em um intervalo
     * @param start Índice inicial
     * @param limit Número máximo de registros
     * @return ids Array com IDs das gravações
     */
    function getRecordingIds(uint256 start, uint256 limit)
        public
        view
        returns (string[] memory ids)
    {
        require(start < recordingIds.length, "Indice inicial invalido");
        
        uint256 end = start + limit;
        if (end > recordingIds.length) {
            end = recordingIds.length;
        }
        
        string[] memory result = new string[](end - start);
        for (uint256 i = start; i < end; i++) {
            result[i - start] = recordingIds[i];
        }
        
        return result;
    }
    
    /**
     * @dev Obter estatísticas do contrato
     * @return total Total de registros
     * @return contractOwner Endereço do proprietário
     * @return contractBalance Saldo do contrato
     */
    function getContractStats()
        public
        view
        returns (
            uint256 total,
            address contractOwner,
            uint256 contractBalance
        )
    {
        return (
            totalRecords,
            owner,
            address(this).balance
        );
    }
    
    /**
     * @dev Transferir propriedade do contrato
     * @param newOwner Novo proprietário
     */
    function transferOwnership(address newOwner)
        public
        onlyOwner
    {
        require(newOwner != address(0), "Novo proprietario nao pode ser endereco zero");
        require(newOwner != owner, "Novo proprietario deve ser diferente do atual");
        
        address previousOwner = owner;
        owner = newOwner;
        
        emit OwnershipTransferred(previousOwner, newOwner);
    }
    
    /**
     * @dev Função para receber Ether
     */
    receive() external payable {
        // Permite que o contrato receba Ether
    }
    
    /**
     * @dev Retirar Ether do contrato (apenas proprietário)
     * @param amount Quantidade a retirar (em wei)
     */
    function withdraw(uint256 amount)
        public
        onlyOwner
    {
        require(amount <= address(this).balance, "Saldo insuficiente");
        require(amount > 0, "Quantidade deve ser maior que zero");
        
        payable(owner).transfer(amount);
    }
    
    /**
     * @dev Função de emergência para pausar o contrato
     */
    bool public paused = false;
    
    modifier whenNotPaused() {
        require(!paused, "Contrato pausado");
        _;
    }
    
    function pause() public onlyOwner {
        paused = true;
    }
    
    function unpause() public onlyOwner {
        paused = false;
    }
    
    // Modificar funções principais para incluir verificação de pausa
    function storeHashSafe(
        string memory recordingId,
        string memory fileHash,
        string memory metadata
    ) 
        public 
        whenNotPaused
        returns (bool success) 
    {
        return storeHash(recordingId, fileHash, metadata);
    }
}
