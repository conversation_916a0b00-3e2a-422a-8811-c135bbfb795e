import { LocationData, NightlyPresenceRecord, ValidationResult, ValidationConfig } from '@/types';
import { CONFIG } from '@/lib/config';

/**
 * Calcula a distância entre duas coordenadas usando a fórmula de Haversine
 */
export function calculateDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number {
  const R = 6371000; // Raio da Terra em metros
  const φ1 = (lat1 * Math.PI) / 180;
  const φ2 = (lat2 * Math.PI) / 180;
  const Δφ = ((lat2 - lat1) * Math.PI) / 180;
  const Δλ = ((lon2 - lon1) * Math.PI) / 180;

  const a =
    Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
    Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return R * c; // Distância em metros
}

/**
 * Verifica se um ponto está dentro do raio de residência
 */
export function isWithinHomeRadius(
  pointLat: number,
  pointLon: number,
  homeLat: number,
  homeLon: number,
  maxDistance: number = CONFIG.VALIDATION.MAX_DISTANCE_FROM_HOME
): boolean {
  const distance = calculateDistance(pointLat, pointLon, homeLat, homeLon);
  return distance <= maxDistance;
}

/**
 * Converte timestamp para horário local
 */
export function formatTimestamp(timestamp: string): Date {
  return new Date(timestamp);
}

/**
 * Verifica se um horário está dentro do período noturno (18h às 6h)
 */
export function isNightTime(date: Date): boolean {
  const hour = date.getHours();
  return hour >= 18 || hour < 6;
}

/**
 * Agrupa dados de localização por dia
 */
export function groupLocationsByDay(locations: LocationData[]): Record<string, LocationData[]> {
  return locations.reduce((groups, location) => {
    const date = formatTimestamp(location.timestamp).toISOString().split('T')[0];
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(location);
    return groups;
  }, {} as Record<string, LocationData[]>);
}

/**
 * Analisa a presença noturna em um dia específico
 */
export function analyzeNightlyPresence(
  dayLocations: LocationData[],
  homeLat: number,
  homeLon: number,
  date: string
): NightlyPresenceRecord {
  // Filtra apenas os pontos do período noturno (18h às 6h)
  const nightlyPoints = dayLocations.filter(location => {
    const locationDate = formatTimestamp(location.timestamp);
    return isNightTime(locationDate);
  });

  // Conta quantos pontos estão dentro do raio de casa
  const homePoints = nightlyPoints.filter(location =>
    isWithinHomeRadius(location.latitude, location.longitude, homeLat, homeLon)
  );

  // Calcula estatísticas
  const totalPoints = nightlyPoints.length;
  const homePointsCount = homePoints.length;
  const presenceRatio = totalPoints > 0 ? homePointsCount / totalPoints : 0;

  // Calcula distância média de casa
  const distances = nightlyPoints.map(location =>
    calculateDistance(location.latitude, location.longitude, homeLat, homeLon)
  );
  const averageDistance = distances.length > 0 
    ? distances.reduce((sum, dist) => sum + dist, 0) / distances.length 
    : 0;

  // Determina se a pessoa estava presente
  const isPresent = presenceRatio >= CONFIG.VALIDATION.CONFIDENCE_THRESHOLD;
  const confidence = Math.min(presenceRatio, 1.0);

  return {
    date,
    startTime: '18:00',
    endTime: '06:00',
    isPresent,
    confidence,
    locationPoints: nightlyPoints,
    distanceFromHome: averageDistance,
  };
}

/**
 * Valida residência com base nos dados de localização
 */
export function validateResidence(
  locations: LocationData[],
  homeLat: number,
  homeLon: number,
  config: ValidationConfig = {
    minimumDays: CONFIG.VALIDATION.MINIMUM_DAYS,
    nightStartTime: CONFIG.VALIDATION.NIGHT_START_TIME,
    nightEndTime: CONFIG.VALIDATION.NIGHT_END_TIME,
    maxDistanceFromHome: CONFIG.VALIDATION.MAX_DISTANCE_FROM_HOME,
    minimumPresenceHours: CONFIG.VALIDATION.MINIMUM_PRESENCE_HOURS,
    confidenceThreshold: CONFIG.VALIDATION.CONFIDENCE_THRESHOLD,
  }
): ValidationResult {
  // Agrupa localizações por dia
  const locationsByDay = groupLocationsByDay(locations);
  
  // Analisa presença noturna para cada dia
  const nightlyRecords: NightlyPresenceRecord[] = [];
  
  for (const [date, dayLocations] of Object.entries(locationsByDay)) {
    const record = analyzeNightlyPresence(dayLocations, homeLat, homeLon, date);
    nightlyRecords.push(record);
  }

  // Ordena por data
  nightlyRecords.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

  // Conta dias válidos consecutivos
  let consecutiveDays = 0;
  let maxConsecutiveDays = 0;
  let validDays = 0;
  for (const record of nightlyRecords) {
    if (record.isPresent && record.confidence >= config.confidenceThreshold) {
      consecutiveDays++;
      validDays++;
      maxConsecutiveDays = Math.max(maxConsecutiveDays, consecutiveDays);
    } else {
      consecutiveDays = 0;
    }
  }

  // Calcula estatísticas gerais
  const totalDays = nightlyRecords.length;
  const validationScore = totalDays > 0 ? validDays / totalDays : 0;
  const averageDistance = nightlyRecords.length > 0
    ? nightlyRecords.reduce((sum, record) => sum + record.distanceFromHome, 0) / nightlyRecords.length
    : 0;
  // Determina se a validação é bem-sucedida
  const isValid = maxConsecutiveDays >= config.minimumDays;
  const confidence = Math.min(validationScore, 1.0);

  return {
    isValid,
    score: validationScore,
    validDays,
    totalDays,
    nightlyRecords,
    averageDistance,
    confidence,
  };
}

/**
 * Gera um número de certificado único
 */
export function generateCertificateNumber(): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  return `RES-${timestamp}-${random}`.toUpperCase();
}

/**
 * Formata data para exibição
 */
export function formatDate(date: string | Date): string {
  const d = typeof date === 'string' ? new Date(date) : date;
  return d.toLocaleDateString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  });
}

/**
 * Formata horário para exibição
 */
export function formatTime(date: string | Date): string {
  const d = typeof date === 'string' ? new Date(date) : date;
  return d.toLocaleTimeString('pt-BR', {
    hour: '2-digit',
    minute: '2-digit',
  });
}

/**
 * Gera um hash simples para verificação de integridade
 */
export function generateHash(data: string): string {
  let hash = 0;
  for (let i = 0; i < data.length; i++) {
    const char = data.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Converte para 32-bit
  }
  return Math.abs(hash).toString(16);
}
