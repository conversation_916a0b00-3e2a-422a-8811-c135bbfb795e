# 🏠 **GUIA COMPLETO - VALIDAÇÃO DE RESIDÊNCIA**

## 📋 **COMO USAR O SISTEMA**

### **🏢 PARA O CARTÓRIO:**

#### **Passo 1: Acessar o Sistema**
1. Abra `http://localhost:3000/residence`
2. Clique no botão **"🏢 Gerar Link para Cliente (Cartório)"**

#### **Passo 2: Preencher Dados do Cliente**
- **Nome Completo** *(obrigatório)*
- **Email** *(obrigatório)*
- **Telefone/WhatsApp** *(opcional)*
- **CPF/CNPJ** *(opcional)*
- **Endereço a ser Validado** *(obrigatório)*
- **Finalidade** *(ex: "Comprovante de residência para abertura de conta bancária")*

#### **Passo 3: Gerar e Enviar Link**
1. Clique em **"Próximo"** → **"Gerar Link"**
2. **Copie o link** gerado
3. **Copie a mensagem** sugerida para WhatsApp
4. **Envie via WhatsApp** para o cliente

**Exemplo de mensagem:**
```
Olá João Silva! 👋

Para validar seu comprovante de residência, clique no link abaixo:
http://localhost:3000/residence/auth/abc123?client=João%20Silva

⚠️ Importante:
• Abra o link no seu celular
• Use a mesma conta Google do seu telefone
• O processo é seguro e protegido

Cartório TechSolutions
```

---

### **📱 PARA O CLIENTE:**

#### **Passo 1: Abrir o Link**
- Cliente recebe o link via WhatsApp
- **Deve abrir no celular** (não no computador)
- Link é único e personalizado

#### **Passo 2: Autorizar com Google**
1. Clique em **"Começar Validação"**
2. Clique em **"Autorizar com Google"**
3. **Fazer login** na conta Google
4. **Autorizar acesso** ao histórico de localização

#### **Passo 3: Aguardar Processamento**
- Sistema analisa automaticamente
- Verifica presença noturna no endereço
- Gera certificado preliminar

---

## ⚙️ **CONFIGURAÇÃO NECESSÁRIA**

### **🔧 Google OAuth (OBRIGATÓRIO)**

**Problema atual:** Credenciais não configuradas

**Solução:**
1. Siga o arquivo `GOOGLE_OAUTH_SETUP.md`
2. Configure no Google Cloud Console
3. Atualize o arquivo `.env`

**Status:** ⚠️ **PENDENTE** - Sistema não funcionará sem isso

---

## 🔍 **COMO FUNCIONA A VALIDAÇÃO**

### **Algoritmo de Análise:**
1. **Coleta dados** do Google Maps Timeline
2. **Filtra período noturno** (18h às 6h)
3. **Verifica presença** em raio de 100m do endereço
4. **Calcula percentual** de presença
5. **Gera certificado** se ≥ 70% de presença em 15+ dias

### **Critérios de Aprovação:**
- ✅ **Mínimo 15 dias** consecutivos
- ✅ **70% de presença** no período noturno
- ✅ **Raio de 100 metros** do endereço informado
- ✅ **Dados do Google Timeline** ativados

---

## 📊 **TIPOS DE CERTIFICADO**

### **🟡 Certificado Preliminar (Gratuito)**
- Gerado automaticamente pelo sistema
- **NÃO tem validade legal**
- Serve para análise prévia
- Formato PDF com QR Code

### **🟢 Certificado Legal (R$ 8-15)**
- Certificado preliminar + autenticação cartorária
- **TEM validade legal**
- Aceito por bancos e órgãos públicos
- Processo: 1-3 dias úteis

---

## ❌ **PROBLEMAS COMUNS E SOLUÇÕES**

### **1. "Endpoint não encontrado"**
**Causa:** Rota não registrada
**Solução:** ✅ **CORRIGIDO** - Rota `/api/residence` adicionada

### **2. "Erro ao gerar URL de autorização"**
**Causa:** Google OAuth não configurado
**Solução:** Configure seguindo `GOOGLE_OAUTH_SETUP.md`

### **3. "Timeline não encontrado"**
**Causa:** Cliente não tem histórico ativado
**Solução:** Cliente deve:
- Ativar **Histórico de localização** no Google Maps
- Aguardar 15-30 dias para acumular dados
- Usar o celular normalmente

### **4. "Dados insuficientes"**
**Causa:** Menos de 15 dias de dados
**Solução:** Aguardar mais tempo ou usar upload manual

---

## 🔄 **FLUXO ALTERNATIVO - UPLOAD MANUAL**

Se o Google OAuth não funcionar:

1. **Cliente exporta** dados do Google Takeout
2. **Faz upload** do arquivo JSON
3. **Sistema processa** localmente
4. **Gera certificado** da mesma forma

---

## 📈 **MÉTRICAS E RELATÓRIOS**

### **Para o Cartório:**
- Número de validações realizadas
- Taxa de sucesso/falha
- Tempo médio de processamento
- Relatórios de compliance LGPD

### **Para o Cliente:**
- Histórico de validações
- Status do processamento
- Download de certificados
- Renovação automática

---

## 🔒 **SEGURANÇA E PRIVACIDADE**

### **Dados Coletados:**
- ✅ Apenas localização do período solicitado
- ✅ Informações básicas do perfil (nome, email)
- ✅ Endereço a ser validado

### **Proteções:**
- 🔐 **Criptografia** de todos os dados
- 🗑️ **Exclusão automática** após 90 dias
- 🚫 **Não compartilhamento** com terceiros
- ✅ **Conformidade LGPD**

---

## 📞 **SUPORTE**

### **Para Cartórios:**
- Treinamento completo da equipe
- Suporte técnico 24/7
- Integração com sistemas existentes
- Relatórios personalizados

### **Para Clientes:**
- FAQ completo no sistema
- Vídeos explicativos
- Chat de suporte
- Telefone de emergência

---

## 🎯 **PRÓXIMOS PASSOS**

1. **✅ CONCLUÍDO:** Interface e fluxo implementados
2. **⚠️ PENDENTE:** Configurar Google OAuth
3. **📋 PLANEJADO:** Testes com clientes reais
4. **🚀 FUTURO:** Integração com outros cartórios

---

**📧 Dúvidas?** Entre em contato com a equipe técnica.
**🔧 Problemas?** Consulte os logs em `docker-compose logs backend`
