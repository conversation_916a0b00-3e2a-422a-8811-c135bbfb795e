#!/bin/bash

# ==============================================================================
# Script de Verificação de Variáveis de Ambiente
# ==============================================================================
# 
# Este script verifica se todas as variáveis de ambiente necessárias
# estão configuradas corretamente antes de executar o Docker
# 
# Uso: ./check-env.sh
# ==============================================================================

echo "🔍 Verificando configuração de variáveis de ambiente..."
echo "================================================"

# Função para verificar se uma variável está definida
check_var() {
    local var_name=$1
    local var_value=${!var_name}
    
    if [ -z "$var_value" ]; then
        echo "❌ $var_name: NÃO CONFIGURADO"
        return 1
    elif [ "$var_value" = "your_google_client_id_here" ] || 
         [ "$var_value" = "your_google_client_secret_here" ] || 
         [ "$var_value" = "your_super_secret_jwt_key_here_change_in_production" ] ||
         [ "$var_value" = "your_32_char_encryption_key_here!!" ]; then
        echo "⚠️  $var_name: VALOR PADRÃO (precisa ser alterado)"
        return 1
    else
        echo "✅ $var_name: CONFIGURADO"
        return 0
    fi
}

# Função para verificar se o arquivo .env.production existe
check_env_file() {
    if [ ! -f ".env.production" ]; then
        echo "❌ Arquivo .env.production não encontrado!"
        echo "   Copie o arquivo .env.production.example para .env.production"
        echo "   e configure os valores reais."
        return 1
    else
        echo "✅ Arquivo .env.production encontrado"
        return 0
    fi
}

# Carregar variáveis do arquivo .env.production se existir
if [ -f ".env.production" ]; then
    export $(grep -v '^#' .env.production | xargs)
fi

echo ""
echo "📁 Verificando arquivo .env.production..."
check_env_file
ENV_FILE_OK=$?

echo ""
echo "🔐 Verificando variáveis obrigatórias..."
check_var "GOOGLE_CLIENT_ID"
GOOGLE_ID_OK=$?

check_var "GOOGLE_CLIENT_SECRET"
GOOGLE_SECRET_OK=$?

check_var "NEXT_PUBLIC_GOOGLE_REDIRECT_URI"
REDIRECT_URI_OK=$?

check_var "JWT_SECRET"
JWT_OK=$?

check_var "ENCRYPTION_KEY"
ENCRYPTION_OK=$?

check_var "NEXT_PUBLIC_BASE_URL"
BASE_URL_OK=$?

echo ""
echo "📊 Verificações adicionais..."

# Verificar se a ENCRYPTION_KEY tem 32 caracteres
if [ -n "$ENCRYPTION_KEY" ] && [ ${#ENCRYPTION_KEY} -ne 32 ]; then
    echo "⚠️  ENCRYPTION_KEY deve ter exatamente 32 caracteres (atual: ${#ENCRYPTION_KEY})"
    ENCRYPTION_OK=1
fi

# Verificar se JWT_SECRET tem pelo menos 32 caracteres
if [ -n "$JWT_SECRET" ] && [ ${#JWT_SECRET} -lt 32 ]; then
    echo "⚠️  JWT_SECRET deve ter pelo menos 32 caracteres (atual: ${#JWT_SECRET})"
    JWT_OK=1
fi

# Verificar se REDIRECT_URI corresponde à BASE_URL
if [ -n "$NEXT_PUBLIC_GOOGLE_REDIRECT_URI" ] && [ -n "$NEXT_PUBLIC_BASE_URL" ]; then
    if [[ "$NEXT_PUBLIC_GOOGLE_REDIRECT_URI" != "$NEXT_PUBLIC_BASE_URL"* ]]; then
        echo "⚠️  REDIRECT_URI deve começar com BASE_URL"
    fi
fi

echo ""
echo "================================================"

# Calcular resultado final
TOTAL_ERRORS=$((ENV_FILE_OK + GOOGLE_ID_OK + GOOGLE_SECRET_OK + REDIRECT_URI_OK + JWT_OK + ENCRYPTION_OK + BASE_URL_OK))

if [ $TOTAL_ERRORS -eq 0 ]; then
    echo "🎉 Todas as verificações passaram!"
    echo "   Você pode executar: docker-compose -f docker-compose.prod.yml up -d"
    echo ""
    echo "🔗 Após iniciar, verifique o endpoint de debug:"
    echo "   curl http://localhost:3000/api/debug/env"
    exit 0
else
    echo "❌ $TOTAL_ERRORS erro(s) encontrado(s)!"
    echo ""
    echo "📋 Passos para corrigir:"
    if [ $ENV_FILE_OK -ne 0 ]; then
        echo "   1. Copie: cp .env.production.example .env.production"
    fi
    echo "   2. Edite o arquivo .env.production com valores reais"
    echo "   3. Configure suas credenciais do Google OAuth2"
    echo "   4. Execute este script novamente para verificar"
    echo ""
    echo "📖 Veja o guia completo em: OAUTH_SETUP_GUIDE.md"
    exit 1
fi
