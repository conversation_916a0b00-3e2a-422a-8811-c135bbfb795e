#!/bin/bash

# Script para reconstruir e testar o sistema integrado com validação de residência

echo "=== Iniciando rebuild do sistema integrado ==="

# Passo 1: Parar containers existentes
echo "Parando containers existentes..."
docker-compose down

# Passo 2: Remover imagens antigas para garantir rebuild completo
echo "Removendo imagens antigas..."
docker-compose down --rmi all --volumes --remove-orphans

# Passo 3: Limpar cache do Docker
echo "Limpando cache do Docker..."
docker system prune -f

# Passo 4: Rebuild com cache limpo
echo "Reconstruindo containers..."
docker-compose build --no-cache

# Passo 5: Subir o sistema
echo "Subindo o sistema..."
docker-compose up -d

# Passo 6: Aguardar inicialização
echo "Aguardando inicialização dos serviços..."
sleep 30

# Passo 7: Verificar status dos containers
echo "Verificando status dos containers..."
docker-compose ps

# Passo 8: Verificar logs
echo "Verificando logs do backend..."
docker-compose logs backend --tail=50

echo "Verificando logs do web-app..."
docker-compose logs web-app --tail=50

# Passo 9: Testar endpoints
echo "Testando endpoints..."
echo "Frontend: http://localhost"
echo "Backend: http://localhost:3001"
echo "Residence API: http://localhost:3001/api/residence"

# Passo 10: Verificar conectividade
echo "Testando conectividade..."
curl -f http://localhost:3001/health || echo "Backend não está respondendo"
curl -f http://localhost || echo "Frontend não está respondendo"

echo "=== Rebuild concluído ==="
echo "Acesse o sistema em: http://localhost"
echo "A nova funcionalidade de Validação de Residência estará disponível no menu lateral"
