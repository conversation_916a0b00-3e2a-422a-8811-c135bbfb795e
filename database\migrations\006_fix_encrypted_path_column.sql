-- Migração para corrigir problema da coluna encrypted_path
-- Data: 2025-07-19
-- Descrição: <PERSON><PERSON><PERSON><PERSON> que todas as colunas necessárias existam na tabela recordings

-- Verificar e criar colunas que podem estar faltando
DO $$
BEGIN
    -- Verificar se a coluna encrypted_path existe
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'recordings' AND column_name = 'encrypted_path') THEN
        ALTER TABLE recordings ADD COLUMN encrypted_path VARCHAR(500);
        RAISE NOTICE 'Coluna encrypted_path adicionada à tabela recordings';
    ELSE
        RAISE NOTICE 'Coluna encrypted_path já existe na tabela recordings';
    END IF;
    
    -- Verificar se a coluna encrypted_hash existe
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'recordings' AND column_name = 'encrypted_hash') THEN
        ALTER TABLE recordings ADD COLUMN encrypted_hash VARCHAR(64);
        RAISE NOTICE 'Coluna encrypted_hash adicionada à tabela recordings';
    ELSE
        RAISE NOTICE 'Coluna encrypted_hash já existe na tabela recordings';
    END IF;
    
    -- Verificar se a coluna file_hash existe
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'recordings' AND column_name = 'file_hash') THEN
        ALTER TABLE recordings ADD COLUMN file_hash VARCHAR(64);
        RAISE NOTICE 'Coluna file_hash adicionada à tabela recordings';
    ELSE
        RAISE NOTICE 'Coluna file_hash já existe na tabela recordings';
    END IF;
    
    -- Verificar se a coluna encrypted existe
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'recordings' AND column_name = 'encrypted') THEN
        ALTER TABLE recordings ADD COLUMN encrypted BOOLEAN DEFAULT FALSE;
        RAISE NOTICE 'Coluna encrypted adicionada à tabela recordings';
    ELSE
        RAISE NOTICE 'Coluna encrypted já existe na tabela recordings';
    END IF;
    
    -- Verificar se a coluna digital_signature existe
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'recordings' AND column_name = 'digital_signature') THEN
        ALTER TABLE recordings ADD COLUMN digital_signature TEXT;
        RAISE NOTICE 'Coluna digital_signature adicionada à tabela recordings';
    ELSE
        RAISE NOTICE 'Coluna digital_signature já existe na tabela recordings';
    END IF;
    
    -- Verificar se a coluna verification_status existe
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'recordings' AND column_name = 'verification_status') THEN
        ALTER TABLE recordings ADD COLUMN verification_status VARCHAR(20) DEFAULT 'PENDING';
        RAISE NOTICE 'Coluna verification_status adicionada à tabela recordings';
    ELSE
        RAISE NOTICE 'Coluna verification_status já existe na tabela recordings';
    END IF;
    
    -- Verificar se a coluna last_verification_at existe
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'recordings' AND column_name = 'last_verification_at') THEN
        ALTER TABLE recordings ADD COLUMN last_verification_at TIMESTAMP;
        RAISE NOTICE 'Coluna last_verification_at adicionada à tabela recordings';
    ELSE
        RAISE NOTICE 'Coluna last_verification_at já existe na tabela recordings';
    END IF;
    
    -- Verificar se a coluna integrity_verified existe
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'recordings' AND column_name = 'integrity_verified') THEN
        ALTER TABLE recordings ADD COLUMN integrity_verified BOOLEAN DEFAULT FALSE;
        RAISE NOTICE 'Coluna integrity_verified adicionada à tabela recordings';
    ELSE
        RAISE NOTICE 'Coluna integrity_verified já existe na tabela recordings';
    END IF;
END $$;

-- Criar índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_recordings_encrypted_path ON recordings(encrypted_path) WHERE encrypted_path IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_recordings_file_hash ON recordings(file_hash) WHERE file_hash IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_recordings_encrypted_hash ON recordings(encrypted_hash) WHERE encrypted_hash IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_recordings_verification_status ON recordings(verification_status);
CREATE INDEX IF NOT EXISTS idx_recordings_last_verification ON recordings(last_verification_at);
CREATE INDEX IF NOT EXISTS idx_recordings_encrypted ON recordings(encrypted) WHERE encrypted = true;

-- Comentário final
COMMENT ON TABLE recordings IS 'Tabela principal de gravações com suporte completo a criptografia e assinatura digital';
