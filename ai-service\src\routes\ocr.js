const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const OCRService = require('../services/ocrService');
const queueManager = require('../services/queueManager');

const router = express.Router();
const ocrService = new OCRService();

// Configuração do multer para upload de imagens
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads/ocr');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, `ocr-${uniqueSuffix}${path.extname(file.originalname)}`);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 50 * 1024 * 1024 // 50MB
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp',
      'image/tiff', 'image/webp', 'application/pdf'
    ];
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Tipo de arquivo não suportado'), false);
    }
  }
});

/**
 * GET /api/ai/ocr
 * Informações sobre o serviço de OCR
 */
router.get('/', (req, res) => {
  res.json({
    success: true,
    service: 'Serviço de OCR (Reconhecimento Óptico de Caracteres)',
    version: '1.0.0',
    availableEndpoints: [
      'POST /api/ai/ocr/process - Processar imagem/PDF com OCR',
      'POST /api/ai/ocr/batch - Processamento em lote',
      'GET /api/ai/ocr/result/:id - Obter resultado do OCR',
      'POST /api/ai/ocr/document - OCR específico para documentos',
      'POST /api/ai/ocr/table - Extração de tabelas'
    ],
    supportedFormats: [
      'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 
      'image/bmp', 'image/tiff', 'image/webp', 'application/pdf'
    ],
    maxFileSize: '50MB',
    features: [
      'OCR de alta precisão',
      'Suporte a múltiplos idiomas',
      'Detecção de layout',
      'Extração de tabelas',
      'Reconhecimento de documentos',
      'Processamento em lote',
      'Correção automática',
      'Confiança por palavra'
    ],
    languages: [
      'Português (pt)', 'Inglês (en)', 'Espanhol (es)',
      'Francês (fr)', 'Alemão (de)', 'Italiano (it)'
    ],
    timestamp: new Date().toISOString()
  });
});

/**
 * POST /api/ai/ocr/process
 * Processar imagem ou PDF com OCR
 */
router.post('/process', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'Arquivo é obrigatório'
      });
    }

    const { language = 'pt', options = {} } = req.body;
    const filePath = req.file.path;

    // Adicionar à fila de processamento
    const jobId = await queueManager.addJob('ocr-processing', {
      filePath,
      language,
      options: typeof options === 'string' ? JSON.parse(options) : options,
      originalName: req.file.originalname,
      timestamp: new Date().toISOString()
    });

    res.json({
      success: true,
      message: 'Processamento OCR iniciado',
      jobId,
      estimatedTime: '1-3 minutos'
    });

  } catch (error) {
    console.error('Erro no processamento OCR:', error);
    
    // Limpar arquivo em caso de erro
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
});

/**
 * POST /api/ai/ocr/batch
 * Processamento OCR em lote
 */
router.post('/batch', upload.array('files', 10), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Pelo menos um arquivo é obrigatório'
      });
    }

    const { language = 'pt', options = {} } = req.body;
    const jobIds = [];

    for (const file of req.files) {
      const jobId = await queueManager.addJob('ocr-processing', {
        filePath: file.path,
        language,
        options: typeof options === 'string' ? JSON.parse(options) : options,
        originalName: file.originalname,
        timestamp: new Date().toISOString()
      });
      jobIds.push(jobId);
    }

    res.json({
      success: true,
      message: 'Processamento OCR em lote iniciado',
      jobIds,
      totalFiles: req.files.length,
      estimatedTime: `${req.files.length * 2}-${req.files.length * 4} minutos`
    });

  } catch (error) {
    console.error('Erro no processamento OCR em lote:', error);
    
    // Limpar arquivos em caso de erro
    if (req.files) {
      req.files.forEach(file => {
        if (fs.existsSync(file.path)) {
          fs.unlinkSync(file.path);
        }
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
});

/**
 * GET /api/ai/ocr/result/:id
 * Obter resultado do processamento OCR
 */
router.get('/result/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const result = await ocrService.getOCRResult(id);

    if (!result) {
      return res.status(404).json({
        success: false,
        message: 'Resultado OCR não encontrado'
      });
    }

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Erro ao obter resultado OCR:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
});

/**
 * POST /api/ai/ocr/document
 * OCR específico para documentos estruturados
 */
router.post('/document', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'Arquivo é obrigatório'
      });
    }

    const { documentType = 'generic', language = 'pt' } = req.body;
    const filePath = req.file.path;

    const result = await ocrService.processDocument(filePath, {
      documentType,
      language,
      extractStructure: true,
      detectFields: true
    });

    // Limpar arquivo após processamento
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Erro no processamento de documento:', error);
    
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
});

/**
 * POST /api/ai/ocr/table
 * Extração específica de tabelas
 */
router.post('/table', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'Arquivo é obrigatório'
      });
    }

    const { format = 'json' } = req.body;
    const filePath = req.file.path;

    const result = await ocrService.extractTables(filePath, { format });

    // Limpar arquivo após processamento
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Erro na extração de tabelas:', error);
    
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
});

module.exports = router;
