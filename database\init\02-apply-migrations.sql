-- Script para aplicar migrações automaticamente durante a inicialização
-- Data: 2025-07-19
-- Descrição: Aplica todas as migrações pendentes na ordem correta

-- Criar tabela de controle de migrações se não existir
CREATE TABLE IF NOT EXISTS schema_migrations (
    id SERIAL PRIMARY KEY,
    version VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    applied_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Função para aplicar migração se ainda não foi aplicada
CREATE OR REPLACE FUNCTION apply_migration(migration_version VARCHAR(255), migration_description TEXT, migration_sql TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    migration_exists BOOLEAN;
BEGIN
    -- Verificar se a migração já foi aplicada
    SELECT EXISTS(SELECT 1 FROM schema_migrations WHERE version = migration_version) INTO migration_exists;
    
    IF NOT migration_exists THEN
        -- Aplicar a migração
        EXECUTE migration_sql;
        
        -- Registrar a migração como aplicada
        INSERT INTO schema_migrations (version, description) VALUES (migration_version, migration_description);
        
        RAISE NOTICE 'Migração % aplicada com sucesso: %', migration_version, migration_description;
        RETURN TRUE;
    ELSE
        RAISE NOTICE 'Migração % já foi aplicada anteriormente', migration_version;
        RETURN FALSE;
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Erro ao aplicar migração %: %', migration_version, SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- Aplicar migrações na ordem correta
-- Nota: As colunas de integridade já foram adicionadas diretamente na tabela recordings no 01-init.sql
-- Este script serve para futuras migrações e para garantir consistência

-- Migração 001: Estrutura básica (já aplicada no 01-init.sql)
SELECT apply_migration(
    '001_initial_schema',
    'Estrutura inicial do banco de dados com suporte a IA e integridade',
    'SELECT 1' -- Não faz nada, pois já foi aplicada
);

-- Migração 002: Campos de auditoria avançada
SELECT apply_migration(
    '002_advanced_audit',
    'Campos adicionais de auditoria e rastreabilidade',
    '
    ALTER TABLE recordings ADD COLUMN IF NOT EXISTS audit_trail JSONB;
    ALTER TABLE recordings ADD COLUMN IF NOT EXISTS compliance_flags JSONB;
    ALTER TABLE recordings ADD COLUMN IF NOT EXISTS retention_policy VARCHAR(50);
    ALTER TABLE recordings ADD COLUMN IF NOT EXISTS retention_expires_at TIMESTAMP WITH TIME ZONE;
    
    CREATE INDEX IF NOT EXISTS idx_recordings_retention_expires ON recordings(retention_expires_at) WHERE retention_expires_at IS NOT NULL;
    CREATE INDEX IF NOT EXISTS idx_recordings_audit_trail ON recordings USING gin(audit_trail) WHERE audit_trail IS NOT NULL;
    
    COMMENT ON COLUMN recordings.audit_trail IS ''Trilha de auditoria completa em formato JSON'';
    COMMENT ON COLUMN recordings.compliance_flags IS ''Flags de compliance específicas por regulamentação'';
    COMMENT ON COLUMN recordings.retention_policy IS ''Política de retenção aplicada ao arquivo'';
    '
);

-- Migração 003: Melhorias de performance
SELECT apply_migration(
    '003_performance_improvements',
    'Índices adicionais e otimizações de performance',
    '
    -- Índices compostos para consultas frequentes
    CREATE INDEX IF NOT EXISTS idx_recordings_user_created ON recordings(user_id, created_at);
    CREATE INDEX IF NOT EXISTS idx_recordings_status_verification ON recordings(verification_status, last_verification_at);
    CREATE INDEX IF NOT EXISTS idx_recordings_encrypted_status ON recordings(encrypted, verification_status);
    
    -- Índices parciais para otimizar consultas específicas
    CREATE INDEX IF NOT EXISTS idx_recordings_pending_verification ON recordings(id) WHERE verification_status = ''PENDING'';
    CREATE INDEX IF NOT EXISTS idx_recordings_failed_verification ON recordings(id) WHERE verification_status = ''FAILED'';
    CREATE INDEX IF NOT EXISTS idx_recordings_active_files ON recordings(id) WHERE deleted_at IS NULL;
    
    -- Estatísticas automáticas para o otimizador
    ALTER TABLE recordings ALTER COLUMN verification_status SET STATISTICS 1000;
    ALTER TABLE recordings ALTER COLUMN encrypted SET STATISTICS 1000;
    '
);

-- Migração 004: Campos de backup e recuperação
SELECT apply_migration(
    '004_backup_recovery',
    'Campos para controle de backup e recuperação de arquivos',
    '
    ALTER TABLE recordings ADD COLUMN IF NOT EXISTS backup_status VARCHAR(20) DEFAULT ''PENDING'';
    ALTER TABLE recordings ADD COLUMN IF NOT EXISTS backup_path VARCHAR(500);
    ALTER TABLE recordings ADD COLUMN IF NOT EXISTS backup_hash VARCHAR(64);
    ALTER TABLE recordings ADD COLUMN IF NOT EXISTS backup_at TIMESTAMP WITH TIME ZONE;
    ALTER TABLE recordings ADD COLUMN IF NOT EXISTS last_backup_verification TIMESTAMP WITH TIME ZONE;
    ALTER TABLE recordings ADD COLUMN IF NOT EXISTS backup_provider VARCHAR(50);
    ALTER TABLE recordings ADD COLUMN IF NOT EXISTS backup_metadata JSONB;
    
    CREATE INDEX IF NOT EXISTS idx_recordings_backup_status ON recordings(backup_status);
    CREATE INDEX IF NOT EXISTS idx_recordings_backup_at ON recordings(backup_at) WHERE backup_at IS NOT NULL;
    CREATE INDEX IF NOT EXISTS idx_recordings_backup_verification ON recordings(last_backup_verification) WHERE last_backup_verification IS NOT NULL;
    
    COMMENT ON COLUMN recordings.backup_status IS ''Status do backup: PENDING, IN_PROGRESS, COMPLETED, FAILED'';
    COMMENT ON COLUMN recordings.backup_path IS ''Caminho do arquivo de backup'';
    COMMENT ON COLUMN recordings.backup_hash IS ''Hash SHA-256 do arquivo de backup para verificação'';
    COMMENT ON COLUMN recordings.backup_provider IS ''Provedor de backup utilizado (AWS S3, Google Cloud, etc.)'';
    '
);

-- Migração 005: Suporte a múltiplas assinaturas
SELECT apply_migration(
    '005_multiple_signatures',
    'Suporte a múltiplas assinaturas digitais e cadeia de custódia',
    '
    -- Tabela para múltiplas assinaturas
    CREATE TABLE IF NOT EXISTS recording_signatures (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        recording_id UUID NOT NULL REFERENCES recordings(id) ON DELETE CASCADE,
        signature_order INTEGER NOT NULL,
        signer_name VARCHAR(255) NOT NULL,
        signer_email VARCHAR(255),
        signer_document VARCHAR(50),
        digital_signature TEXT NOT NULL,
        certificate_data TEXT,
        signature_algorithm VARCHAR(50),
        signed_at TIMESTAMP WITH TIME ZONE NOT NULL,
        signature_hash VARCHAR(64) NOT NULL,
        is_valid BOOLEAN DEFAULT TRUE,
        validation_details JSONB,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        
        UNIQUE(recording_id, signature_order)
    );
    
    CREATE INDEX IF NOT EXISTS idx_recording_signatures_recording_id ON recording_signatures(recording_id);
    CREATE INDEX IF NOT EXISTS idx_recording_signatures_signed_at ON recording_signatures(signed_at);
    CREATE INDEX IF NOT EXISTS idx_recording_signatures_signer ON recording_signatures(signer_email);
    
    COMMENT ON TABLE recording_signatures IS ''Múltiplas assinaturas digitais por gravação para cadeia de custódia'';
    '
);

-- Migração 006: Correção do sistema de armazenamento
SELECT apply_migration(
    '006_fix_recordings_storage',
    'Correção do sistema de armazenamento - adiciona original_filename e migra caminhos',
    '
    -- Adicionar coluna original_filename se não existir
    ALTER TABLE recordings ADD COLUMN IF NOT EXISTS original_filename VARCHAR(255);
    COMMENT ON COLUMN recordings.original_filename IS ''Nome original do arquivo enviado pelo usuário'';

    -- Atualizar original_filename com base no filename existente
    UPDATE recordings SET original_filename = filename WHERE original_filename IS NULL;

    -- Criar índice para original_filename
    CREATE INDEX IF NOT EXISTS idx_recordings_original_filename ON recordings(original_filename);

    -- Migrar caminhos de temp para recordings
    UPDATE recordings
    SET file_path = ''/app/storage/recordings/'' || filename,
        updated_at = CURRENT_TIMESTAMP
    WHERE file_path LIKE ''%/temp/%'' AND deleted_at IS NULL;
    '
);

-- Remover a função temporária
DROP FUNCTION IF EXISTS apply_migration(VARCHAR(255), TEXT, TEXT);

-- Log final
INSERT INTO schema_migrations (version, description) VALUES 
('999_initialization_complete', 'Inicialização completa do banco de dados com todas as migrações aplicadas')
ON CONFLICT (version) DO NOTHING;

RAISE NOTICE 'Todas as migrações foram aplicadas com sucesso!';
