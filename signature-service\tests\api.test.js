/**
 * Testes de API para o Signature Service
 */
const request = require('supertest');

// URL base do serviço
const BASE_URL = 'http://localhost:3004';

describe('Signature Service API Tests', () => {
  
  describe('Health Check', () => {
    test('GET /health should return 200', async () => {
      const response = await request(BASE_URL)
        .get('/health')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'healthy');
      expect(response.body).toHaveProperty('service', 'signature-service');
      expect(response.body).toHaveProperty('timestamp');
    });
  });

  describe('API Info', () => {
    test('GET /api should return service information', async () => {
      const response = await request(BASE_URL)
        .get('/api')
        .expect(200);
      
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('service', 'CartorioTech Signature Service');
      expect(response.body).toHaveProperty('version', '1.0.0');
      expect(response.body).toHaveProperty('endpoints');
      expect(response.body.endpoints).toHaveProperty('signature');
      expect(response.body.endpoints).toHaveProperty('timestamp');
      expect(response.body.endpoints).toHaveProperty('certificate');
    });
  });

  describe('Signature Endpoints', () => {
    test('GET /api/signature should return signature info', async () => {
      const response = await request(BASE_URL)
        .get('/api/signature')
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('service', 'Serviço de Assinatura Digital');
      expect(response.body).toHaveProperty('availableEndpoints');
      expect(response.body).toHaveProperty('features');
    });

    test('GET /api/timestamp should return timestamp info', async () => {
      const response = await request(BASE_URL)
        .get('/api/timestamp')
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('service', 'Serviço de Carimbo de Tempo');
      expect(response.body).toHaveProperty('availableEndpoints');
      expect(response.body).toHaveProperty('features');
    });

    test('GET /api/certificate should return certificate info', async () => {
      const response = await request(BASE_URL)
        .get('/api/certificate')
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('service', 'Serviço de Certificados Digitais');
      expect(response.body).toHaveProperty('availableEndpoints');
      expect(response.body).toHaveProperty('features');
    });
  });

  describe('Error Handling', () => {
    test('GET /nonexistent should return 404', async () => {
      const response = await request(BASE_URL)
        .get('/nonexistent')
        .expect(404);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('requestedPath', '/nonexistent');
    });
  });

});
