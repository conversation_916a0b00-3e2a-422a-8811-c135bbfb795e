-- Migração 004: Adicionar suporte ao pgvector para buscas semânticas
-- Data: 2025-01-18
-- Descrição: <PERSON><PERSON>r extensão pgvector e tabelas para embeddings de certificados e gravações

-- Criar extensão pgvector se não existir
CREATE EXTENSION IF NOT EXISTS vector;

-- Tabela para armazenar embeddings de certificados de residência
CREATE TABLE IF NOT EXISTS residence_certificate_embeddings (
    id SERIAL PRIMARY KEY,
    certificate_id VARCHAR(255) NOT NULL,
    user_id VARCHAR(255),
    address TEXT NOT NULL,
    description TEXT,
    metadata JSONB,
    embedding vector(1536), -- OpenAI ada-002 embeddings (1536 dimensões)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Índices para performance
    CONSTRAINT unique_certificate_embedding UNIQUE (certificate_id)
);

-- Tabela para armazenar embeddings de gravações
CREATE TABLE IF NOT EXISTS recording_embeddings (
    id SERIAL PRIMARY KEY,
    recording_id VARCHAR(255) NOT NULL,
    user_id VARCHAR(255),
    title TEXT,
    description TEXT,
    transcript TEXT,
    metadata JSONB,
    embedding vector(1536), -- OpenAI ada-002 embeddings (1536 dimensões)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Índices para performance
    CONSTRAINT unique_recording_embedding UNIQUE (recording_id)
);

-- Tabela para armazenar embeddings de documentos OCR
CREATE TABLE IF NOT EXISTS ocr_document_embeddings (
    id SERIAL PRIMARY KEY,
    document_id VARCHAR(255) NOT NULL,
    user_id VARCHAR(255),
    filename TEXT,
    extracted_text TEXT,
    document_type VARCHAR(100),
    metadata JSONB,
    embedding vector(1536), -- OpenAI ada-002 embeddings (1536 dimensões)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Índices para performance
    CONSTRAINT unique_document_embedding UNIQUE (document_id)
);

-- Tabela para armazenar embeddings de dados de Timeline
CREATE TABLE IF NOT EXISTS timeline_location_embeddings (
    id SERIAL PRIMARY KEY,
    validation_id VARCHAR(255) NOT NULL,
    user_id VARCHAR(255),
    address TEXT,
    location_summary TEXT,
    time_period_description TEXT,
    validation_result JSONB,
    metadata JSONB,
    embedding vector(1536), -- OpenAI ada-002 embeddings (1536 dimensões)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Índices para performance
    CONSTRAINT unique_timeline_embedding UNIQUE (validation_id)
);

-- Criar índices HNSW para busca vetorial eficiente
-- HNSW (Hierarchical Navigable Small World) é otimizado para busca de similaridade

-- Índice para certificados de residência
CREATE INDEX IF NOT EXISTS idx_residence_certificate_embeddings_vector 
ON residence_certificate_embeddings 
USING hnsw (embedding vector_cosine_ops)
WITH (m = 16, ef_construction = 64);

-- Índice para gravações
CREATE INDEX IF NOT EXISTS idx_recording_embeddings_vector 
ON recording_embeddings 
USING hnsw (embedding vector_cosine_ops)
WITH (m = 16, ef_construction = 64);

-- Índice para documentos OCR
CREATE INDEX IF NOT EXISTS idx_ocr_document_embeddings_vector 
ON ocr_document_embeddings 
USING hnsw (embedding vector_cosine_ops)
WITH (m = 16, ef_construction = 64);

-- Índice para dados de Timeline
CREATE INDEX IF NOT EXISTS idx_timeline_location_embeddings_vector 
ON timeline_location_embeddings 
USING hnsw (embedding vector_cosine_ops)
WITH (m = 16, ef_construction = 64);

-- Índices adicionais para consultas por usuário e data
CREATE INDEX IF NOT EXISTS idx_residence_certificate_embeddings_user_id 
ON residence_certificate_embeddings (user_id);

CREATE INDEX IF NOT EXISTS idx_recording_embeddings_user_id 
ON recording_embeddings (user_id);

CREATE INDEX IF NOT EXISTS idx_ocr_document_embeddings_user_id 
ON ocr_document_embeddings (user_id);

CREATE INDEX IF NOT EXISTS idx_timeline_location_embeddings_user_id 
ON timeline_location_embeddings (user_id);

-- Índices para busca por data
CREATE INDEX IF NOT EXISTS idx_residence_certificate_embeddings_created_at 
ON residence_certificate_embeddings (created_at DESC);

CREATE INDEX IF NOT EXISTS idx_recording_embeddings_created_at 
ON recording_embeddings (created_at DESC);

CREATE INDEX IF NOT EXISTS idx_ocr_document_embeddings_created_at 
ON ocr_document_embeddings (created_at DESC);

CREATE INDEX IF NOT EXISTS idx_timeline_location_embeddings_created_at 
ON timeline_location_embeddings (created_at DESC);

-- Função para busca semântica unificada
CREATE OR REPLACE FUNCTION search_semantic_content(
    query_embedding vector(1536),
    similarity_threshold float DEFAULT 0.7,
    max_results integer DEFAULT 10,
    user_filter varchar DEFAULT NULL
)
RETURNS TABLE (
    content_type varchar,
    content_id varchar,
    title text,
    description text,
    similarity_score float,
    metadata jsonb,
    created_at timestamp
) AS $$
BEGIN
    RETURN QUERY
    (
        -- Buscar em certificados de residência
        SELECT 
            'residence_certificate'::varchar as content_type,
            rce.certificate_id as content_id,
            ('Certificado: ' || rce.address)::text as title,
            rce.description,
            (1 - (rce.embedding <=> query_embedding))::float as similarity_score,
            rce.metadata,
            rce.created_at
        FROM residence_certificate_embeddings rce
        WHERE (user_filter IS NULL OR rce.user_id = user_filter)
        AND (1 - (rce.embedding <=> query_embedding)) >= similarity_threshold
        
        UNION ALL
        
        -- Buscar em gravações
        SELECT 
            'recording'::varchar as content_type,
            re.recording_id as content_id,
            re.title,
            re.description,
            (1 - (re.embedding <=> query_embedding))::float as similarity_score,
            re.metadata,
            re.created_at
        FROM recording_embeddings re
        WHERE (user_filter IS NULL OR re.user_id = user_filter)
        AND (1 - (re.embedding <=> query_embedding)) >= similarity_threshold
        
        UNION ALL
        
        -- Buscar em documentos OCR
        SELECT 
            'ocr_document'::varchar as content_type,
            ode.document_id as content_id,
            ode.filename as title,
            LEFT(ode.extracted_text, 200) as description,
            (1 - (ode.embedding <=> query_embedding))::float as similarity_score,
            ode.metadata,
            ode.created_at
        FROM ocr_document_embeddings ode
        WHERE (user_filter IS NULL OR ode.user_id = user_filter)
        AND (1 - (ode.embedding <=> query_embedding)) >= similarity_threshold
        
        UNION ALL
        
        -- Buscar em dados de Timeline
        SELECT 
            'timeline_location'::varchar as content_type,
            tle.validation_id as content_id,
            ('Timeline: ' || tle.address)::text as title,
            tle.location_summary as description,
            (1 - (tle.embedding <=> query_embedding))::float as similarity_score,
            tle.metadata,
            tle.created_at
        FROM timeline_location_embeddings tle
        WHERE (user_filter IS NULL OR tle.user_id = user_filter)
        AND (1 - (tle.embedding <=> query_embedding)) >= similarity_threshold
    )
    ORDER BY similarity_score DESC
    LIMIT max_results;
END;
$$ LANGUAGE plpgsql;

-- Função para atualizar timestamp de updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers para atualizar updated_at automaticamente
CREATE TRIGGER update_residence_certificate_embeddings_updated_at
    BEFORE UPDATE ON residence_certificate_embeddings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_recording_embeddings_updated_at
    BEFORE UPDATE ON recording_embeddings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ocr_document_embeddings_updated_at
    BEFORE UPDATE ON ocr_document_embeddings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_timeline_location_embeddings_updated_at
    BEFORE UPDATE ON timeline_location_embeddings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Comentários para documentação
COMMENT ON TABLE residence_certificate_embeddings IS 'Armazena embeddings de certificados de residência para busca semântica';
COMMENT ON TABLE recording_embeddings IS 'Armazena embeddings de gravações para busca semântica';
COMMENT ON TABLE ocr_document_embeddings IS 'Armazena embeddings de documentos OCR para busca semântica';
COMMENT ON TABLE timeline_location_embeddings IS 'Armazena embeddings de dados de Timeline para busca semântica';

COMMENT ON FUNCTION search_semantic_content IS 'Função para busca semântica unificada em todos os tipos de conteúdo';

-- Inserir dados de exemplo para teste (opcional)
-- INSERT INTO residence_certificate_embeddings (certificate_id, user_id, address, description, metadata, embedding)
-- VALUES ('cert_demo_001', 'user_demo', 'Rua das Flores, 123, São Paulo - SP', 'Certificado de residência validado por geolocalização', '{"score": 0.95, "validation_days": 30}', '[0.1, 0.2, 0.3, ...]'::vector);

COMMIT;
