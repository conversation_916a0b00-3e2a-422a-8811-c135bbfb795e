const express = require('express');
const AnalyticsService = require('../services/analyticsService');

const router = express.Router();
const analyticsService = new AnalyticsService();

/**
 * GET /api/ai/analytics
 * Informações sobre o serviço de analytics de IA
 */
router.get('/', (req, res) => {
  res.json({
    success: true,
    service: 'Serviço de Analytics de IA',
    version: '1.0.0',
    availableEndpoints: [
      'GET /api/ai/analytics/summary - Resumo geral de analytics',
      'GET /api/ai/analytics/transcription - Analytics de transcrição',
      'GET /api/ai/analytics/quality - Analytics de qualidade',
      'GET /api/ai/analytics/compliance - Analytics de compliance',
      'GET /api/ai/analytics/usage - Estatísticas de uso',
      'GET /api/ai/analytics/performance - Métricas de performance'
    ],
    metrics: [
      'Total de transcrições processadas',
      'Tempo médio de processamento',
      'Taxa de sucesso',
      'Qualidade média das gravações',
      'Compliance score',
      'Uso de recursos'
    ],
    features: [
      'Dashboards em tempo real',
      'Relatórios personalizados',
      'Alertas automáticos',
      'Tendências históricas',
      'Comparativos de performance',
      'Exportação de dados'
    ],
    timestamp: new Date().toISOString()
  });
});

/**
 * GET /api/ai/analytics/summary
 * Resumo geral de analytics
 */
router.get('/summary', async (req, res) => {
  try {
    const { period = '30d' } = req.query;

    const summary = await analyticsService.getSummary(period);

    res.json({
      success: true,
      data: summary,
      period,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Erro ao obter resumo de analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
});

/**
 * GET /api/ai/analytics/transcription
 * Analytics específicos de transcrição
 */
router.get('/transcription', async (req, res) => {
  try {
    const { period = '30d', groupBy = 'day' } = req.query;

    const analytics = await analyticsService.getTranscriptionAnalytics(period, groupBy);

    res.json({
      success: true,
      data: analytics,
      period,
      groupBy,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Erro ao obter analytics de transcrição:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
});

/**
 * GET /api/ai/analytics/quality
 * Analytics de qualidade
 */
router.get('/quality', async (req, res) => {
  try {
    const { period = '30d' } = req.query;

    const analytics = await analyticsService.getQualityAnalytics(period);

    res.json({
      success: true,
      data: analytics,
      period,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Erro ao obter analytics de qualidade:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
});

/**
 * GET /api/ai/analytics/compliance
 * Analytics de compliance
 */
router.get('/compliance', async (req, res) => {
  try {
    const { period = '30d' } = req.query;

    const analytics = await analyticsService.getComplianceAnalytics(period);

    res.json({
      success: true,
      data: analytics,
      period,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Erro ao obter analytics de compliance:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
});

/**
 * GET /api/ai/analytics/usage
 * Estatísticas de uso do serviço
 */
router.get('/usage', async (req, res) => {
  try {
    const { period = '30d' } = req.query;

    const usage = await analyticsService.getUsageStats(period);

    res.json({
      success: true,
      data: usage,
      period,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Erro ao obter estatísticas de uso:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
});

/**
 * GET /api/ai/analytics/performance
 * Métricas de performance do sistema
 */
router.get('/performance', async (req, res) => {
  try {
    const { period = '24h' } = req.query;

    const performance = await analyticsService.getPerformanceMetrics(period);

    res.json({
      success: true,
      data: performance,
      period,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Erro ao obter métricas de performance:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
});

/**
 * GET /api/ai/analytics/trends
 * Análise de tendências
 */
router.get('/trends', async (req, res) => {
  try {
    const { metric = 'transcriptions', period = '30d' } = req.query;

    const trends = await analyticsService.getTrends(metric, period);

    res.json({
      success: true,
      data: trends,
      metric,
      period,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Erro ao obter tendências:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
});

module.exports = router;
