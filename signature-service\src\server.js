/**
 * Servidor do Serviço de Assinatura Digital e Carimbo de Tempo
 * CartórioTech - Sistema de documentos notariais
 */
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const dotenv = require('dotenv');
const fs = require('fs');
const path = require('path');
const winston = require('winston');
const swaggerJsDoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');

// Carregar variáveis de ambiente
dotenv.config();

// Configurar logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'signature-service' },
  transports: [
    new winston.transports.File({ filename: '/app/logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: '/app/logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
});

// Inicializar app Express
const app = express();
const PORT = process.env.PORT || 3004;

// Criar diretórios necessários
const directories = [
  path.join(__dirname, '../storage/certificates'),
  path.join(__dirname, '../storage/signatures'),
  path.join(__dirname, '../keys'),
  path.join(__dirname, '../logs'),
];

directories.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    logger.info(`Diretório criado: ${dir}`);
  }
});

// Configuração do Swagger
const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'API de Assinatura Digital e Carimbo de Tempo',
      version: '1.0.0',
      description: 'API para assinatura digital de documentos e aplicação de carimbos de tempo',
      contact: {
        name: 'CartórioTech'
      },
      servers: [
        {
          url: `http://localhost:${PORT}`
        }
      ]
    },
    components: {
      securitySchemes: {
        apiKey: {
          type: 'apiKey',
          in: 'header',
          name: 'X-API-KEY'
        },
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT'
        }
      }
    }
  },
  apis: ['./src/routes/*.js']
};

const swaggerDocs = swaggerJsDoc(swaggerOptions);

// Middleware para segurança e parsing
app.use(helmet());
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',') : '*'
}));
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Middleware de logging
app.use((req, res, next) => {
  logger.info(`${req.method} ${req.path}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });
  next();
});

// Rota de documentação Swagger
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocs));

// Rota de saúde para monitoramento
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    service: 'signature-service',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    uptime: process.uptime(),
    memory: {
      used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
      total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024)
    }
  });
});

// Rota de informações da API
app.get('/api', (req, res) => {
  res.json({
    success: true,
    service: 'CartorioTech Signature Service',
    version: '1.0.0',
    description: 'Serviço de Assinatura Digital e Carimbo de Tempo',
    endpoints: {
      signature: '/api/signature',
      timestamp: '/api/timestamp',
      certificate: '/api/certificate'
    },
    features: [
      'Assinatura digital ICP-Brasil',
      'Carimbo de tempo confiável',
      'Certificados digitais A1/A3',
      'Validação de assinaturas',
      'Cadeia de certificação',
      'Auditoria completa'
    ],
    documentation: '/api-docs',
    health: '/health',
    timestamp: new Date().toISOString()
  });
});

// Importar e configurar rotas
const digitalSignatureRoutes = require('./routes/digitalSignature');
const timestampRoutes = require('./routes/timestamp');
const certificateRoutes = require('./routes/certificate');

// Configurar rotas
app.use('/api/signature', digitalSignatureRoutes);
app.use('/api/timestamp', timestampRoutes);
app.use('/api/certificate', certificateRoutes);

// Middleware de tratamento de erros
app.use((error, req, res, next) => {
  logger.error('Erro não tratado:', error);
  res.status(error.status || 500).json({
    success: false,
    message: error.message || 'Erro interno do servidor',
    error: process.env.NODE_ENV === 'development' ? error.stack : undefined
  });
});

// Middleware para rotas não encontradas
app.use('*', (req, res) => {
  logger.warn(`Rota não encontrada: ${req.method} ${req.originalUrl}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  res.status(404).json({
    success: false,
    message: 'Endpoint não encontrado no serviço de assinaturas',
    requestedPath: req.originalUrl,
    method: req.method,
    availableEndpoints: {
      info: 'GET /api',
      health: 'GET /health',
      documentation: 'GET /api-docs',
      signature: '/api/signature/*',
      timestamp: '/api/timestamp/*',
      certificate: '/api/certificate/*'
    },
    examples: [
      'GET /api - Informações da API',
      'GET /health - Status do serviço',
      'POST /api/signature/sign - Assinar documento',
      'POST /api/timestamp/apply - Aplicar carimbo de tempo',
      'GET /api/certificate/list - Listar certificados'
    ],
    timestamp: new Date().toISOString()
  });
});

// Inicializar serviços
const initializeServices = async () => {
  try {
    // Verificar e inicializar chaves criptográficas e certificados
    const certService = require('./services/certificateService');
    await certService.initializeKeyStore();
    
    logger.info('Serviço de assinaturas inicializado com sucesso');
  } catch (error) {
    logger.error('Erro ao inicializar serviços:', error);
    throw error;
  }
};

// Iniciar servidor
const startServer = async () => {
  try {
    await initializeServices();
    
    app.listen(PORT, () => {
      logger.info(`🔏 Serviço de Assinatura Digital e Carimbo de Tempo rodando na porta ${PORT}`);
      logger.info(`📄 Documentação API: http://localhost:${PORT}/api-docs`);
      logger.info(`🩺 Health check: http://localhost:${PORT}/health`);
    });
  } catch (error) {
    logger.error('Erro fatal ao iniciar o serviço:', error);
    process.exit(1);
  }
};

// Tratamento de sinais de sistema
process.on('SIGTERM', () => {
  logger.info('SIGTERM recebido, encerrando servidor...');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT recebido, encerrando servidor...');
  process.exit(0);
});

startServer().catch(error => {
  logger.error('Erro ao iniciar servidor:', error);
  process.exit(1);
});

module.exports = app;
