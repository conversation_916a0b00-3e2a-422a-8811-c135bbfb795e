const fs = require('fs');
const path = require('path');
const ffmpeg = require('fluent-ffmpeg');

class QualityService {
  constructor() {
    this.qualityThresholds = {
      audio: {
        snr: 20, // Signal-to-noise ratio mínimo
        volume: -30, // Volume mínimo em dB
        bitrate: 64000 // Bitrate mínimo
      },
      video: {
        resolution: 480, // Resolução mínima (altura)
        fps: 15, // FPS mínimo
        bitrate: 500000 // Bitrate mínimo
      }
    };
  }

  /**
   * Analisar qualidade geral de um arquivo
   */
  async analyzeQuality(filePath, options = {}) {
    try {
      if (!fs.existsSync(filePath)) {
        throw new Error('Arquivo não encontrado');
      }

      const fileStats = fs.statSync(filePath);
      const fileInfo = await this.getFileInfo(filePath);
      
      const analysis = {
        file: {
          path: filePath,
          size: fileStats.size,
          sizeFormatted: this.formatFileSize(fileStats.size),
          created: fileStats.birthtime,
          modified: fileStats.mtime
        },
        metadata: fileInfo,
        quality: {
          overall: 'unknown',
          audio: null,
          video: null
        },
        issues: [],
        recommendations: [],
        score: 0
      };

      // Analisar áudio se presente
      if (fileInfo.streams.find(s => s.codec_type === 'audio')) {
        analysis.quality.audio = await this.analyzeAudioQuality(filePath, options);
      }

      // Analisar vídeo se presente
      if (fileInfo.streams.find(s => s.codec_type === 'video')) {
        analysis.quality.video = await this.analyzeVideoQuality(filePath, options);
      }

      // Calcular score geral
      analysis.score = this.calculateOverallScore(analysis.quality);
      analysis.quality.overall = this.getQualityLevel(analysis.score);

      // Gerar recomendações
      analysis.recommendations = this.generateRecommendations(analysis);

      return analysis;

    } catch (error) {
      console.error('Erro na análise de qualidade:', error);
      throw error;
    }
  }

  /**
   * Analisar qualidade específica do áudio
   */
  async analyzeAudioQuality(filePath, options = {}) {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(filePath, (err, metadata) => {
        if (err) {
          reject(err);
          return;
        }

        const audioStream = metadata.streams.find(s => s.codec_type === 'audio');
        if (!audioStream) {
          resolve(null);
          return;
        }

        const analysis = {
          codec: audioStream.codec_name,
          bitrate: parseInt(audioStream.bit_rate) || 0,
          sampleRate: parseInt(audioStream.sample_rate) || 0,
          channels: audioStream.channels || 0,
          duration: parseFloat(audioStream.duration) || 0,
          quality: 'unknown',
          score: 0,
          issues: []
        };

        // Avaliar qualidade baseada em métricas
        let score = 100;

        // Bitrate
        if (analysis.bitrate < this.qualityThresholds.audio.bitrate) {
          score -= 20;
          analysis.issues.push('Bitrate de áudio baixo');
        }

        // Sample rate
        if (analysis.sampleRate < 22050) {
          score -= 15;
          analysis.issues.push('Taxa de amostragem baixa');
        }

        // Canais
        if (analysis.channels < 1) {
          score -= 30;
          analysis.issues.push('Problema nos canais de áudio');
        }

        analysis.score = Math.max(0, score);
        analysis.quality = this.getQualityLevel(analysis.score);

        resolve(analysis);
      });
    });
  }

  /**
   * Analisar qualidade específica do vídeo
   */
  async analyzeVideoQuality(filePath, options = {}) {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(filePath, (err, metadata) => {
        if (err) {
          reject(err);
          return;
        }

        const videoStream = metadata.streams.find(s => s.codec_type === 'video');
        if (!videoStream) {
          resolve(null);
          return;
        }

        const analysis = {
          codec: videoStream.codec_name,
          width: videoStream.width || 0,
          height: videoStream.height || 0,
          fps: this.parseFPS(videoStream.r_frame_rate),
          bitrate: parseInt(videoStream.bit_rate) || 0,
          duration: parseFloat(videoStream.duration) || 0,
          quality: 'unknown',
          score: 0,
          issues: []
        };

        // Avaliar qualidade baseada em métricas
        let score = 100;

        // Resolução
        if (analysis.height < this.qualityThresholds.video.resolution) {
          score -= 25;
          analysis.issues.push('Resolução baixa');
        }

        // FPS
        if (analysis.fps < this.qualityThresholds.video.fps) {
          score -= 15;
          analysis.issues.push('Taxa de quadros baixa');
        }

        // Bitrate
        if (analysis.bitrate < this.qualityThresholds.video.bitrate) {
          score -= 20;
          analysis.issues.push('Bitrate de vídeo baixo');
        }

        analysis.score = Math.max(0, score);
        analysis.quality = this.getQualityLevel(analysis.score);

        resolve(analysis);
      });
    });
  }

  /**
   * Obter informações do arquivo
   */
  async getFileInfo(filePath) {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(filePath, (err, metadata) => {
        if (err) {
          reject(err);
          return;
        }
        resolve(metadata);
      });
    });
  }

  /**
   * Calcular score geral
   */
  calculateOverallScore(quality) {
    let totalScore = 0;
    let count = 0;

    if (quality.audio) {
      totalScore += quality.audio.score;
      count++;
    }

    if (quality.video) {
      totalScore += quality.video.score;
      count++;
    }

    return count > 0 ? Math.round(totalScore / count) : 0;
  }

  /**
   * Obter nível de qualidade baseado no score
   */
  getQualityLevel(score) {
    if (score >= 80) return 'excellent';
    if (score >= 60) return 'good';
    if (score >= 40) return 'fair';
    if (score >= 20) return 'poor';
    return 'very_poor';
  }

  /**
   * Gerar recomendações
   */
  generateRecommendations(analysis) {
    const recommendations = [];

    if (analysis.quality.audio && analysis.quality.audio.score < 60) {
      recommendations.push('Considere melhorar a qualidade do áudio');
      if (analysis.quality.audio.bitrate < 128000) {
        recommendations.push('Aumente o bitrate do áudio para pelo menos 128kbps');
      }
    }

    if (analysis.quality.video && analysis.quality.video.score < 60) {
      recommendations.push('Considere melhorar a qualidade do vídeo');
      if (analysis.quality.video.height < 720) {
        recommendations.push('Use resolução HD (720p) ou superior');
      }
    }

    if (analysis.file.size > 500 * 1024 * 1024) { // 500MB
      recommendations.push('Arquivo muito grande, considere compressão');
    }

    return recommendations;
  }

  /**
   * Parsear FPS do formato de fração
   */
  parseFPS(fpsString) {
    if (!fpsString) return 0;
    
    const parts = fpsString.split('/');
    if (parts.length === 2) {
      return parseFloat(parts[0]) / parseFloat(parts[1]);
    }
    
    return parseFloat(fpsString) || 0;
  }

  /**
   * Formatar tamanho do arquivo
   */
  formatFileSize(bytes) {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }

  /**
   * Obter relatório de qualidade
   */
  async getQualityReport(id) {
    // Implementar busca no banco de dados ou cache
    // Por enquanto, retorna null
    return null;
  }

  /**
   * Obter estatísticas de qualidade
   */
  async getQualityStats() {
    // Implementar estatísticas agregadas
    return {
      totalAnalyses: 0,
      averageScore: 0,
      qualityDistribution: {
        excellent: 0,
        good: 0,
        fair: 0,
        poor: 0,
        very_poor: 0
      }
    };
  }
}

module.exports = QualityService;
