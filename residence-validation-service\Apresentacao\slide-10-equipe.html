<!DOCTYPE html>
<html lang="pt-br">
<head>
  <meta charset="UTF-8">
  <title>ProvaSegura - Slide 10: <PERSON><PERSON><PERSON> <PERSON></title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');
    
    body {
      margin: 0;
      padding: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      font-family: 'Inter', 'Segoe UI', Arial, sans-serif;
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }
      .slide {
      width: 90vw;
      max-width: 1280px;
      height: 80vh;
      max-height: 720px;
      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
      box-shadow: 0 20px 60px rgba(0,0,0,0.15), 0 8px 32px rgba(0,0,0,0.08);
      border-radius: 24px;
      display: flex;
      flex-direction: row;
      overflow: hidden;
      position: relative;
      border: 1px solid rgba(255,255,255,0.2);
    }
    
    .slide::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
      border-radius: 24px 24px 0 0;
    }
      .slide-content {
      padding: 40px 60px;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      position: relative;
      z-index: 2;
    }
      .slide-title {
      font-size: 2.5rem;
      font-weight: 800;
      background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-bottom: 30px;
      line-height: 1.1;
      letter-spacing: -0.02em;
    }
    
    .slide-text {
      font-size: 1.2rem;
      color: #334155;
      line-height: 1.7;
      font-weight: 400;
    }
    
    .slide-text b {
      color: #1e293b;
      font-weight: 700;
      font-size: 1.3rem;
    }
      .team-section {
      display: flex;
      gap: 25px;
      margin: 25px 0;
      justify-content: flex-start;
    }
    
    .team-member {
      text-align: center;
    }
      .team-photo {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 2rem;
      color: #fff;
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
      border: 4px solid rgba(255, 255, 255, 0.2);
      margin-bottom: 12px;
      transition: transform 0.3s ease;
    }
    
    .team-photo:hover {
      transform: scale(1.1);
    }
    
    .team-role {
      font-size: 0.9rem;
      font-weight: 600;
      color: #667eea;
    }
      .expansion-highlight {
      background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
      padding: 20px;
      border-radius: 16px;
      margin-top: 20px;
      border: 2px solid rgba(102, 126, 234, 0.2);
      text-align: center;
    }
    
    .expansion-highlight span {
      font-size: 1.3rem;
      font-weight: 700;
      color: #667eea;
    }
    
    .slide-img {
      flex: 0 0 400px;
      background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      overflow: hidden;
      order: -1;
    }
    
    .slide-img::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 70% 30%, rgba(102, 126, 234, 0.1) 0%, transparent 50%);
    }
    
    .slide-img svg {
      filter: drop-shadow(0 15px 35px rgba(102, 126, 234, 0.2));
      z-index: 1;
      position: relative;
    }
    
    .slide-number {
      position: absolute;
      top: 30px;
      right: 40px;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-weight: 700;
      font-size: 1.3rem;
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }    @media print {
      body { background: #fff; }
      .slide { box-shadow: none; border: 1px solid #e2e8f0; }
    }

    @media (max-width: 768px) {
      .slide {
        width: 95vw;
        height: 90vh;
        flex-direction: column;
        padding: 30px 20px;
      }
      
      .slide-content {
        padding: 20px;
        order: 2;
      }
      
      .slide-title {
        font-size: 2rem;
        margin-bottom: 20px;
      }
      
      .slide-text {
        font-size: 1rem;
        margin-bottom: 20px;
      }
      
      .slide-text b {
        font-size: 1.1rem;
      }
      
      .team-section {
        gap: 20px;
        margin: 20px 0;
      }
      
      .team-photo {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
        margin-bottom: 8px;
      }
      
      .team-role {
        font-size: 0.8rem;
      }
      
      .expansion-highlight {
        padding: 16px;
        margin-top: 16px;
      }
      
      .expansion-highlight span {
        font-size: 1.1rem;
      }
      
      .slide-img {
        flex: 0 0 200px;
        order: 1;
      }
      
      .slide-img svg {
        width: 280px;
        height: 300px;
      }
    }
  </style>
</head>
<body>

<div class="slide">
  <div class="slide-number">10</div>
  <div class="slide-content">
    <div class="slide-title">Quem Somos e Para Onde Vamos</div>
    <div class="slide-text">
      <b>Equipe:</b> Fundadores com experiência em tecnologia, direito e finanças, prontos para liderar a inovação.<br><br>
      <b>Visão:</b> Ser a principal plataforma de comprovação de localização na América Latina, transformando processos em RH, finanças e justiça.<br><br>
      <b>Missão:</b> Tornar a validação de residência e localização rápida, confiável e acessível para todos.
    </div>
    
    <div class="team-section">
      <div class="team-member">
        <div class="team-photo">👩‍💻</div>
        <div class="team-role">CTO</div>
      </div>
      <div class="team-member">
        <div class="team-photo">👨‍⚖️</div>
        <div class="team-role">Legal</div>
      </div>
      <div class="team-member">
        <div class="team-photo">👨‍💼</div>
        <div class="team-role">CEO</div>
      </div>
    </div>
    
    <div class="expansion-highlight">
      <span>🌎 Expandindo pela América Latina</span>
    </div>
  </div>
  <div class="slide-img">
    <!-- Mapa América Latina melhorado -->
    <svg width="350" height="400" viewBox="0 0 350 400">
      <!-- Mapa estilizado da América Latina -->
      <path d="M100 80 Q120 70 140 80 L150 100 Q160 120 150 140 L145 160 Q155 180 145 200 L140 240 Q150 280 140 320 L135 360 Q130 380 115 375 L100 370 Q90 360 95 340 L100 320 Q95 300 100 280 L105 240 Q100 200 105 160 L110 140 Q105 120 110 100 Z" 
            fill="url(#mapGradient)" stroke="#667eea" stroke-width="4"/>
      
      <!-- Pontos de expansão com animação -->
      <g>
        <!-- Brasil -->
        <circle cx="125" cy="200" r="8" fill="#10b981">
          <animate attributeName="r" values="8;12;8" dur="2s" repeatCount="indefinite"/>
        </circle>
        <text x="155" y="205" font-size="14" font-weight="bold" fill="#1e293b">Brasil</text>
        <text x="155" y="220" font-size="12" fill="#059669">Base Principal</text>
        
        <!-- México -->
        <circle cx="110" cy="120" r="6" fill="#f59e0b">
          <animate attributeName="r" values="6;10;6" dur="2.5s" repeatCount="indefinite"/>
        </circle>
        <text x="130" y="125" font-size="12" font-weight="bold" fill="#1e293b">México</text>
        <text x="130" y="138" font-size="10" fill="#d97706">2026</text>
        
        <!-- Colômbia -->
        <circle cx="115" cy="160" r="6" fill="#8b5cf6">
          <animate attributeName="r" values="6;10;6" dur="3s" repeatCount="indefinite"/>
        </circle>
        <text x="135" y="165" font-size="12" font-weight="bold" fill="#1e293b">Colômbia</text>
        <text x="135" y="178" font-size="10" fill="#7c3aed">2026</text>
        
        <!-- Argentina -->
        <circle cx="120" cy="280" r="6" fill="#ef4444">
          <animate attributeName="r" values="6;10;6" dur="3.5s" repeatCount="indefinite"/>
        </circle>
        <text x="140" y="285" font-size="12" font-weight="bold" fill="#1e293b">Argentina</text>
        <text x="140" y="298" font-size="10" fill="#dc2626">2027</text>
        
        <!-- Chile -->
        <circle cx="105" cy="320" r="6" fill="#06b6d4">
          <animate attributeName="r" values="6;10;6" dur="4s" repeatCount="indefinite"/>
        </circle>
        <text x="125" y="325" font-size="12" font-weight="bold" fill="#1e293b">Chile</text>
        <text x="125" y="338" font-size="10" fill="#0891b2">2027</text>
      </g>
      
      <!-- Conexões entre países -->
      <g opacity="0.4">
        <line x1="125" y1="200" x2="110" y2="120" stroke="#667eea" stroke-width="2" stroke-dasharray="5,5"/>
        <line x1="125" y1="200" x2="115" y2="160" stroke="#667eea" stroke-width="2" stroke-dasharray="5,5"/>
        <line x1="125" y1="200" x2="120" y2="280" stroke="#667eea" stroke-width="2" stroke-dasharray="5,5"/>
        <line x1="125" y1="200" x2="105" y2="320" stroke="#667eea" stroke-width="2" stroke-dasharray="5,5"/>
      </g>
      
      <!-- Estatísticas de mercado -->
      <g transform="translate(200, 50)">
        <rect x="0" y="0" width="140" height="100" rx="12" fill="#fff" stroke="#e2e8f0" stroke-width="2"/>
        <text x="70" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#1e293b">Mercado LatAm</text>
        <text x="10" y="40" font-size="11" fill="#059669">✓ 200M+ habitantes</text>
        <text x="10" y="55" font-size="11" fill="#059669">✓ 15M+ empresas</text>
        <text x="10" y="70" font-size="11" fill="#059669">✓ R$ 500M potencial</text>
        <text x="10" y="85" font-size="11" fill="#059669">✓ Baixa concorrência</text>
      </g>
      
      <!-- Bandeiras ou indicadores -->
      <g transform="translate(50, 50)">
        <circle r="20" fill="#3b82f6"/>
        <text x="0" y="6" text-anchor="middle" font-size="20" fill="#fff">🚀</text>
        <text x="0" y="40" text-anchor="middle" font-size="10" font-weight="bold" fill="#1e293b">Expansão</text>
      </g>
      
      <g transform="translate(280, 350)">
        <circle r="20" fill="#10b981"/>
        <text x="0" y="6" text-anchor="middle" font-size="20" fill="#fff">🌟</text>
        <text x="0" y="40" text-anchor="middle" font-size="10" font-weight="bold" fill="#1e293b">Liderança</text>
      </g>
      
      <!-- Definições -->
      <defs>
        <linearGradient id="mapGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#e0e7ff;stop-opacity:1" />
          <stop offset="50%" style="stop-color:#c7d2fe;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#a5b4fc;stop-opacity:1" />
        </linearGradient>
      </defs>    </svg>
  </div>
</div>

<div class="navigation">
  <a href="slide-09-investimento.html" class="nav-button">← Anterior</a>
  <a href="index.html" class="nav-button">📋 Índice</a>
  <a href="slide-11-cta.html" class="nav-button">Próximo →</a>
</div>

<style>
.navigation {
  position: fixed;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 15px;
  z-index: 1000;
}

.nav-button {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  color: #1e293b;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  text-decoration: none;
  display: inline-block;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.nav-button:hover {
  background: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}
</style>

<script>
document.addEventListener('keydown', function(e) {
  switch(e.key) {
    case 'ArrowLeft':
      window.location.href = 'slide-09-investimento.html';
      break;
    case 'ArrowRight':
      window.location.href = 'slide-11-cta.html';
      break;
    case 'Home':
      window.location.href = 'index.html';
      break;
    case 'F11':
      e.preventDefault();
      if (document.fullscreenElement) {
        document.exitFullscreen();
      } else {
        document.documentElement.requestFullscreen();
      }
      break;
  }
});
</script>

</body>
</html>
