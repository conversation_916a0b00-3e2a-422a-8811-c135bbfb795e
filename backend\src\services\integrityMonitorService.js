const cron = require('node-cron');
const crypto = require('crypto');
const fs = require('fs');
const { Pool } = require('pg');
const DigitalSignatureService = require('./digitalSignatureService');
const EncryptionService = require('./encryptionService');
const BlockchainService = require('./blockchainService');
const AuditService = require('./auditService');

class IntegrityMonitorService {
  constructor() {
    // Pool de conexões PostgreSQL
    this.pool = new Pool({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 5432,
      database: process.env.DB_NAME || 'cartorio_db',
      user: process.env.DB_USER || 'cartorio_user',
      password: process.env.DB_PASSWORD || 'cartorio_secure_pass',
    });

    // Serviços
    this.digitalSignatureService = new DigitalSignatureService();
    this.encryptionService = new EncryptionService();
    this.blockchainService = new BlockchainService();
    this.auditService = new AuditService();

    // Configurações
    this.monitoringEnabled = process.env.INTEGRITY_MONITORING !== 'false';
    this.checkInterval = process.env.INTEGRITY_CHECK_INTERVAL || '0 2 * * *'; // Diário às 2h
    this.batchSize = parseInt(process.env.INTEGRITY_BATCH_SIZE) || 50;
    this.alertThreshold = parseFloat(process.env.INTEGRITY_ALERT_THRESHOLD) || 0.05; // 5%

    // Estado do monitor
    this.isRunning = false;
    this.lastCheck = null;
    this.statistics = {
      totalChecks: 0,
      violationsFound: 0,
      lastViolation: null
    };

    // Inicializar agendamento
    this.initializeScheduler();
  }

  /**
   * Inicializar agendador de verificações
   */
  initializeScheduler() {
    if (!this.monitoringEnabled) {
      console.log('[IntegrityMonitor] Monitoramento desabilitado');
      return;
    }

    console.log(`[IntegrityMonitor] Agendando verificações: ${this.checkInterval}`);
    
    // Verificação diária completa
    cron.schedule(this.checkInterval, () => {
      this.runFullIntegrityCheck();
    });

    // Verificação rápida a cada hora
    cron.schedule('0 * * * *', () => {
      this.runQuickIntegrityCheck();
    });

    // Verificação de arquivos críticos a cada 15 minutos
    cron.schedule('*/15 * * * *', () => {
      this.runCriticalFilesCheck();
    });

    console.log('[IntegrityMonitor] Agendador inicializado');
  }

  /**
   * Executar verificação completa de integridade
   */
  async runFullIntegrityCheck() {
    if (this.isRunning) {
      console.log('[IntegrityMonitor] Verificação já em andamento, pulando...');
      return;
    }

    this.isRunning = true;
    const startTime = Date.now();

    try {
      console.log('[IntegrityMonitor] Iniciando verificação completa de integridade');

      // Log início da verificação
      await this.auditService.logAction({
        action: 'INTEGRITY_FULL_CHECK_START',
        userId: 'system',
        userName: 'Integrity Monitor',
        userRole: 'system',
        details: {
          checkType: 'FULL',
          scheduledAt: new Date().toISOString()
        },
        severity: 'INFO',
        category: 'INTEGRITY_MONITORING'
      });

      // Obter todas as gravações
      const recordings = await this.pool.query(`
        SELECT id, file_path, encrypted_path, file_hash, encrypted_hash, 
               digital_signature, encrypted, verification_status
        FROM recordings 
        WHERE file_path IS NOT NULL
        ORDER BY created_at DESC
      `);

      const results = {
        total: recordings.rows.length,
        checked: 0,
        violations: [],
        errors: [],
        summary: {
          hashMismatches: 0,
          missingFiles: 0,
          signatureFailures: 0,
          encryptionIssues: 0,
          blockchainMismatches: 0
        }
      };

      // Processar em lotes
      for (let i = 0; i < recordings.rows.length; i += this.batchSize) {
        const batch = recordings.rows.slice(i, i + this.batchSize);
        
        for (const recording of batch) {
          try {
            const checkResult = await this.checkRecordingIntegrity(recording);
            results.checked++;

            if (!checkResult.valid) {
              results.violations.push({
                recordingId: recording.id,
                violations: checkResult.violations,
                severity: checkResult.severity
              });

              // Atualizar contadores
              checkResult.violations.forEach(violation => {
                switch (violation.type) {
                  case 'HASH_MISMATCH':
                    results.summary.hashMismatches++;
                    break;
                  case 'FILE_MISSING':
                    results.summary.missingFiles++;
                    break;
                  case 'SIGNATURE_INVALID':
                    results.summary.signatureFailures++;
                    break;
                  case 'ENCRYPTION_ISSUE':
                    results.summary.encryptionIssues++;
                    break;
                  case 'BLOCKCHAIN_MISMATCH':
                    results.summary.blockchainMismatches++;
                    break;
                }
              });
            }
          } catch (error) {
            results.errors.push({
              recordingId: recording.id,
              error: error.message
            });
          }
        }

        // Pequena pausa entre lotes
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      const processingTime = Date.now() - startTime;
      const violationRate = results.violations.length / results.total;

      // Atualizar estatísticas
      this.statistics.totalChecks++;
      this.statistics.violationsFound += results.violations.length;
      this.lastCheck = new Date().toISOString();

      if (results.violations.length > 0) {
        this.statistics.lastViolation = new Date().toISOString();
      }

      // Salvar resultados no banco
      await this.saveCheckResults('FULL', results, processingTime);

      // Verificar se precisa alertar
      if (violationRate > this.alertThreshold) {
        await this.sendIntegrityAlert(results, violationRate);
      }

      console.log(`[IntegrityMonitor] Verificação completa concluída: ${results.checked}/${results.total} arquivos, ${results.violations.length} violações`);

      // Log conclusão da verificação
      await this.auditService.logAction({
        action: 'INTEGRITY_FULL_CHECK_COMPLETE',
        userId: 'system',
        userName: 'Integrity Monitor',
        userRole: 'system',
        details: {
          checkType: 'FULL',
          results: {
            total: results.total,
            checked: results.checked,
            violations: results.violations.length,
            violationRate: Math.round(violationRate * 100) / 100
          },
          processingTimeMs: processingTime
        },
        severity: results.violations.length > 0 ? 'WARNING' : 'INFO',
        category: 'INTEGRITY_MONITORING'
      });

    } catch (error) {
      console.error('[IntegrityMonitor] Erro na verificação completa:', error);
      
      await this.auditService.logAction({
        action: 'INTEGRITY_FULL_CHECK_ERROR',
        userId: 'system',
        userName: 'Integrity Monitor',
        userRole: 'system',
        details: {
          error: error.message,
          processingTimeMs: Date.now() - startTime
        },
        severity: 'ERROR',
        category: 'INTEGRITY_MONITORING'
      });
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Executar verificação rápida (apenas arquivos modificados recentemente)
   */
  async runQuickIntegrityCheck() {
    try {
      console.log('[IntegrityMonitor] Iniciando verificação rápida');

      // Verificar apenas arquivos modificados nas últimas 2 horas
      const recordings = await this.pool.query(`
        SELECT id, file_path, encrypted_path, file_hash, encrypted_hash, 
               digital_signature, encrypted, verification_status
        FROM recordings 
        WHERE file_path IS NOT NULL
        AND (updated_at > NOW() - INTERVAL '2 hours' 
             OR last_verification_at IS NULL 
             OR last_verification_at < NOW() - INTERVAL '24 hours')
        ORDER BY updated_at DESC
        LIMIT 100
      `);

      if (recordings.rows.length === 0) {
        console.log('[IntegrityMonitor] Nenhum arquivo para verificação rápida');
        return;
      }

      const violations = [];
      
      for (const recording of recordings.rows) {
        const checkResult = await this.checkRecordingIntegrity(recording);
        if (!checkResult.valid) {
          violations.push({
            recordingId: recording.id,
            violations: checkResult.violations
          });
        }
      }

      if (violations.length > 0) {
        console.log(`[IntegrityMonitor] Verificação rápida encontrou ${violations.length} violações`);
        
        // Agendar verificação completa se muitas violações
        if (violations.length > 5) {
          setTimeout(() => this.runFullIntegrityCheck(), 5000);
        }
      }

    } catch (error) {
      console.error('[IntegrityMonitor] Erro na verificação rápida:', error);
    }
  }

  /**
   * Verificar arquivos críticos (assinados digitalmente)
   */
  async runCriticalFilesCheck() {
    try {
      const criticalFiles = await this.pool.query(`
        SELECT id, file_path, encrypted_path, file_hash, encrypted_hash,
               digital_signature, encrypted, verification_status
        FROM recordings
        WHERE digital_signature IS NOT NULL
        AND verification_status != 'INVALID'
        ORDER BY created_at DESC
        LIMIT 20
      `);

      for (const recording of criticalFiles.rows) {
        const checkResult = await this.checkRecordingIntegrity(recording);
        
        if (!checkResult.valid) {
          // Arquivo crítico com problema - alertar imediatamente
          await this.sendCriticalFileAlert(recording.id, checkResult.violations);
        }
      }

    } catch (error) {
      console.error('[IntegrityMonitor] Erro na verificação de arquivos críticos:', error);
    }
  }

  /**
   * Verificar integridade de uma gravação específica
   */
  async checkRecordingIntegrity(recording) {
    const violations = [];
    let severity = 'LOW';

    try {
      // 1. Verificar se arquivo existe
      const filePath = recording.encrypted ? recording.encrypted_path : recording.file_path;
      
      if (!fs.existsSync(filePath)) {
        violations.push({
          type: 'FILE_MISSING',
          message: 'Arquivo não encontrado',
          path: filePath
        });
        severity = 'CRITICAL';
      } else {
        // 2. Verificar hash do arquivo
        const currentHash = this.calculateFileHash(filePath);
        const expectedHash = recording.encrypted ? recording.encrypted_hash : recording.file_hash;
        
        if (expectedHash && currentHash !== expectedHash) {
          violations.push({
            type: 'HASH_MISMATCH',
            message: 'Hash do arquivo não confere',
            expected: expectedHash,
            current: currentHash
          });
          severity = 'HIGH';
        }

        // 3. Verificar assinatura digital (se existir)
        if (recording.digital_signature) {
          try {
            const signatureVerification = await this.digitalSignatureService.verifyIntegrity(
              recording.file_path,
              recording.signature_path,
              {
                token: recording.timestamp_token,
                authority: recording.timestamp_authority,
                timestamp: recording.timestamp_at
              }
            );

            if (!signatureVerification.overallValid) {
              violations.push({
                type: 'SIGNATURE_INVALID',
                message: 'Assinatura digital inválida',
                details: signatureVerification
              });
              severity = 'HIGH';
            }
          } catch (error) {
            violations.push({
              type: 'SIGNATURE_ERROR',
              message: 'Erro na verificação de assinatura',
              error: error.message
            });
          }
        }

        // 4. Verificar criptografia (se aplicável)
        if (recording.encrypted) {
          try {
            const encryptionVerification = await this.encryptionService.verifyEncryptedFile(
              recording.encrypted_path,
              recording.encrypted_hash
            );

            if (!encryptionVerification.valid) {
              violations.push({
                type: 'ENCRYPTION_ISSUE',
                message: 'Problema na verificação de criptografia',
                details: encryptionVerification
              });
              severity = 'MEDIUM';
            }
          } catch (error) {
            violations.push({
              type: 'ENCRYPTION_ERROR',
              message: 'Erro na verificação de criptografia',
              error: error.message
            });
          }
        }

        // 5. Verificar blockchain (se disponível)
        try {
          const blockchainVerification = await this.blockchainService.verifyIntegrity(
            recording.id,
            currentHash
          );

          if (blockchainVerification.verified === false && blockchainVerification.reason === 'HASH_MISMATCH') {
            violations.push({
              type: 'BLOCKCHAIN_MISMATCH',
              message: 'Hash não confere com blockchain',
              details: blockchainVerification
            });
            severity = 'HIGH';
          }
        } catch (error) {
          // Blockchain pode não estar disponível - não é crítico
          console.warn(`[IntegrityMonitor] Blockchain não disponível para ${recording.id}`);
        }
      }

      // Atualizar status no banco
      const newStatus = violations.length > 0 ? 'INVALID' : 'VALID';
      await this.pool.query(
        'UPDATE recordings SET verification_status = $1, last_verification_at = $2 WHERE id = $3',
        [newStatus, new Date(), recording.id]
      );

      return {
        valid: violations.length === 0,
        violations,
        severity,
        checkedAt: new Date().toISOString()
      };

    } catch (error) {
      console.error(`[IntegrityMonitor] Erro ao verificar ${recording.id}:`, error);
      return {
        valid: false,
        violations: [{
          type: 'CHECK_ERROR',
          message: 'Erro na verificação',
          error: error.message
        }],
        severity: 'MEDIUM'
      };
    }
  }

  /**
   * Calcular hash de arquivo
   */
  calculateFileHash(filePath) {
    try {
      const fileBuffer = fs.readFileSync(filePath);
      return crypto.createHash('sha256').update(fileBuffer).digest('hex');
    } catch (error) {
      throw new Error(`Erro ao calcular hash: ${error.message}`);
    }
  }

  /**
   * Salvar resultados da verificação
   */
  async saveCheckResults(checkType, results, processingTime) {
    try {
      await this.pool.query(
        `INSERT INTO integrity_check_results 
         (check_type, total_files, checked_files, violations_found, processing_time_ms, results_data) 
         VALUES ($1, $2, $3, $4, $5, $6)`,
        [
          checkType,
          results.total,
          results.checked,
          results.violations.length,
          processingTime,
          JSON.stringify(results)
        ]
      );
    } catch (error) {
      console.error('[IntegrityMonitor] Erro ao salvar resultados:', error);
    }
  }

  /**
   * Enviar alerta de integridade
   */
  async sendIntegrityAlert(results, violationRate) {
    try {
      console.warn(`[IntegrityMonitor] ALERTA: Taxa de violação alta: ${Math.round(violationRate * 100)}%`);
      
      // Log do alerta
      await this.auditService.logAction({
        action: 'INTEGRITY_ALERT',
        userId: 'system',
        userName: 'Integrity Monitor',
        userRole: 'system',
        details: {
          violationRate,
          totalViolations: results.violations.length,
          totalFiles: results.total,
          summary: results.summary
        },
        severity: 'CRITICAL',
        category: 'INTEGRITY_ALERT'
      });

      // Aqui você pode implementar notificações por email, Slack, etc.
      // await this.sendEmailAlert(results);
      // await this.sendSlackAlert(results);
      
    } catch (error) {
      console.error('[IntegrityMonitor] Erro ao enviar alerta:', error);
    }
  }

  /**
   * Enviar alerta para arquivo crítico
   */
  async sendCriticalFileAlert(recordingId, violations) {
    try {
      console.error(`[IntegrityMonitor] ALERTA CRÍTICO: Arquivo ${recordingId} comprometido`);
      
      await this.auditService.logAction({
        action: 'CRITICAL_FILE_ALERT',
        recordingId,
        userId: 'system',
        userName: 'Integrity Monitor',
        userRole: 'system',
        details: {
          violations,
          alertLevel: 'CRITICAL'
        },
        severity: 'CRITICAL',
        category: 'INTEGRITY_ALERT'
      });

    } catch (error) {
      console.error('[IntegrityMonitor] Erro ao enviar alerta crítico:', error);
    }
  }

  /**
   * Obter estatísticas do monitor
   */
  getMonitoringStats() {
    return {
      enabled: this.monitoringEnabled,
      isRunning: this.isRunning,
      lastCheck: this.lastCheck,
      checkInterval: this.checkInterval,
      batchSize: this.batchSize,
      alertThreshold: this.alertThreshold,
      statistics: this.statistics,
      generatedAt: new Date().toISOString()
    };
  }

  /**
   * Parar monitoramento
   */
  stop() {
    this.monitoringEnabled = false;
    console.log('[IntegrityMonitor] Monitoramento parado');
  }

  /**
   * Iniciar monitoramento
   */
  start() {
    this.monitoringEnabled = true;
    this.initializeScheduler();
    console.log('[IntegrityMonitor] Monitoramento iniciado');
  }
}

module.exports = IntegrityMonitorService;
