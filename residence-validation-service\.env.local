# Configurações do Google OAuth2
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
NEXT_PUBLIC_GOOGLE_REDIRECT_URI=http://localhost:3000/api/auth/callback

# JWT Secret
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production

# Chave de criptografia para dados sensíveis
ENCRYPTION_KEY=your_32_char_encryption_key_here!!

# URL base da aplicação
NEXT_PUBLIC_BASE_URL=http://localhost:3000

# Configurações do banco de dados (para implementação futura)
DATABASE_URL=postgresql://username:password@localhost:5432/residencia_db

# Configurações de ambiente
NODE_ENV=development
