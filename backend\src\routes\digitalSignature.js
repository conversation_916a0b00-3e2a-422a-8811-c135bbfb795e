const express = require('express');
const DigitalSignatureService = require('../services/digitalSignatureService');
const BlockchainService = require('../services/blockchainService');
const AuditService = require('../services/auditService');
const { Pool } = require('pg');
const router = express.Router();

// Pool de conexões PostgreSQL
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'cartorio_db',
  user: process.env.DB_USER || 'cartorio_user',
  password: process.env.DB_PASSWORD || 'cartorio_secure_pass',
});

const digitalSignatureService = new DigitalSignatureService();
const blockchainService = new BlockchainService();
const auditService = new AuditService();

/**
 * @swagger
 * /api/digital-signature/sign/{recordingId}:
 *   post:
 *     summary: Assinar gravação digitalmente
 *     tags: [Digital Signature]
 *     parameters:
 *       - in: path
 *         name: recordingId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID da gravação
 *     responses:
 *       200:
 *         description: Gravação assinada com sucesso
 *       404:
 *         description: Gravação não encontrada
 *       500:
 *         description: Erro interno do servidor
 */
router.post('/sign/:recordingId', async (req, res) => {
  const { recordingId } = req.params;
  
  try {
    // Buscar gravação no banco
    const recordingQuery = 'SELECT * FROM recordings WHERE id = $1';
    const recordingResult = await pool.query(recordingQuery, [recordingId]);
    
    if (recordingResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Gravação não encontrada'
      });
    }
    
    const recording = recordingResult.rows[0];
    const filePath = recording.file_path;
    
    // Log início da operação
    await pool.query(
      'INSERT INTO digital_signature_logs (recording_id, operation, status, details) VALUES ($1, $2, $3, $4)',
      [recordingId, 'SIGN', 'STARTED', JSON.stringify({ filePath, startedAt: new Date() })]
    );
    
    const startTime = Date.now();
    
    // Assinar arquivo e obter carimbo de tempo
    const signatureResult = await digitalSignatureService.signAndTimestamp(filePath, recordingId);
    
    const processingTime = Date.now() - startTime;
    
    // Atualizar gravação com dados de assinatura
    const updateQuery = `
      UPDATE recordings SET 
        file_hash = $1,
        digital_signature = $2,
        signature_path = $3,
        certificate_data = $4,
        signed_at = $5,
        timestamp_token = $6,
        timestamp_authority = $7,
        timestamp_nonce = $8,
        timestamp_at = $9,
        verification_status = 'SIGNED'
      WHERE id = $10
    `;
    
    await pool.query(updateQuery, [
      signatureResult.hash,
      signatureResult.signature.data,
      signatureResult.signature.path,
      signatureResult.signature.certificate,
      signatureResult.signature.signedAt,
      signatureResult.timestamp.token,
      signatureResult.timestamp.authority,
      signatureResult.timestamp.nonce,
      signatureResult.timestamp.timestamp,
      recordingId
    ]);
    
    // Registrar carimbo de tempo
    await pool.query(
      `INSERT INTO timestamp_records 
       (recording_id, hash_value, timestamp_token, timestamp_authority, nonce, timestamp_at) 
       VALUES ($1, $2, $3, $4, $5, $6)`,
      [
        recordingId,
        signatureResult.hash,
        signatureResult.timestamp.token,
        signatureResult.timestamp.authority,
        signatureResult.timestamp.nonce,
        signatureResult.timestamp.timestamp
      ]
    );
    
    // Log sucesso da operação
    await pool.query(
      'INSERT INTO digital_signature_logs (recording_id, operation, status, details, processing_time_ms) VALUES ($1, $2, $3, $4, $5)',
      [
        recordingId, 
        'SIGN', 
        'SUCCESS', 
        JSON.stringify({
          hash: signatureResult.hash,
          signedAt: signatureResult.signature.signedAt,
          timestampAuthority: signatureResult.timestamp.authority,
          timestampFallback: signatureResult.timestamp.fallback || false
        }),
        processingTime
      ]
    );
    
    res.json({
      success: true,
      message: 'Gravação assinada digitalmente com sucesso',
      data: {
        recordingId,
        hash: signatureResult.hash,
        signedAt: signatureResult.signature.signedAt,
        timestamped: !signatureResult.timestamp.fallback,
        processingTimeMs: processingTime
      }
    });
    
  } catch (error) {
    console.error('[DigitalSignature] Erro ao assinar gravação:', error);
    
    // Log erro da operação
    await pool.query(
      'INSERT INTO digital_signature_logs (recording_id, operation, status, error_message) VALUES ($1, $2, $3, $4)',
      [recordingId, 'SIGN', 'FAILED', error.message]
    );
    
    res.status(500).json({
      success: false,
      error: 'Erro ao assinar gravação digitalmente',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/digital-signature/verify/{recordingId}:
 *   post:
 *     summary: Verificar integridade de gravação
 *     tags: [Digital Signature]
 *     parameters:
 *       - in: path
 *         name: recordingId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID da gravação
 *     responses:
 *       200:
 *         description: Verificação concluída
 *       404:
 *         description: Gravação não encontrada
 *       500:
 *         description: Erro interno do servidor
 */
router.post('/verify/:recordingId', async (req, res) => {
  const { recordingId } = req.params;
  
  try {
    // Buscar gravação no banco
    const recordingQuery = 'SELECT * FROM recordings WHERE id = $1';
    const recordingResult = await pool.query(recordingQuery, [recordingId]);
    
    if (recordingResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Gravação não encontrada'
      });
    }
    
    const recording = recordingResult.rows[0];
    
    if (!recording.digital_signature) {
      return res.status(400).json({
        success: false,
        error: 'Gravação não possui assinatura digital'
      });
    }
    
    const startTime = Date.now();
    
    // Verificar integridade
    const verificationResult = await digitalSignatureService.verifyIntegrity(
      recording.file_path,
      recording.signature_path,
      {
        token: recording.timestamp_token,
        authority: recording.timestamp_authority,
        timestamp: recording.timestamp_at
      }
    );
    
    const processingTime = Date.now() - startTime;
    
    // Registrar verificação
    await pool.query(
      `INSERT INTO integrity_verifications 
       (recording_id, verification_type, original_hash, current_hash, hash_match, 
        signature_valid, timestamp_valid, overall_status, verification_details, verified_by) 
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`,
      [
        recordingId,
        'FULL',
        recording.file_hash,
        digitalSignatureService.generateFileHash(recording.file_path),
        verificationResult.signatureValid && verificationResult.timestampValid,
        verificationResult.signatureValid,
        verificationResult.timestampValid,
        verificationResult.overallValid ? 'VALID' : 'INVALID',
        JSON.stringify(verificationResult.details),
        req.user?.id || 'system'
      ]
    );
    
    // Atualizar status da gravação
    await pool.query(
      'UPDATE recordings SET integrity_verified = $1, last_verification_at = $2, verification_status = $3 WHERE id = $4',
      [
        verificationResult.overallValid,
        new Date(),
        verificationResult.overallValid ? 'VALID' : 'INVALID',
        recordingId
      ]
    );
    
    // Log da operação
    await pool.query(
      'INSERT INTO digital_signature_logs (recording_id, operation, status, details, processing_time_ms) VALUES ($1, $2, $3, $4, $5)',
      [
        recordingId,
        'VERIFY',
        verificationResult.overallValid ? 'SUCCESS' : 'WARNING',
        JSON.stringify({
          signatureValid: verificationResult.signatureValid,
          timestampValid: verificationResult.timestampValid,
          overallValid: verificationResult.overallValid
        }),
        processingTime
      ]
    );
    
    // Gerar relatório
    const report = digitalSignatureService.generateIntegrityReport(verificationResult);
    
    res.json({
      success: true,
      message: 'Verificação de integridade concluída',
      data: {
        recordingId,
        verification: verificationResult,
        report,
        processingTimeMs: processingTime
      }
    });
    
  } catch (error) {
    console.error('[DigitalSignature] Erro ao verificar gravação:', error);
    
    // Log erro da operação
    await pool.query(
      'INSERT INTO digital_signature_logs (recording_id, operation, status, error_message) VALUES ($1, $2, $3, $4)',
      [recordingId, 'VERIFY', 'FAILED', error.message]
    );
    
    res.status(500).json({
      success: false,
      error: 'Erro ao verificar integridade da gravação',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/digital-signature/batch-sign:
 *   post:
 *     summary: Assinar múltiplas gravações em lote
 *     tags: [Digital Signature]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               recordingIds:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: Processamento em lote iniciado
 *       400:
 *         description: Parâmetros inválidos
 *       500:
 *         description: Erro interno do servidor
 */
router.post('/batch-sign', async (req, res) => {
  const { recordingIds } = req.body;
  
  if (!Array.isArray(recordingIds) || recordingIds.length === 0) {
    return res.status(400).json({
      success: false,
      error: 'Lista de IDs de gravação é obrigatória'
    });
  }
  
  if (recordingIds.length > 100) {
    return res.status(400).json({
      success: false,
      error: 'Máximo de 100 gravações por lote'
    });
  }
  
  try {
    const results = {
      total: recordingIds.length,
      success: 0,
      failed: 0,
      details: []
    };
    
    // Processar cada gravação
    for (const recordingId of recordingIds) {
      try {
        // Fazer requisição interna para assinar
        const signResult = await fetch(`http://localhost:${process.env.PORT || 3001}/api/digital-signature/sign/${recordingId}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': req.headers.authorization || ''
          }
        });
        
        if (signResult.ok) {
          results.success++;
          results.details.push({
            recordingId,
            status: 'SUCCESS',
            message: 'Assinado com sucesso'
          });
        } else {
          results.failed++;
          results.details.push({
            recordingId,
            status: 'FAILED',
            message: 'Erro na assinatura'
          });
        }
      } catch (error) {
        results.failed++;
        results.details.push({
          recordingId,
          status: 'FAILED',
          message: error.message
        });
      }
    }
    
    res.json({
      success: true,
      message: `Processamento em lote concluído: ${results.success} sucessos, ${results.failed} falhas`,
      data: results
    });
    
  } catch (error) {
    console.error('[DigitalSignature] Erro no processamento em lote:', error);
    res.status(500).json({
      success: false,
      error: 'Erro no processamento em lote',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/digital-signature/statistics:
 *   get:
 *     summary: Obter estatísticas de integridade
 *     tags: [Digital Signature]
 *     responses:
 *       200:
 *         description: Estatísticas de integridade
 *       500:
 *         description: Erro interno do servidor
 */
router.get('/statistics', async (req, res) => {
  try {
    // Obter estatísticas usando função do banco
    const statsResult = await pool.query('SELECT * FROM get_integrity_statistics()');
    const stats = statsResult.rows[0];
    
    // Estatísticas adicionais
    const recentVerifications = await pool.query(
      'SELECT COUNT(*) as count FROM integrity_verifications WHERE verified_at > NOW() - INTERVAL \'24 hours\''
    );
    
    const criticalIssues = await pool.query(
      'SELECT COUNT(*) as count FROM integrity_audit_logs WHERE severity = \'CRITICAL\' AND resolved_at IS NULL'
    );
    
    res.json({
      success: true,
      data: {
        overview: {
          totalRecordings: parseInt(stats.total_recordings),
          signedRecordings: parseInt(stats.signed_recordings),
          timestampedRecordings: parseInt(stats.timestamped_recordings),
          verifiedRecordings: parseInt(stats.verified_recordings),
          integrityViolations: parseInt(stats.integrity_violations),
          pendingVerifications: parseInt(stats.pending_verifications)
        },
        recent: {
          verificationsLast24h: parseInt(recentVerifications.rows[0].count),
          criticalIssues: parseInt(criticalIssues.rows[0].count)
        },
        percentages: {
          signedPercentage: stats.total_recordings > 0 ? 
            Math.round((stats.signed_recordings / stats.total_recordings) * 100) : 0,
          timestampedPercentage: stats.total_recordings > 0 ? 
            Math.round((stats.timestamped_recordings / stats.total_recordings) * 100) : 0,
          verifiedPercentage: stats.total_recordings > 0 ? 
            Math.round((stats.verified_recordings / stats.total_recordings) * 100) : 0
        },
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    console.error('[DigitalSignature] Erro ao obter estatísticas:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao obter estatísticas de integridade',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/digital-signature/integrity-report:
 *   get:
 *     summary: Obter relatório de integridade
 *     tags: [Digital Signature]
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Número máximo de registros
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [VALID, INVALID, PENDING]
 *         description: Filtrar por status
 *     responses:
 *       200:
 *         description: Relatório de integridade
 *       500:
 *         description: Erro interno do servidor
 */
router.get('/integrity-report', async (req, res) => {
  try {
    const { limit = 50, status } = req.query;
    
    let query = 'SELECT * FROM integrity_report';
    const params = [];
    
    if (status) {
      query += ' WHERE verification_status = $1';
      params.push(status);
    }
    
    query += ' ORDER BY last_verification_at DESC NULLS LAST';
    
    if (limit) {
      query += ` LIMIT $${params.length + 1}`;
      params.push(parseInt(limit));
    }
    
    const result = await pool.query(query, params);
    
    res.json({
      success: true,
      data: {
        recordings: result.rows,
        total: result.rows.length,
        filters: { status, limit },
        generatedAt: new Date().toISOString()
      }
    });
    
  } catch (error) {
    console.error('[DigitalSignature] Erro ao gerar relatório:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao gerar relatório de integridade',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/digital-signature/blockchain/register/{recordingId}:
 *   post:
 *     summary: Registrar hash na blockchain
 *     tags: [Blockchain]
 *     parameters:
 *       - in: path
 *         name: recordingId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID da gravação
 *     responses:
 *       200:
 *         description: Hash registrado na blockchain
 *       404:
 *         description: Gravação não encontrada
 *       500:
 *         description: Erro interno do servidor
 */
router.post('/blockchain/register/:recordingId', async (req, res) => {
  const { recordingId } = req.params;

  try {
    // Buscar gravação no banco
    const recordingQuery = 'SELECT * FROM recordings WHERE id = $1';
    const recordingResult = await pool.query(recordingQuery, [recordingId]);

    if (recordingResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Gravação não encontrada'
      });
    }

    const recording = recordingResult.rows[0];

    if (!recording.file_hash) {
      return res.status(400).json({
        success: false,
        error: 'Gravação não possui hash calculado'
      });
    }

    // Preparar metadados para blockchain
    const metadata = {
      filename: recording.filename,
      duration: recording.duration,
      signedAt: recording.signed_at,
      timestampAuthority: recording.timestamp_authority,
      registeredBy: req.user?.id || 'system'
    };

    // Registrar na blockchain
    const blockchainResult = await blockchainService.registerHash(
      recordingId,
      recording.file_hash,
      metadata
    );

    // Salvar dados da blockchain no banco
    await pool.query(
      `INSERT INTO blockchain_records
       (recording_id, transaction_hash, block_number, gas_used, registered_at)
       VALUES ($1, $2, $3, $4, $5)`,
      [
        recordingId,
        blockchainResult.transactionHash,
        blockchainResult.blockNumber,
        blockchainResult.gasUsed,
        blockchainResult.registeredAt
      ]
    );

    res.json({
      success: true,
      message: 'Hash registrado na blockchain com sucesso',
      data: blockchainResult
    });

  } catch (error) {
    console.error('[Blockchain] Erro ao registrar hash:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao registrar na blockchain',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/digital-signature/blockchain/verify/{recordingId}:
 *   get:
 *     summary: Verificar integridade via blockchain
 *     tags: [Blockchain]
 *     parameters:
 *       - in: path
 *         name: recordingId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID da gravação
 *     responses:
 *       200:
 *         description: Verificação concluída
 *       404:
 *         description: Gravação não encontrada
 *       500:
 *         description: Erro interno do servidor
 */
router.get('/blockchain/verify/:recordingId', async (req, res) => {
  const { recordingId } = req.params;

  try {
    // Buscar gravação no banco
    const recordingQuery = 'SELECT * FROM recordings WHERE id = $1';
    const recordingResult = await pool.query(recordingQuery, [recordingId]);

    if (recordingResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Gravação não encontrada'
      });
    }

    const recording = recordingResult.rows[0];

    // Calcular hash atual do arquivo
    const currentHash = digitalSignatureService.generateFileHash(recording.file_path);

    // Verificar na blockchain
    const verificationResult = await blockchainService.verifyIntegrity(recordingId, currentHash);

    res.json({
      success: true,
      message: 'Verificação blockchain concluída',
      data: verificationResult
    });

  } catch (error) {
    console.error('[Blockchain] Erro na verificação:', error);
    res.status(500).json({
      success: false,
      error: 'Erro na verificação blockchain',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/digital-signature/blockchain/stats:
 *   get:
 *     summary: Obter estatísticas da blockchain
 *     tags: [Blockchain]
 *     responses:
 *       200:
 *         description: Estatísticas da blockchain
 *       500:
 *         description: Erro interno do servidor
 */
router.get('/blockchain/stats', async (req, res) => {
  try {
    const stats = await blockchainService.getBlockchainStats();

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('[Blockchain] Erro ao obter estatísticas:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao obter estatísticas da blockchain',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
