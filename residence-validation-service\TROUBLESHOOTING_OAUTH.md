# 🚨 G<PERSON><PERSON> de Troubleshooting - OAuth2 Docker

## Problema: <PERSON>rro "OAuth credentials not configured"

### 1. **PRIMEIRA VERIFICAÇÃO: Execute o script de diagnóstico**

```powershell
# No Windows
.\check-env.ps1

# No Linux/Mac
./check-env.sh
```

---

### 2. **CONFIGURAÇÃO RÁPIDA DO .env.production**

```bash
# 1. Copie o arquivo de exemplo
cp .env.production.example .env.production

# 2. Edite com seus valores reais
# SUBSTITUA os valores abaixo pelos seus:

GOOGLE_CLIENT_ID=123456789-abcdefghijk.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=GOCSPX-abcdefghijklmnopqrstuvwxyz
NEXT_PUBLIC_GOOGLE_REDIRECT_URI=https://seudominio.com/api/auth/callback
JWT_SECRET=sua_chave_jwt_super_secreta_de_32_chars_ou_mais
ENC<PERSON>TION_KEY=12345678901234567890123456789012
NEXT_PUBLIC_BASE_URL=https://seudominio.com
```

---

### 3. **OBTER CREDENCIAIS DO GOOGLE**

1. **Acesse:** https://console.cloud.google.com/
2. **Crie/selecione um projeto**
3. **Ative APIs necessárias:**
   - Google+ API
   - Google Identity API
4. **Criar credenciais:**
   - Vá em "Credenciais" → "Criar credenciais" → "ID do cliente OAuth 2.0"
   - Tipo: **Aplicação da Web**
   - **Origens JavaScript autorizadas:** 
     - `http://localhost:3000` (desenvolvimento)
     - `https://seudominio.com` (produção)
   - **URIs de redirecionamento:**
     - `http://localhost:3000/api/auth/callback` (desenvolvimento)
     - `https://seudominio.com/api/auth/callback` (produção)

---

### 4. **VERIFICAR DOCKER COMPOSE**

```yaml
# Certifique-se que o docker-compose.prod.yml tem:
services:
  app:
    env_file:
      - .env.production
    environment:
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID:-}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET:-}
      # ... outras variáveis
```

---

### 5. **TESTAR A CONFIGURAÇÃO**

```bash
# 1. Parar containers existentes
docker-compose -f docker-compose.prod.yml down

# 2. Reconstruir com novas variáveis
docker-compose -f docker-compose.prod.yml up --build -d

# 3. Verificar logs
docker-compose -f docker-compose.prod.yml logs -f app

# 4. Testar endpoint de debug
curl http://localhost:3000/api/debug/env
```

---

### 6. **PROBLEMAS COMUNS E SOLUÇÕES**

| Problema | Solução |
|----------|---------|
| "NÃO CONFIGURADO" no debug | Verificar se .env.production existe e tem valores corretos |
| "VALOR PADRÃO" no debug | Substituir valores placeholder pelos reais |
| "OAuth redirect_uri_mismatch" | Verificar se URI no Google Console coincide com REDIRECT_URI |
| "invalid_client" | Verificar GOOGLE_CLIENT_ID e GOOGLE_CLIENT_SECRET |
| Variáveis não carregam no Docker | Verificar se .env.production está no mesmo diretório |

---

### 7. **COMANDOS DE DEBUG**

```bash
# Verificar se variáveis estão sendo lidas pelo container
docker exec sistema-residencia-app env | grep GOOGLE

# Verificar logs específicos do OAuth
docker-compose -f docker-compose.prod.yml logs app | grep -i oauth

# Testar endpoint de saúde
curl http://localhost:3000/api/health

# Verificar se o container está rodando
docker ps | grep sistema-residencia
```

---

### 8. **LAST RESORT: Rebuild completo**

```bash
# Parar tudo
docker-compose -f docker-compose.prod.yml down -v

# Limpar imagens
docker rmi $(docker images -q sistema-residencia*)

# Reconstruir do zero
docker-compose -f docker-compose.prod.yml up --build -d

# Monitorar inicialização
docker-compose -f docker-compose.prod.yml logs -f app
```

---

### 9. **CHECKLIST FINAL**

- [ ] Arquivo `.env.production` existe
- [ ] Todas as variáveis têm valores reais (não placeholder)
- [ ] Google OAuth2 configurado no Console
- [ ] URIs de redirect corretas
- [ ] Container reiniciado após mudanças
- [ ] Endpoint `/api/debug/env` retorna "CONFIGURADO"
- [ ] Logs do Docker não mostram erros OAuth

---

### 🆘 **Se ainda não funcionar:**

1. Execute `.\check-env.ps1` e corrija todos os erros
2. Verifique se o domínio no Google Console está correto
3. Tente primeiro em desenvolvimento local
4. Consulte os logs completos: `docker-compose -f docker-compose.prod.yml logs app`
