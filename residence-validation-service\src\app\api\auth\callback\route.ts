import { NextRequest, NextResponse } from 'next/server';
import { googleAuthService } from '@/lib/google-auth';
import jwt from 'jsonwebtoken';
import { ENV } from '@/lib/config';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');
    const error = searchParams.get('error');

    if (error) {
      return NextResponse.redirect(new URL(`/?error=${error}`, request.url));
    }

    if (!code) {
      return NextResponse.redirect(new URL('/?error=no_code', request.url));
    }

    // Troca código por tokens
    const tokens = await googleAuthService.getTokens(code);
    googleAuthService.setCredentials(tokens);

    // Obtém informações do usuário
    const userInfo = await googleAuthService.getUserInfo();

    if (!userInfo.email || !userInfo.id) {
      return NextResponse.redirect(new URL('/?error=invalid_user', request.url));
    }

    // Cria JWT com informações do usuário
    const user = {
      id: userInfo.id,
      email: userInfo.email,
      name: userInfo.name || '',
      googleId: userInfo.id,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    const jwtToken = jwt.sign(
      { 
        userId: user.id, 
        email: user.email,
        googleTokens: tokens 
      },
      ENV.JWT_SECRET,
      { expiresIn: '7d' }
    );

    // Redireciona para o dashboard com token
    const response = NextResponse.redirect(new URL('/dashboard', request.url));
    
    // Define cookie seguro com o token
    response.cookies.set('auth-token', jwtToken, {
      httpOnly: true,
      secure: ENV.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 dias
      path: '/',
    });

    return response;
  } catch (error) {
    console.error('Erro no callback de autenticação:', error);
    return NextResponse.redirect(new URL('/?error=auth_failed', request.url));
  }
}
