import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  LinearProgress,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Alert,
  CircularProgress
} from '@mui/material';
import {
  Assessment as AnalyticsIcon,
  VideoLibrary as RecordingsIcon,
  People as UsersIcon,
  Storage as StorageIcon,
  Security as SecurityIcon,
  TrendingUp as TrendingIcon,
  Memory as MemoryIcon,
  Speed as PerformanceIcon
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, Cell } from 'recharts';

interface DashboardStats {
  totalRecordings: number;
  totalSizeGB: number;
  avgDurationMinutes: number;
  completedRecordings: number;
  processingRecordings: number;
  failedRecordings: number;
  archivedRecordings: number;
  uniqueUsers: number;
  completionRate: number;
  systemUptime: number;
  memoryUsage: {
    used: number;
    total: number;
  };
}

interface TrendData {
  period: string;
  count: number;
  total_size: number;
  avg_duration: number;
}

interface UserData {
  user_name: string;
  recording_count: number;
}

interface ComplianceData {
  totalRecordings: number;
  withConsent: number;
  withoutConsent: number;
  complianceRate: number;
  dataRetentionStatus: 'compliant' | 'warning' | 'violation';
  recommendations: string[];
}

const AnalyticsDashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [trends, setTrends] = useState<TrendData[]>([]);
  const [users, setUsers] = useState<UserData[]>([]);
  const [compliance, setCompliance] = useState<ComplianceData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchAnalyticsData();
  }, []);

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Carregar dados com fallback individual
      const results = await Promise.allSettled([
        fetch('/api/analytics/dashboard').then(res => res.ok ? res.json() : null),
        fetch('/api/analytics/trends?period=month').then(res => res.ok ? res.json() : null),
        fetch('/api/analytics/users').then(res => res.ok ? res.json() : null),
        fetch('/api/analytics/compliance').then(res => res.ok ? res.json() : null)
      ]);

      // Processar resultados com fallbacks
      const [statsResult, trendsResult, usersResult, complianceResult] = results;

      // Stats com fallback
      if (statsResult.status === 'fulfilled' && statsResult.value?.success) {
        setStats(statsResult.value.data);
      } else {
        console.warn('Falha ao carregar estatísticas, usando dados padrão');
        setStats({
          totalRecordings: 0,
          totalSize: 0,
          totalUsers: 0,
          avgDuration: 0,
          todayRecordings: 0,
          weekRecordings: 0,
          monthRecordings: 0
        });
      }

      // Trends com fallback
      if (trendsResult.status === 'fulfilled' && trendsResult.value?.success) {
        setTrends(trendsResult.value.data);
      } else {
        console.warn('Falha ao carregar tendências, usando dados padrão');
        setTrends([]);
      }

      // Users com fallback
      if (usersResult.status === 'fulfilled' && usersResult.value?.success) {
        setUsers(usersResult.value.data);
      } else {
        console.warn('Falha ao carregar usuários, usando dados padrão');
        setUsers([]);
      }

      // Compliance com fallback
      if (complianceResult.status === 'fulfilled' && complianceResult.value?.success) {
        setCompliance(complianceResult.value.data);
      } else {
        console.warn('Falha ao carregar compliance, usando dados padrão');
        setCompliance({
          totalRecordings: 0,
          withConsent: 0,
          complianceRate: 100,
          oldRecordings: 0,
          recentRecordings: 0,
          dataRetentionDays: 730
        });
      }

      // Verificar se pelo menos um endpoint funcionou
      const hasAnyData = results.some(result =>
        result.status === 'fulfilled' && result.value?.success
      );

      if (!hasAnyData) {
        throw new Error('Nenhum serviço de analytics está disponível');
      }

    } catch (error) {
      console.error('Erro ao carregar analytics:', error);
      setError(error instanceof Error ? error.message : 'Erro desconhecido ao carregar analytics');

      // Definir dados padrão em caso de erro total
      setStats({
        totalRecordings: 0,
        totalSize: 0,
        totalUsers: 0,
        avgDuration: 0,
        todayRecordings: 0,
        weekRecordings: 0,
        monthRecordings: 0
      });
      setTrends([]);
      setUsers([]);
      setCompliance({
        totalRecordings: 0,
        withConsent: 0,
        complianceRate: 100,
        oldRecordings: 0,
        recentRecordings: 0,
        dataRetentionDays: 730
      });
    } finally {
      setLoading(false);
    }
  };

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / (24 * 3600));
    const hours = Math.floor((seconds % (24 * 3600)) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${days}d ${hours}h ${minutes}m`;
  };

  const formatBytes = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  // Cores para gráficos
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4, display: 'flex', justifyContent: 'center' }}>
        <CircularProgress size={60} />
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error">
          Erro ao carregar dados: {error}
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 4, display: 'flex', alignItems: 'center', gap: 2 }}>
        <AnalyticsIcon sx={{ fontSize: 40, color: 'primary.main' }} />
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Dashboard de Analytics
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Visão geral das métricas e estatísticas do sistema
          </Typography>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* Cards de Estatísticas Principais */}
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <RecordingsIcon color="primary" />
                <Typography color="textSecondary" gutterBottom>
                  Total de Gravações
                </Typography>
              </Box>
              <Typography variant="h4" component="div">
                {stats?.totalRecordings || 0}
              </Typography>
              <Typography variant="body2" color="success.main">
                {stats?.completionRate}% concluídas
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <StorageIcon color="primary" />
                <Typography color="textSecondary" gutterBottom>
                  Armazenamento
                </Typography>
              </Box>
              <Typography variant="h4" component="div">
                {stats?.totalSizeGB?.toFixed(1)} GB
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Duração média: {stats?.avgDurationMinutes}min
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <UsersIcon color="primary" />
                <Typography color="textSecondary" gutterBottom>
                  Usuários Únicos
                </Typography>
              </Box>
              <Typography variant="h4" component="div">
                {stats?.uniqueUsers || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Usuários ativos
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <PerformanceIcon color="primary" />
                <Typography color="textSecondary" gutterBottom>
                  Sistema
                </Typography>
              </Box>
              <Typography variant="h6" component="div">
                Uptime: {formatUptime(stats?.systemUptime || 0)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Memória: {stats?.memoryUsage.used}MB / {stats?.memoryUsage.total}MB
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Status das Gravações */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Status das Gravações
              </Typography>
              <Box sx={{ mt: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Concluídas</Typography>
                  <Typography variant="body2">{stats?.completedRecordings}</Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={(stats?.completedRecordings || 0) / (stats?.totalRecordings || 1) * 100}
                  sx={{ mb: 2 }}
                />

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Processando</Typography>
                  <Typography variant="body2">{stats?.processingRecordings}</Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={(stats?.processingRecordings || 0) / (stats?.totalRecordings || 1) * 100}
                  color="warning"
                  sx={{ mb: 2 }}
                />

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Com Falha</Typography>
                  <Typography variant="body2">{stats?.failedRecordings}</Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={(stats?.failedRecordings || 0) / (stats?.totalRecordings || 1) * 100}
                  color="error"
                  sx={{ mb: 2 }}
                />

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Arquivadas</Typography>
                  <Typography variant="body2">{stats?.archivedRecordings}</Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={(stats?.archivedRecordings || 0) / (stats?.totalRecordings || 1) * 100}
                  color="secondary"
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Conformidade LGPD */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                <SecurityIcon color="primary" />
                <Typography variant="h6">
                  Conformidade LGPD
                </Typography>
                <Chip 
                  label={compliance?.complianceRate?.toFixed(1) + '%'}
                  color={
                    (compliance?.complianceRate || 0) >= 95 ? 'success' :
                    (compliance?.complianceRate || 0) >= 80 ? 'warning' : 'error'
                  }
                  size="small"
                />
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" gutterBottom>
                  Gravações com Consentimento: {compliance?.withConsent} / {compliance?.totalRecordings}
                </Typography>
                <LinearProgress 
                  variant="determinate" 
                  value={compliance?.complianceRate || 0}
                  color={
                    (compliance?.complianceRate || 0) >= 95 ? 'success' :
                    (compliance?.complianceRate || 0) >= 80 ? 'warning' : 'error'
                  }
                />
              </Box>

              <Alert 
                severity={
                  compliance?.dataRetentionStatus === 'compliant' ? 'success' :
                  compliance?.dataRetentionStatus === 'warning' ? 'warning' : 'error'
                }
                sx={{ mt: 2 }}
              >
                {compliance?.recommendations?.[0] || 'Sistema em conformidade'}
              </Alert>
            </CardContent>
          </Card>
        </Grid>

        {/* Gráfico de Tendências */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Tendência de Gravações por Mês
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={trends}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="period" />
                  <YAxis />
                  <Tooltip />
                  <Line 
                    type="monotone" 
                    dataKey="count" 
                    stroke="#1976d2" 
                    strokeWidth={2}
                    name="Gravações"
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Top Usuários */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Usuários Mais Ativos
              </Typography>
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Usuário</TableCell>
                      <TableCell align="right">Gravações</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {users.slice(0, 10).map((user, index) => (
                      <TableRow key={index}>
                        <TableCell component="th" scope="row">
                          {user.user_name}
                        </TableCell>
                        <TableCell align="right">
                          <Chip 
                            label={user.recording_count}
                            size="small"
                            color="primary"
                            variant="outlined"
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Gráfico de Armazenamento por Período */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Crescimento de Armazenamento (GB por Mês)
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={trends}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="period" />
                  <YAxis />
                  <Tooltip formatter={(value) => [formatBytes(value as number), 'Tamanho']} />
                  <Bar 
                    dataKey="total_size" 
                    fill="#00C49F"
                    name="Armazenamento"
                  />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
};

export default AnalyticsDashboard;
