import React, { useState, useEffect, forwardRef } from 'react';
import {
  Box,
  Alert,
  AlertTitle,
  Snackbar,
  LinearProgress,
  CircularProgress,
  Fade,
  Slide,
  Zoom,
  Typography,
  Card,
  CardContent,
  IconButton,
  Chip,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Step<PERSON>ontent,
  Skeleton,
  Backdrop
} from '@mui/material';
import {
  CheckCircle,
  Error,
  Warning,
  Info,
  Close,
  Refresh,
  CloudUpload,
  CloudDone,
  Schedule,
  Speed,
  Security,
  Assessment
} from '@mui/icons-material';

export interface FeedbackState {
  type: 'loading' | 'success' | 'error' | 'warning' | 'info' | 'progress';
  message: string;
  title?: string;
  progress?: number;
  details?: string[];
  duration?: number;
  persistent?: boolean;
  actions?: Array<{
    label: string;
    action: () => void;
    color?: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success';
  }>;
}

interface FeedbackSystemProps {
  feedback: FeedbackState | null;
  onClose?: () => void;
  position?: 'top' | 'bottom' | 'center';
  variant?: 'snackbar' | 'card' | 'overlay' | 'inline';
}

// Componente de transição personalizado
const SlideTransition = forwardRef((props: any, ref: React.Ref<unknown>) => {
  return <Slide direction="up" ref={ref} {...props} />;
});

const FeedbackSystem: React.FC<FeedbackSystemProps> = ({ 
  feedback, 
  onClose, 
  position = 'top', 
  variant = 'snackbar' 
}) => {
  const [open, setOpen] = useState(false);

  useEffect(() => {
    if (feedback) {
      setOpen(true);
    }
  }, [feedback]);

  const handleClose = () => {
    setOpen(false);
    setTimeout(() => onClose?.(), 300);
  };

  const getIcon = () => {
    if (!feedback) return null;
    
    switch (feedback.type) {
      case 'success':
        return <CheckCircle color="success" />;
      case 'error':
        return <Error color="error" />;
      case 'warning':
        return <Warning color="warning" />;
      case 'info':
        return <Info color="info" />;
      case 'loading':
        return <CircularProgress size={24} />;
      case 'progress':
        return <Schedule color="primary" />;
      default:
        return <Info />;
    }
  };

  const getColor = () => {
    if (!feedback) return 'info';
    
    switch (feedback.type) {
      case 'success':
        return 'success';
      case 'error':
        return 'error';
      case 'warning':
        return 'warning';
      case 'loading':
      case 'progress':
        return 'info';
      default:
        return 'info';
    }
  };

  if (!feedback) return null;

  // Snackbar Variant
  if (variant === 'snackbar') {
    return (
      <Snackbar
        open={open}
        autoHideDuration={feedback.persistent ? null : (feedback.duration || 6000)}
        onClose={feedback.persistent ? undefined : handleClose}
        anchorOrigin={{
          vertical: position === 'bottom' ? 'bottom' : 'top',
          horizontal: 'center'
        }}
        TransitionComponent={SlideTransition}
      >
        <Alert
          severity={getColor()}
          onClose={feedback.persistent ? undefined : handleClose}
          icon={getIcon()}
          sx={{
            minWidth: 300,
            maxWidth: 600,
            '& .MuiAlert-message': {
              width: '100%'
            }
          }}
        >
          {feedback.title && <AlertTitle>{feedback.title}</AlertTitle>}
          <Typography variant="body2">
            {feedback.message}
          </Typography>
          
          {feedback.type === 'progress' && feedback.progress !== undefined && (
            <Box sx={{ mt: 1 }}>
              <LinearProgress 
                variant="determinate" 
                value={feedback.progress}
                sx={{ borderRadius: 1 }}
              />
              <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
                {Math.round(feedback.progress)}% concluído
              </Typography>
            </Box>
          )}
          
          {feedback.details && feedback.details.length > 0 && (
            <Box sx={{ mt: 1 }}>
              {feedback.details.map((detail, index) => (
                <Typography key={index} variant="caption" display="block" color="text.secondary">
                  • {detail}
                </Typography>
              ))}
            </Box>
          )}
          
          {feedback.actions && (
            <Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              {feedback.actions.map((action, index) => (
                <Chip
                  key={index}
                  label={action.label}
                  onClick={action.action}
                  color={action.color || 'primary'}
                  variant="outlined"
                  size="small"
                  clickable
                />
              ))}
            </Box>
          )}
        </Alert>
      </Snackbar>
    );
  }

  // Card Variant
  if (variant === 'card') {
    return (
      <Fade in={open} timeout={300}>
        <Card sx={{ 
          mb: 2,
          border: `1px solid`,
          borderColor: `${getColor()}.main`,
          backgroundColor: `${getColor()}.50`
        }}>
          <CardContent sx={{ 
            display: 'flex', 
            alignItems: 'flex-start',
            gap: 2,
            '&:last-child': { pb: 2 }
          }}>
            <Box sx={{ mt: 0.5 }}>
              {getIcon()}
            </Box>
            
            <Box sx={{ flex: 1, minWidth: 0 }}>
              {feedback.title && (
                <Typography variant="h6" gutterBottom>
                  {feedback.title}
                </Typography>
              )}
              
              <Typography variant="body2" color="text.secondary">
                {feedback.message}
              </Typography>
              
              {feedback.type === 'progress' && feedback.progress !== undefined && (
                <Box sx={{ mt: 2 }}>
                  <LinearProgress 
                    variant="determinate" 
                    value={feedback.progress}
                    sx={{ borderRadius: 1 }}
                  />
                  <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
                    {Math.round(feedback.progress)}% concluído
                  </Typography>
                </Box>
              )}
              
              {feedback.details && feedback.details.length > 0 && (
                <Box sx={{ mt: 1 }}>
                  {feedback.details.map((detail, index) => (
                    <Typography key={index} variant="caption" display="block" color="text.secondary">
                      • {detail}
                    </Typography>
                  ))}
                </Box>
              )}
              
              {feedback.actions && (
                <Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  {feedback.actions.map((action, index) => (
                    <Chip
                      key={index}
                      label={action.label}
                      onClick={action.action}
                      color={action.color || 'primary'}
                      variant="outlined"
                      size="small"
                      clickable
                    />
                  ))}
                </Box>
              )}
            </Box>
            
            {!feedback.persistent && (
              <IconButton
                size="small"
                onClick={handleClose}
                sx={{ mt: -0.5, mr: -0.5 }}
              >
                <Close fontSize="small" />
              </IconButton>
            )}
          </CardContent>
        </Card>
      </Fade>
    );
  }

  // Overlay Variant
  if (variant === 'overlay') {
    return (
      <Backdrop
        open={open}
        sx={{ 
          zIndex: (theme) => theme.zIndex.modal + 1,
          backgroundColor: 'rgba(0, 0, 0, 0.7)'
        }}
      >
        <Zoom in={open} timeout={300}>
          <Card sx={{ 
            minWidth: 300,
            maxWidth: 500,
            m: 2
          }}>
            <CardContent sx={{ 
              textAlign: 'center',
              p: 4
            }}>
              <Box sx={{ mb: 2 }}>
                {getIcon()}
              </Box>
              
              {feedback.title && (
                <Typography variant="h5" gutterBottom>
                  {feedback.title}
                </Typography>
              )}
              
              <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                {feedback.message}
              </Typography>
              
              {feedback.type === 'progress' && feedback.progress !== undefined && (
                <Box sx={{ mb: 2 }}>
                  <LinearProgress 
                    variant="determinate" 
                    value={feedback.progress}
                    sx={{ borderRadius: 1, height: 8 }}
                  />
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    {Math.round(feedback.progress)}% concluído
                  </Typography>
                </Box>
              )}
              
              {feedback.details && feedback.details.length > 0 && (
                <Box sx={{ mb: 2, textAlign: 'left' }}>
                  {feedback.details.map((detail, index) => (
                    <Typography key={index} variant="body2" color="text.secondary">
                      • {detail}
                    </Typography>
                  ))}
                </Box>
              )}
              
              {feedback.actions && (
                <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center', flexWrap: 'wrap' }}>
                  {feedback.actions.map((action, index) => (
                    <Chip
                      key={index}
                      label={action.label}
                      onClick={action.action}
                      color={action.color || 'primary'}
                      variant="filled"
                      clickable
                    />
                  ))}
                </Box>
              )}
            </CardContent>
          </Card>
        </Zoom>
      </Backdrop>
    );
  }

  // Inline Variant (default)
  return (
    <Fade in={open} timeout={300}>
      <Alert
        severity={getColor()}
        onClose={feedback.persistent ? undefined : handleClose}
        icon={getIcon()}
        sx={{ mb: 2 }}
      >
        {feedback.title && <AlertTitle>{feedback.title}</AlertTitle>}
        <Typography variant="body2">
          {feedback.message}
        </Typography>
        
        {feedback.type === 'progress' && feedback.progress !== undefined && (
          <Box sx={{ mt: 1 }}>
            <LinearProgress 
              variant="determinate" 
              value={feedback.progress}
              sx={{ borderRadius: 1 }}
            />
            <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
              {Math.round(feedback.progress)}% concluído
            </Typography>
          </Box>
        )}
        
        {feedback.details && feedback.details.length > 0 && (
          <Box sx={{ mt: 1 }}>
            {feedback.details.map((detail, index) => (
              <Typography key={index} variant="caption" display="block" color="text.secondary">
                • {detail}
              </Typography>
            ))}
          </Box>
        )}
        
        {feedback.actions && (
          <Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            {feedback.actions.map((action, index) => (
              <Chip
                key={index}
                label={action.label}
                onClick={action.action}
                color={action.color || 'primary'}
                variant="outlined"
                size="small"
                clickable
              />
            ))}
          </Box>
        )}
      </Alert>
    </Fade>
  );
};

export default FeedbackSystem;
