import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Configuração para build de produção otimizado
  output: 'standalone',
  
  // Configurações de imagem
  images: {
    domains: ['lh3.googleusercontent.com', 'maps.googleapis.com'],
    unoptimized: true, // Para deploy Docker
  },
  
  // Headers de segurança
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
        ],
      },
    ];
  },
  
  // Configurações de ambiente
  env: {
    NEXT_TELEMETRY_DISABLED: '1',
  },
  
  // External packages for server components
  serverExternalPackages: ['googleapis', 'google-auth-library'],
  
  // TypeScript config
  typescript: {
    ignoreBuildErrors: false,
  },
  
  // ESLint config
  eslint: {
    ignoreDuringBuilds: false,
  },
};

export default nextConfig;
