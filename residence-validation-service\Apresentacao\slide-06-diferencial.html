<!DOCTYPE html>
<html lang="pt-br">
<head>
  <meta charset="UTF-8">
  <title>ProvaSegura - Slide 6: Diferencial Competitivo</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');
    
    body {
      margin: 0;
      padding: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      font-family: 'Inter', 'Segoe UI', Arial, sans-serif;
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }
      .slide {
      width: 90vw;
      max-width: 1280px;
      height: 80vh;
      max-height: 720px;
      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
      box-shadow: 0 20px 60px rgba(0,0,0,0.15), 0 8px 32px rgba(0,0,0,0.08);
      border-radius: 24px;
      display: flex;
      flex-direction: row;
      overflow: hidden;
      position: relative;
      border: 1px solid rgba(255,255,255,0.2);
    }
    
    .slide::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
      border-radius: 24px 24px 0 0;
    }
      .slide-content {
      padding: 40px 60px;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      position: relative;
      z-index: 2;
    }
      .slide-title {
      font-size: 2.5rem;
      font-weight: 800;
      background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-bottom: 30px;
      line-height: 1.1;
      letter-spacing: -0.02em;
    }
    
    .slide-text {
      font-size: 1.2rem;
      color: #334155;
      line-height: 1.7;
      font-weight: 400;
    }
    
    .slide-text ul {
      padding-left: 0;
      list-style: none;
    }
    
    .slide-text li {
      position: relative;
      padding: 20px 20px 20px 60px;
      margin-bottom: 16px;
      background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
      border-left: 4px solid #0ea5e9;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(14, 165, 233, 0.1);
    }
    
    .slide-text li::before {
      content: '⚡';
      position: absolute;
      left: 20px;
      top: 20px;
      font-size: 1.8rem;
    }
    
    .slide-text li:nth-child(2)::before { content: '🛡️'; }
    .slide-text li:nth-child(3)::before { content: '🚀'; }
    .slide-text li:nth-child(4)::before { content: '📋'; }
    .slide-text li:nth-child(5)::before { content: '🎯'; }
    
    .slide-text strong {
      color: #0c4a6e;
      font-weight: 700;
    }
    
    .slide-img {
      flex: 0 0 400px;
      background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      overflow: hidden;
      order: -1;
    }
    
    .slide-img::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 70% 30%, rgba(102, 126, 234, 0.1) 0%, transparent 50%);
    }
    
    .slide-img svg {
      filter: drop-shadow(0 15px 35px rgba(102, 126, 234, 0.2));
      z-index: 1;
      position: relative;
    }
    
    .slide-number {
      position: absolute;
      top: 30px;
      right: 40px;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-weight: 700;
      font-size: 1.3rem;
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }    @media print {
      body { background: #fff; }
      .slide { box-shadow: none; border: 1px solid #e2e8f0; }
    }

    @media (max-width: 768px) {
      .slide {
        width: 95vw;
        height: 90vh;
        flex-direction: column;
        padding: 30px 20px;
      }
      
      .slide-content {
        padding: 20px;
        order: 2;
      }
      
      .slide-title {
        font-size: 2rem;
        margin-bottom: 20px;
      }
      
      .slide-text {
        font-size: 1rem;
      }
      
      .slide-text li {
        padding: 15px 15px 15px 50px;
        margin-bottom: 12px;
      }
      
      .slide-text li::before {
        font-size: 1.5rem;
        left: 15px;
        top: 15px;
      }
      
      .slide-img {
        flex: 0 0 200px;
        order: 1;
      }
      
      .slide-img svg {
        width: 180px;
        height: 180px;
      }
    }

    .navigation {
      position: fixed;
      bottom: 30px;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      gap: 15px;
      z-index: 1000;
    }

    .nav-button {
      background: rgba(255, 255, 255, 0.9);
      border: none;
      padding: 12px 24px;
      border-radius: 25px;
      color: #1e293b;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      text-decoration: none;
      display: inline-block;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .nav-button:hover {
      background: white;
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }
  </style>
</head>
<body>

<div class="slide">
  <div class="slide-number">6</div>
  <div class="slide-content">
    <div class="slide-title">Nosso Diferencial Competitivo</div>
    <div class="slide-text">
      <ul>
        <li><strong>Tecnologia única:</strong> Combina biometria (100% segura) com Google Maps (líder em geolocalização).</li>
        <li><strong>Confiabilidade:</strong> Relatórios assinados digitalmente, aceitos por tribunais e bancos.</li>
        <li><strong>Rapidez:</strong> Substitui documentos físicos, reduzindo dias para minutos.</li>
        <li><strong>Conformidade:</strong> Atende à LGPD (Lei de Proteção de Dados) com criptografia e consentimento do usuário.</li>
        <li><strong>Baixa concorrência:</strong> Poucos sistemas oferecem validação biométrica + geolocalização.</li>
      </ul>
    </div>
  </div>
  <div class="slide-img">
    <!-- Selo de confiabilidade melhorado -->
    <svg width="320" height="320" viewBox="0 0 320 320">
      <!-- Círculo principal -->
      <circle cx="160" cy="160" r="140" fill="url(#gradient1)" opacity="0.1"/>
      <circle cx="160" cy="160" r="120" fill="#fff" stroke="url(#gradient1)" stroke-width="8"/>
      
      <!-- Selo central -->
      <circle cx="160" cy="160" r="80" fill="url(#gradient1)"/>
      
      <!-- Check mark grande -->
      <path d="M130 160 L150 180 L190 140" stroke="#fff" stroke-width="12" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
      
      <!-- Ícones de recursos ao redor -->
      <g transform="translate(80, 80)">
        <circle r="25" fill="#3b82f6"/>
        <text x="0" y="8" text-anchor="middle" font-size="24" fill="#fff">🔒</text>
      </g>
      
      <g transform="translate(240, 100)">
        <circle r="25" fill="#10b981"/>
        <text x="0" y="8" text-anchor="middle" font-size="24" fill="#fff">⚡</text>
      </g>
      
      <g transform="translate(260, 220)">
        <circle r="25" fill="#f59e0b"/>
        <text x="0" y="8" text-anchor="middle" font-size="24" fill="#fff">📍</text>
      </g>
      
      <g transform="translate(60, 240)">
        <circle r="25" fill="#8b5cf6"/>
        <text x="0" y="8" text-anchor="middle" font-size="24" fill="#fff">🛡️</text>
      </g>
      
      <!-- Conexões -->
      <line x1="105" y1="105" x2="135" y2="135" stroke="#cbd5e1" stroke-width="3" opacity="0.5"/>
      <line x1="215" y1="125" x2="185" y2="145" stroke="#cbd5e1" stroke-width="3" opacity="0.5"/>
      <line x1="235" y1="195" x2="185" y2="175" stroke="#cbd5e1" stroke-width="3" opacity="0.5"/>
      <line x1="85" y1="215" x2="135" y2="185" stroke="#cbd5e1" stroke-width="3" opacity="0.5"/>
      
      <!-- Texto "CONFIÁVEL" -->
      <text x="160" y="280" text-anchor="middle" font-size="20" font-weight="bold" fill="#1e293b">CONFIÁVEL</text>
      
      <!-- Estrelas de qualidade -->
      <text x="60" y="40" font-size="20" fill="#f59e0b">⭐</text>
      <text x="260" y="50" font-size="20" fill="#f59e0b">⭐</text>
      <text x="280" y="280" font-size="20" fill="#f59e0b">⭐</text>
      <text x="40" y="290" font-size="20" fill="#f59e0b">⭐</text>
      
      <!-- Definições de gradiente -->
      <defs>
        <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
        </linearGradient>
      </defs>
    </svg>  </div>
</div>

<div class="navigation">
  <a href="slide-05-aplicacoes.html" class="nav-button">← Anterior</a>
  <a href="index.html" class="nav-button">Índice</a>
  <a href="slide-07-modelo.html" class="nav-button">Próximo →</a>
</div>

<script>
document.addEventListener('keydown', function(event) {
  if (event.key === 'ArrowLeft') {
    window.location.href = 'slide-05-aplicacoes.html';
  } else if (event.key === 'ArrowRight') {
    window.location.href = 'slide-07-modelo.html';
  } else if (event.key === 'Escape') {
    window.location.href = 'index.html';
  }
});
</script>

</body>
</html>
