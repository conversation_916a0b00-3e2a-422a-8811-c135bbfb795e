#!/usr/bin/env pwsh

# Script para verificar problemas no sistema CartorioTech

Write-Host "🔍 Verificando sistema CartorioTech..." -ForegroundColor Green

# 1. Verificar se os containers estão rodando
Write-Host "📦 Status dos containers:" -ForegroundColor Cyan
$containers = docker-compose ps --format "table {{.Name}}\t{{.State}}\t{{.Ports}}"
Write-Host $containers

# 2. Verificar logs de erro no backend
Write-Host "`n🔍 Verificando erros no backend..." -ForegroundColor Cyan
$backendLogs = docker-compose logs backend --tail=50 2>&1
$backendErrors = $backendLogs | Select-String -Pattern "Error|TypeError|Cannot|Failed|Exception"

if ($backendErrors) {
    Write-Host "❌ Erros encontrados no backend:" -ForegroundColor Red
    $backendErrors | ForEach-Object { Write-Host "   $_" -ForegroundColor Red }
} else {
    Write-Host "✅ Nenhum erro crítico no backend" -ForegroundColor Green
}

# 3. Verificar logs de erro no frontend
Write-Host "`n🔍 Verificando erros no frontend..." -ForegroundColor Cyan
$webappLogs = docker-compose logs web-app --tail=30 2>&1
$webappErrors = $webappLogs | Select-String -Pattern "Error|Failed|Cannot|Exception"

if ($webappErrors) {
    Write-Host "❌ Erros encontrados no frontend:" -ForegroundColor Red
    $webappErrors | ForEach-Object { Write-Host "   $_" -ForegroundColor Red }
} else {
    Write-Host "✅ Nenhum erro crítico no frontend" -ForegroundColor Green
}

# 4. Testar conectividade dos serviços
Write-Host "`n🌐 Testando conectividade..." -ForegroundColor Cyan

# Backend Health
try {
    $healthResponse = Invoke-RestMethod -Uri "http://localhost:3001/health" -Method GET -TimeoutSec 5
    Write-Host "✅ Backend health: OK" -ForegroundColor Green
} catch {
    Write-Host "❌ Backend health: FALHOU - $($_.Exception.Message)" -ForegroundColor Red
}

# Analytics
try {
    $analyticsResponse = Invoke-RestMethod -Uri "http://localhost:3001/api/analytics/dashboard" -Method GET -TimeoutSec 5
    if ($analyticsResponse.success) {
        Write-Host "✅ Analytics: OK" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Analytics: Usando fallback" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Analytics: FALHOU - $($_.Exception.Message)" -ForegroundColor Red
}

# Frontend
try {
    $frontendResponse = Invoke-WebRequest -Uri "http://localhost:3000" -Method GET -TimeoutSec 5
    if ($frontendResponse.StatusCode -eq 200) {
        Write-Host "✅ Frontend: OK" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Frontend: FALHOU - $($_.Exception.Message)" -ForegroundColor Red
}

# 5. Verificar uso de recursos
Write-Host "`n📊 Uso de recursos:" -ForegroundColor Cyan
$stats = docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}"
Write-Host $stats

# 6. Verificar portas em uso
Write-Host "`n🔌 Portas em uso:" -ForegroundColor Cyan
$ports = @(3000, 3001, 3002, 5432, 6379)
foreach ($port in $ports) {
    $connection = Test-NetConnection -ComputerName localhost -Port $port -WarningAction SilentlyContinue
    if ($connection.TcpTestSucceeded) {
        Write-Host "✅ Porta $port : ABERTA" -ForegroundColor Green
    } else {
        Write-Host "❌ Porta $port : FECHADA" -ForegroundColor Red
    }
}

# 7. Verificar espaço em disco
Write-Host "`n💾 Espaço em disco:" -ForegroundColor Cyan
$disk = Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='C:'"
$freeSpaceGB = [math]::Round($disk.FreeSpace / 1GB, 2)
$totalSpaceGB = [math]::Round($disk.Size / 1GB, 2)
$usedPercentage = [math]::Round((($disk.Size - $disk.FreeSpace) / $disk.Size) * 100, 2)

Write-Host "   Livre: $freeSpaceGB GB" -ForegroundColor White
Write-Host "   Total: $totalSpaceGB GB" -ForegroundColor White
Write-Host "   Usado: $usedPercentage%" -ForegroundColor $(if ($usedPercentage -gt 90) { "Red" } elseif ($usedPercentage -gt 80) { "Yellow" } else { "Green" })

# 8. Resumo e recomendações
Write-Host "`n📋 RESUMO:" -ForegroundColor Green

$issues = @()

# Verificar se há containers parados
$stoppedContainers = docker-compose ps --filter "status=exited"
if ($stoppedContainers) {
    $issues += "Containers parados detectados"
}

# Verificar se há erros críticos
if ($backendErrors) {
    $issues += "Erros no backend"
}

if ($webappErrors) {
    $issues += "Erros no frontend"
}

# Verificar uso de disco
if ($usedPercentage -gt 90) {
    $issues += "Espaço em disco baixo ($usedPercentage% usado)"
}

if ($issues.Count -eq 0) {
    Write-Host "✅ Sistema funcionando normalmente!" -ForegroundColor Green
} else {
    Write-Host "⚠️ Problemas detectados:" -ForegroundColor Yellow
    $issues | ForEach-Object { Write-Host "   - $_" -ForegroundColor Red }
    
    Write-Host "`n🔧 RECOMENDAÇÕES:" -ForegroundColor Cyan
    Write-Host "   1. Execute: .\rebuild-system.ps1" -ForegroundColor White
    Write-Host "   2. Verifique logs: docker-compose logs [service]" -ForegroundColor White
    Write-Host "   3. Reinicie serviços: docker-compose restart" -ForegroundColor White
}

Write-Host "`n🔗 Links úteis:" -ForegroundColor Cyan
Write-Host "   Frontend: http://localhost:3000" -ForegroundColor White
Write-Host "   Backend: http://localhost:3001" -ForegroundColor White
Write-Host "   API Docs: http://localhost:3001/api-docs" -ForegroundColor White
