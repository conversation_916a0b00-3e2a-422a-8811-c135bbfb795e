-- Inicialização do banco PostgreSQL para Sistema de Cartório
-- Com suporte a pgvector para embeddings de IA

-- Criar extensões necessárias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "vector";

-- Configurar timezone
SET timezone = 'America/Sao_Paulo';

-- <PERSON>bela de usuários com RBAC
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    role VARCHAR(20) NOT NULL DEFAULT 'visualizador' CHECK (role IN ('admin', 'operador', 'visualizador', 'auditor')),
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,

    -- Sistema de cotas de armazenamento
    storage_quota_mb BIGINT DEFAULT 50, -- Cota em MB (padrão: 50MB)
    storage_used_mb BIGINT DEFAULT 0,   -- Espaço usado em MB
    storage_plan VARCHAR(20) DEFAULT 'basic' CHECK (storage_plan IN ('basic', 'premium', 'enterprise')),

    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de sessões JWT
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL,
    refresh_token_hash VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    refresh_expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    ip_address INET,
    user_agent TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Tabela principal de gravações (migrada do SQLite)
CREATE TABLE recordings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    filename VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    duration INTEGER,
    hash VARCHAR(64) UNIQUE NOT NULL,
    user_id UUID REFERENCES users(id),
    user_name VARCHAR(255) NOT NULL,

    -- Campos LGPD
    consent_timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    consent_ip INET NOT NULL,
    consent_user_agent TEXT,
    consent_name VARCHAR(255) NOT NULL,
    consent_document VARCHAR(50) NOT NULL,
    consent_purpose TEXT NOT NULL,
    consent_given BOOLEAN DEFAULT true,

    -- Campos de integridade e assinatura digital
    file_hash VARCHAR(64),
    digital_signature TEXT,
    signature_path VARCHAR(500),
    certificate_data TEXT,
    signed_at TIMESTAMP WITH TIME ZONE,
    timestamp_token TEXT,
    timestamp_authority VARCHAR(255),
    timestamp_nonce VARCHAR(32),
    timestamp_at TIMESTAMP WITH TIME ZONE,
    integrity_verified BOOLEAN DEFAULT FALSE,
    last_verification_at TIMESTAMP WITH TIME ZONE,
    verification_status VARCHAR(20) DEFAULT 'PENDING',

    -- Campos de criptografia
    encrypted BOOLEAN DEFAULT FALSE,
    encrypted_path VARCHAR(500),
    encryption_key_id VARCHAR(255),
    encryption_iv VARCHAR(32),
    encryption_tag VARCHAR(32),
    encrypted_hash VARCHAR(64),
    encryption_algorithm VARCHAR(50),
    encrypted_at TIMESTAMP WITH TIME ZONE,
    key_rotated_at TIMESTAMP WITH TIME ZONE,
    last_encryption_verification TIMESTAMP WITH TIME ZONE,

    -- Metadados técnicos
    device_info JSONB,
    orientation VARCHAR(20),
    additional_data JSONB,

    -- Controle
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Tabelas de IA (migradas do SQLite para PostgreSQL)

-- Transcrições
CREATE TABLE ai_transcriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    recording_id UUID NOT NULL REFERENCES recordings(id) ON DELETE CASCADE,
    session_id VARCHAR(255) NOT NULL,
    text TEXT NOT NULL,
    language VARCHAR(10) DEFAULT 'pt',
    duration REAL,
    confidence REAL,
    word_count INTEGER,
    segments JSONB,
    processing_time INTEGER,
    model VARCHAR(50) DEFAULT 'whisper-1',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Embeddings vetoriais para busca semântica
CREATE TABLE ai_embeddings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    recording_id UUID NOT NULL REFERENCES recordings(id) ON DELETE CASCADE,
    embedding vector(1536), -- OpenAI text-embedding-3-small tem 1536 dimensões
    model VARCHAR(50) DEFAULT 'text-embedding-3-small',
    tokens INTEGER,
    keywords JSONB,
    suggested_tags JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Análises de compliance LGPD
CREATE TABLE ai_compliance_analyses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    recording_id UUID NOT NULL REFERENCES recordings(id) ON DELETE CASCADE,
    session_id VARCHAR(255) NOT NULL,
    analysis JSONB NOT NULL,
    compliance_score REAL,
    risk_level VARCHAR(20) CHECK (risk_level IN ('baixo', 'medio', 'alto', 'critico')),
    recommendations JSONB,
    processing_time INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Análises de qualidade
CREATE TABLE ai_quality_analyses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    recording_id UUID NOT NULL REFERENCES recordings(id) ON DELETE CASCADE,
    session_id VARCHAR(255) NOT NULL,
    analysis JSONB NOT NULL,
    overall_score REAL,
    quality_level VARCHAR(20) CHECK (quality_level IN ('excelente', 'boa', 'regular', 'baixa', 'muito_baixa')),
    audio_score REAL,
    transcription_score REAL,
    recommendations JSONB,
    processing_time INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Buscas semânticas (para analytics)
CREATE TABLE ai_semantic_searches (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id VARCHAR(255) NOT NULL,
    user_id UUID REFERENCES users(id),
    query TEXT NOT NULL,
    results_count INTEGER,
    processing_time INTEGER,
    filters JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Feedback de busca
CREATE TABLE ai_search_feedback (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id VARCHAR(255) NOT NULL,
    recording_id UUID NOT NULL REFERENCES recordings(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id),
    relevant BOOLEAN,
    feedback TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Jobs de IA processados
CREATE TABLE ai_jobs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    job_id VARCHAR(255) NOT NULL UNIQUE,
    job_type VARCHAR(50) NOT NULL,
    recording_id UUID NOT NULL REFERENCES recordings(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    result JSONB,
    error_message TEXT,
    processing_time INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Logs de auditoria
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Tabela para controlar gravações salvas pelos usuários
CREATE TABLE saved_recordings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    recording_id UUID NOT NULL REFERENCES recordings(id) ON DELETE CASCADE,
    saved_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    file_size_mb DECIMAL(10,2) NOT NULL,
    storage_path TEXT NOT NULL,
    is_permanent BOOLEAN DEFAULT true,
    notes TEXT,

    UNIQUE(user_id, recording_id) -- Evita duplicatas
);

-- Índices para performance

-- Índices básicos
CREATE INDEX idx_recordings_user_id ON recordings(user_id);
CREATE INDEX idx_recordings_created_at ON recordings(created_at);
CREATE INDEX idx_recordings_hash ON recordings(hash);
CREATE INDEX idx_recordings_consent_timestamp ON recordings(consent_timestamp);

-- Índices para integridade e assinatura digital
CREATE INDEX idx_recordings_encrypted_path ON recordings(encrypted_path) WHERE encrypted_path IS NOT NULL;
CREATE INDEX idx_recordings_file_hash ON recordings(file_hash) WHERE file_hash IS NOT NULL;
CREATE INDEX idx_recordings_encrypted_hash ON recordings(encrypted_hash) WHERE encrypted_hash IS NOT NULL;
CREATE INDEX idx_recordings_verification_status ON recordings(verification_status);
CREATE INDEX idx_recordings_last_verification ON recordings(last_verification_at);
CREATE INDEX idx_recordings_encrypted ON recordings(encrypted) WHERE encrypted = true;
CREATE INDEX idx_recordings_digital_signature ON recordings(digital_signature) WHERE digital_signature IS NOT NULL;
CREATE INDEX idx_recordings_signed_at ON recordings(signed_at) WHERE signed_at IS NOT NULL;

-- Índices para saved_recordings
CREATE INDEX idx_saved_recordings_user_id ON saved_recordings(user_id);
CREATE INDEX idx_saved_recordings_recording_id ON saved_recordings(recording_id);
CREATE INDEX idx_saved_recordings_saved_at ON saved_recordings(saved_at);

-- Índices de IA
CREATE INDEX idx_ai_transcriptions_recording_id ON ai_transcriptions(recording_id);
CREATE INDEX idx_ai_embeddings_recording_id ON ai_embeddings(recording_id);
CREATE INDEX idx_ai_compliance_recording_id ON ai_compliance_analyses(recording_id);
CREATE INDEX idx_ai_quality_recording_id ON ai_quality_analyses(recording_id);

-- Índice vetorial para busca semântica (HNSW é mais eficiente para alta dimensionalidade)
CREATE INDEX idx_ai_embeddings_vector ON ai_embeddings USING hnsw (embedding vector_cosine_ops);

-- Índices de sessões e autenticação
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_expires_at ON user_sessions(expires_at);
CREATE INDEX idx_user_sessions_token_hash ON user_sessions(token_hash);

-- Índices de auditoria
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);

-- Índices de busca semântica
CREATE INDEX idx_ai_searches_user_id ON ai_semantic_searches(user_id);
CREATE INDEX idx_ai_searches_created_at ON ai_semantic_searches(created_at);

-- Triggers para updated_at automático
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_recordings_updated_at BEFORE UPDATE ON recordings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_ai_transcriptions_updated_at BEFORE UPDATE ON ai_transcriptions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_ai_embeddings_updated_at BEFORE UPDATE ON ai_embeddings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_ai_compliance_updated_at BEFORE UPDATE ON ai_compliance_analyses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_ai_quality_updated_at BEFORE UPDATE ON ai_quality_analyses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Inserir usuário admin padrão
INSERT INTO users (username, email, password_hash, full_name, role) VALUES 
('admin', '<EMAIL>', crypt('admin123', gen_salt('bf')), 'Administrador do Sistema', 'admin');

-- Comentários nas tabelas
COMMENT ON TABLE users IS 'Usuários do sistema com controle de acesso baseado em roles';
COMMENT ON TABLE recordings IS 'Gravações principais do sistema de cartório com suporte completo a criptografia e assinatura digital';
COMMENT ON TABLE ai_embeddings IS 'Embeddings vetoriais para busca semântica com pgvector';
COMMENT ON TABLE ai_compliance_analyses IS 'Análises automáticas de compliance LGPD';
COMMENT ON TABLE audit_logs IS 'Logs de auditoria para rastreabilidade completa';

-- Comentários nas colunas de integridade
COMMENT ON COLUMN recordings.file_hash IS 'Hash SHA-256 do arquivo original para verificação de integridade';
COMMENT ON COLUMN recordings.encrypted_path IS 'Caminho do arquivo criptografado quando aplicável';
COMMENT ON COLUMN recordings.encrypted_hash IS 'Hash SHA-256 do arquivo criptografado';
COMMENT ON COLUMN recordings.digital_signature IS 'Assinatura digital do arquivo em formato base64';
COMMENT ON COLUMN recordings.verification_status IS 'Status da verificação de integridade: PENDING, VERIFIED, FAILED, CORRUPTED';
COMMENT ON COLUMN recordings.encrypted IS 'Indica se o arquivo está criptografado';
COMMENT ON COLUMN recordings.encryption_algorithm IS 'Algoritmo de criptografia utilizado (AES-256-GCM, etc.)';

-- Configurações de performance
-- ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements,auto_explain'; -- Removido: extensões não disponíveis no pgvector
ALTER SYSTEM SET max_connections = 200;
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;

-- Função para atualizar uso de armazenamento do usuário
CREATE OR REPLACE FUNCTION update_user_storage_usage(user_uuid UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE users
    SET storage_used_mb = (
        SELECT COALESCE(SUM(file_size_mb), 0)
        FROM saved_recordings
        WHERE user_id = user_uuid AND is_permanent = true
    )
    WHERE id = user_uuid;
END;
$$ LANGUAGE plpgsql;

-- Trigger para atualizar automaticamente o uso de armazenamento
CREATE OR REPLACE FUNCTION trigger_update_storage_usage()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        PERFORM update_user_storage_usage(NEW.user_id);
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        PERFORM update_user_storage_usage(NEW.user_id);
        IF OLD.user_id != NEW.user_id THEN
            PERFORM update_user_storage_usage(OLD.user_id);
        END IF;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        PERFORM update_user_storage_usage(OLD.user_id);
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Aplicar trigger na tabela saved_recordings
CREATE TRIGGER trigger_saved_recordings_storage_update
    AFTER INSERT OR UPDATE OR DELETE ON saved_recordings
    FOR EACH ROW EXECUTE FUNCTION trigger_update_storage_usage();

-- Inserir usuário administrador padrão com cota enterprise
INSERT INTO users (username, email, password_hash, full_name, role, storage_quota_mb, storage_plan)
VALUES ('admin', '<EMAIL>', crypt('admin123', gen_salt('bf')), 'Administrador do Sistema', 'admin', 1000, 'enterprise');
