const express = require('express');
const IntegrityMonitorService = require('../services/integrityMonitorService');
const { Pool } = require('pg');
const router = express.Router();

// Pool de conexões PostgreSQL
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'cartorio_db',
  user: process.env.DB_USER || 'cartorio_user',
  password: process.env.DB_PASSWORD || 'cartorio_secure_pass',
});

// Instância única do monitor
let integrityMonitor = null;

// Inicializar monitor
function getIntegrityMonitor() {
  if (!integrityMonitor) {
    integrityMonitor = new IntegrityMonitorService();
  }
  return integrityMonitor;
}

/**
 * @swagger
 * /api/integrity-monitor/status:
 *   get:
 *     summary: Obter status do monitor de integridade
 *     tags: [Integrity Monitor]
 *     responses:
 *       200:
 *         description: Status do monitor
 *       500:
 *         description: Erro interno do servidor
 */
router.get('/status', async (req, res) => {
  try {
    const monitor = getIntegrityMonitor();
    const stats = monitor.getMonitoringStats();
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('[IntegrityMonitor] Erro ao obter status:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao obter status do monitor',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/integrity-monitor/start:
 *   post:
 *     summary: Iniciar monitoramento de integridade
 *     tags: [Integrity Monitor]
 *     responses:
 *       200:
 *         description: Monitor iniciado com sucesso
 *       500:
 *         description: Erro interno do servidor
 */
router.post('/start', async (req, res) => {
  try {
    const monitor = getIntegrityMonitor();
    monitor.start();
    
    res.json({
      success: true,
      message: 'Monitor de integridade iniciado com sucesso'
    });
  } catch (error) {
    console.error('[IntegrityMonitor] Erro ao iniciar monitor:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao iniciar monitor de integridade',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/integrity-monitor/stop:
 *   post:
 *     summary: Parar monitoramento de integridade
 *     tags: [Integrity Monitor]
 *     responses:
 *       200:
 *         description: Monitor parado com sucesso
 *       500:
 *         description: Erro interno do servidor
 */
router.post('/stop', async (req, res) => {
  try {
    const monitor = getIntegrityMonitor();
    monitor.stop();
    
    res.json({
      success: true,
      message: 'Monitor de integridade parado com sucesso'
    });
  } catch (error) {
    console.error('[IntegrityMonitor] Erro ao parar monitor:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao parar monitor de integridade',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/integrity-monitor/check/full:
 *   post:
 *     summary: Executar verificação completa de integridade
 *     tags: [Integrity Monitor]
 *     responses:
 *       200:
 *         description: Verificação iniciada
 *       409:
 *         description: Verificação já em andamento
 *       500:
 *         description: Erro interno do servidor
 */
router.post('/check/full', async (req, res) => {
  try {
    const monitor = getIntegrityMonitor();
    
    if (monitor.isRunning) {
      return res.status(409).json({
        success: false,
        error: 'Verificação já em andamento'
      });
    }
    
    // Executar verificação em background
    setImmediate(() => {
      monitor.runFullIntegrityCheck();
    });
    
    res.json({
      success: true,
      message: 'Verificação completa de integridade iniciada'
    });
  } catch (error) {
    console.error('[IntegrityMonitor] Erro ao iniciar verificação completa:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao iniciar verificação completa',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/integrity-monitor/check/quick:
 *   post:
 *     summary: Executar verificação rápida de integridade
 *     tags: [Integrity Monitor]
 *     responses:
 *       200:
 *         description: Verificação rápida concluída
 *       500:
 *         description: Erro interno do servidor
 */
router.post('/check/quick', async (req, res) => {
  try {
    const monitor = getIntegrityMonitor();
    
    // Executar verificação rápida
    await monitor.runQuickIntegrityCheck();
    
    res.json({
      success: true,
      message: 'Verificação rápida de integridade concluída'
    });
  } catch (error) {
    console.error('[IntegrityMonitor] Erro na verificação rápida:', error);
    res.status(500).json({
      success: false,
      error: 'Erro na verificação rápida',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/integrity-monitor/check/recording/{recordingId}:
 *   post:
 *     summary: Verificar integridade de gravação específica
 *     tags: [Integrity Monitor]
 *     parameters:
 *       - in: path
 *         name: recordingId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID da gravação
 *     responses:
 *       200:
 *         description: Verificação concluída
 *       404:
 *         description: Gravação não encontrada
 *       500:
 *         description: Erro interno do servidor
 */
router.post('/check/recording/:recordingId', async (req, res) => {
  const { recordingId } = req.params;
  
  try {
    // Buscar gravação no banco
    const recordingQuery = 'SELECT * FROM recordings WHERE id = $1';
    const recordingResult = await pool.query(recordingQuery, [recordingId]);
    
    if (recordingResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Gravação não encontrada'
      });
    }
    
    const recording = recordingResult.rows[0];
    const monitor = getIntegrityMonitor();
    
    // Verificar integridade
    const checkResult = await monitor.checkRecordingIntegrity(recording);
    
    res.json({
      success: true,
      message: 'Verificação de integridade concluída',
      data: {
        recordingId,
        result: checkResult
      }
    });
  } catch (error) {
    console.error('[IntegrityMonitor] Erro na verificação individual:', error);
    res.status(500).json({
      success: false,
      error: 'Erro na verificação de integridade',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/integrity-monitor/results:
 *   get:
 *     summary: Obter resultados de verificações anteriores
 *     tags: [Integrity Monitor]
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Número máximo de resultados
 *       - in: query
 *         name: checkType
 *         schema:
 *           type: string
 *           enum: [FULL, QUICK, CRITICAL]
 *         description: Filtrar por tipo de verificação
 *     responses:
 *       200:
 *         description: Resultados das verificações
 *       500:
 *         description: Erro interno do servidor
 */
router.get('/results', async (req, res) => {
  try {
    const { limit = 50, checkType } = req.query;
    
    let query = 'SELECT * FROM integrity_check_results WHERE 1=1';
    const params = [];
    let paramIndex = 1;
    
    if (checkType) {
      query += ` AND check_type = $${paramIndex}`;
      params.push(checkType);
      paramIndex++;
    }
    
    query += ' ORDER BY created_at DESC';
    
    if (limit) {
      query += ` LIMIT $${paramIndex}`;
      params.push(parseInt(limit));
    }
    
    const result = await pool.query(query, params);
    
    res.json({
      success: true,
      data: {
        results: result.rows,
        total: result.rows.length,
        filters: { limit, checkType },
        retrievedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('[IntegrityMonitor] Erro ao obter resultados:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao obter resultados das verificações',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/integrity-monitor/violations:
 *   get:
 *     summary: Obter violações de integridade
 *     tags: [Integrity Monitor]
 *     parameters:
 *       - in: query
 *         name: severity
 *         schema:
 *           type: string
 *           enum: [LOW, MEDIUM, HIGH, CRITICAL]
 *         description: Filtrar por severidade
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 100
 *         description: Número máximo de resultados
 *     responses:
 *       200:
 *         description: Violações de integridade
 *       500:
 *         description: Erro interno do servidor
 */
router.get('/violations', async (req, res) => {
  try {
    const { severity, limit = 100 } = req.query;
    
    // Buscar gravações com problemas de integridade
    let query = `
      SELECT r.id, r.filename, r.verification_status, r.last_verification_at,
             al.details as violation_details, al.severity, al.timestamp as violation_time
      FROM recordings r
      LEFT JOIN audit_logs al ON r.id = al.recording_id 
        AND al.action LIKE '%INTEGRITY%' 
        AND al.severity IN ('WARNING', 'ERROR', 'CRITICAL')
      WHERE r.verification_status = 'INVALID'
    `;
    
    const params = [];
    let paramIndex = 1;
    
    if (severity) {
      query += ` AND al.severity = $${paramIndex}`;
      params.push(severity);
      paramIndex++;
    }
    
    query += ' ORDER BY r.last_verification_at DESC';
    
    if (limit) {
      query += ` LIMIT $${paramIndex}`;
      params.push(parseInt(limit));
    }
    
    const result = await pool.query(query, params);
    
    res.json({
      success: true,
      data: {
        violations: result.rows,
        total: result.rows.length,
        filters: { severity, limit },
        retrievedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('[IntegrityMonitor] Erro ao obter violações:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao obter violações de integridade',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/integrity-monitor/statistics:
 *   get:
 *     summary: Obter estatísticas de integridade
 *     tags: [Integrity Monitor]
 *     responses:
 *       200:
 *         description: Estatísticas de integridade
 *       500:
 *         description: Erro interno do servidor
 */
router.get('/statistics', async (req, res) => {
  try {
    // Estatísticas gerais
    const generalStats = await pool.query(`
      SELECT 
        COUNT(*) as total_recordings,
        COUNT(CASE WHEN verification_status = 'VALID' THEN 1 END) as valid_recordings,
        COUNT(CASE WHEN verification_status = 'INVALID' THEN 1 END) as invalid_recordings,
        COUNT(CASE WHEN verification_status = 'PENDING' THEN 1 END) as pending_recordings,
        COUNT(CASE WHEN last_verification_at > NOW() - INTERVAL '24 hours' THEN 1 END) as checked_last_24h
      FROM recordings
    `);
    
    // Estatísticas de verificações
    const checkStats = await pool.query(`
      SELECT 
        check_type,
        COUNT(*) as total_checks,
        AVG(violations_found) as avg_violations,
        AVG(processing_time_ms) as avg_processing_time,
        MAX(created_at) as last_check
      FROM integrity_check_results
      WHERE created_at > NOW() - INTERVAL '30 days'
      GROUP BY check_type
    `);
    
    // Tendências de violações
    const violationTrends = await pool.query(`
      SELECT 
        DATE(created_at) as check_date,
        SUM(violations_found) as total_violations,
        AVG(violations_found::float / NULLIF(checked_files, 0)) as violation_rate
      FROM integrity_check_results
      WHERE created_at > NOW() - INTERVAL '7 days'
      GROUP BY DATE(created_at)
      ORDER BY check_date DESC
    `);
    
    const monitor = getIntegrityMonitor();
    const monitorStats = monitor.getMonitoringStats();
    
    res.json({
      success: true,
      data: {
        general: generalStats.rows[0],
        checks: checkStats.rows,
        trends: violationTrends.rows,
        monitor: monitorStats,
        generatedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('[IntegrityMonitor] Erro ao obter estatísticas:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao obter estatísticas de integridade',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Inicializar monitor automaticamente
getIntegrityMonitor();

module.exports = router;
