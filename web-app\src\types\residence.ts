// Tipos para dados de localização do Google Maps Timeline
export interface LocationData {
  timestamp: string;
  latitude: number;
  longitude: number;
  accuracy: number;
  address?: string;
  source: 'GPS' | 'NETWORK' | 'PASSIVE';
}

// Tipos para dados de atividade
export interface ActivityData {
  startTime: string;
  endTime: string;
  type: 'STILL' | 'WALKING' | 'DRIVING' | 'IN_VEHICLE' | 'ON_BICYCLE' | 'UNKNOWN';
  confidence: number;
  location: LocationData;
}

// Tipos para validação de residência
export interface ResidenceValidation {
  id: string;
  userId: string;
  address: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  validationPeriod: {
    startDate: string;
    endDate: string;
  };
  nightlyPresence: NightlyPresenceRecord[];
  status: 'PENDING' | 'VALIDATING' | 'APPROVED' | 'REJECTED';
  validationScore: number;
  createdAt: string;
  updatedAt: string;
}

// Registro de presença noturna
export interface NightlyPresenceRecord {
  date: string;
  startTime: string; // 18:00
  endTime: string;   // 06:00
  isPresent: boolean;
  confidence: number;
  locationPoints: LocationData[];
  distanceFromHome: number; // em metros
}

// Dados do usuário
export interface User {
  id: string;
  email: string;
  name: string;
  googleId: string;
  phone?: string;
  document?: string; // CPF/RG
  createdAt: string;
  updatedAt: string;
}

// Configurações de validação
export interface ValidationConfig {
  minimumDays: number; // 15 dias
  nightStartTime: string; // "18:00"
  nightEndTime: string;   // "06:00"
  maxDistanceFromHome: number; // 100 metros
  minimumPresenceHours: number; // 8 horas
  confidenceThreshold: number; // 0.7
}

// Certificado de residência
export interface ResidenceCertificate {
  id: string;
  userId: string;
  validationId: string;
  certificateNumber: string;
  issueDate: string;
  validUntil: string;
  address: string;
  validationPeriod: {
    startDate: string;
    endDate: string;
  };
  validationScore: number;
  validDays: number;
  pdfUrl?: string;
  qrCode: string;
  status: 'ACTIVE' | 'EXPIRED' | 'REVOKED';
}

// Resposta da API do Google
export interface GoogleTimelineResponse {
  timelineObjects: TimelineObject[];
}

export interface TimelineObject {
  activitySegment?: ActivitySegment;
  placeVisit?: PlaceVisit;
}

export interface ActivitySegment {
  startLocation: Location;
  endLocation: Location;
  duration: {
    startTimestamp: string;
    endTimestamp: string;
  };
  distance: number;
  activityType: string;
  confidence: number;
}

export interface PlaceVisit {
  location: Location;
  duration: {
    startTimestamp: string;
    endTimestamp: string;
  };
  placeConfidence: number;
  visitConfidence: number;
  locationConfidence: number;
}

export interface Location {
  latitudeE7: number;
  longitudeE7: number;
  placeId?: string;
  name?: string;
  address?: string;
}

// Tipos para resultado da validação
export interface ValidationResult {
  isValid: boolean;
  score: number;
  totalDays: number;
  validDays: number;
  averageDistanceFromHome: number;
  nightlyPresence: NightlyPresenceRecord[];
  summary: {
    totalNights: number;
    presentNights: number;
    presencePercentage: number;
    averagePresenceHours: number;
  };
  recommendations?: string[];
  warnings?: string[];
}

// Tipos para configuração OAuth
export interface OAuthConfig {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
  scopes: string[];
}

// Tipos para token de autenticação
export interface AuthToken {
  accessToken: string;
  refreshToken: string;
  expiryDate: number;
  scope: string;
  tokenType: string;
}

// Tipos para dados de geolocalização
export interface GeolocationData {
  latitude: number;
  longitude: number;
  accuracy: number;
  timestamp: number;
  address?: string;
}

// Tipos para validação de endereço
export interface AddressValidation {
  isValid: boolean;
  formattedAddress: string;
  components: AddressComponents;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  confidence: number;
}

export interface AddressComponents {
  streetNumber?: string;
  street?: string;
  neighborhood?: string;
  city: string;
  state: string;
  country: string;
  postalCode?: string;
}

// Tipos para estatísticas
export interface ValidationStatistics {
  totalValidations: number;
  successfulValidations: number;
  failedValidations: number;
  averageValidationTime: number;
  averageScore: number;
  commonFailureReasons: string[];
}

// Tipos para relatório de validação
export interface ValidationReport {
  id: string;
  userId: string;
  validationId: string;
  generatedAt: string;
  reportData: {
    userInfo: User;
    validationDetails: ResidenceValidation;
    result: ValidationResult;
    locationAnalysis: LocationAnalysis;
    timelineData: LocationData[];
  };
}

export interface LocationAnalysis {
  homeLocation: {
    latitude: number;
    longitude: number;
    address: string;
  };
  frequentLocations: Array<{
    latitude: number;
    longitude: number;
    visits: number;
    address?: string;
  }>;
  travelPatterns: Array<{
    fromLocation: string;
    toLocation: string;
    frequency: number;
    averageTime: number;
  }>;
}

// Tipos para configuração do sistema
export interface SystemConfig {
  validation: ValidationConfig;
  oauth: OAuthConfig;
  certificate: {
    validityPeriod: number; // em dias
    signingKey: string;
    issuer: string;
  };
  api: {
    baseUrl: string;
    timeout: number;
    retries: number;
  };
}

// Tipos para resposta da API
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
}

// Tipos para paginação
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Tipos para filtros de busca
export interface SearchFilters {
  status?: ResidenceValidation['status'];
  startDate?: string;
  endDate?: string;
  minScore?: number;
  maxScore?: number;
  userId?: string;
}

// Todos os tipos são exportados como named exports
// Este default export está vazio pois interfaces não podem ser valores
export default {};
