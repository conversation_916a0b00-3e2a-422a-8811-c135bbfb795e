'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { ConsentModal } from '@/components/ConsentModal';
import { 
  AnimatedValidation, 
  AnimatedSecurity, 
  FadeInSection, 
  AnimatedButton,
  AnimatedCard 
} from '@/components/AnimatedIcons';

export default function HomePage() {
  const [error, setError] = useState<string | null>(null);
  const [showConsentModal, setShowConsentModal] = useState(false);

  useEffect(() => {
    // Verifica se há erro na URL (redirect do callback)
    const urlParams = new URLSearchParams(window.location.search);
    const errorParam = urlParams.get('error');
    
    if (errorParam) {
      const errorMessages: Record<string, string> = {
        'access_denied': 'Acesso negado pelo usuário',
        'invalid_user': 'Usuário inválido',
        'auth_failed': 'Falha na autenticação',
        'no_code': 'Código de autorização não recebido',
      };
      
      setError(errorMessages[errorParam] || 'Erro desconhecido na autenticação');
    }
  }, []);

  const handleGoogleAuth = async () => {
    try {
      const response = await fetch('/api/auth/google');
      const data = await response.json();
      
      if (data.success && data.authUrl) {
        window.location.href = data.authUrl;
      } else {
        setError('Erro ao iniciar autenticação');
      }
    } catch (err) {
      setError('Erro ao conectar com o servidor');
    }
  };

  const handleConsentAccept = () => {
    setShowConsentModal(false);
    handleGoogleAuth();
  };

  const handleStartAuth = () => {
    setShowConsentModal(true);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-900">
              Sistema de Validação de Residência
            </h1>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Comprove sua residência de forma <span className="text-blue-600">automática</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Utilize seus dados de <strong>histórico de localização</strong> (Timeline) do Google Maps para gerar 
            comprovantes de residência de forma segura e automatizada.
          </p>
          
          {/* Aviso Legal Principal */}
          <div className="bg-amber-50 border-l-4 border-amber-400 p-4 mt-6 max-w-4xl mx-auto">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-amber-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-amber-700">
                  <strong className="font-semibold">⚖️ IMPORTANTE - Validade Legal:</strong> Este comprovante <strong>só possui validade legal após autenticação em cartório</strong>. 
                  O documento gerado aqui é preliminar e serve para agilizar o processo cartorário.
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-6 max-w-2xl mx-auto">
            <div className="flex items-start">
              <svg className="w-5 h-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div className="text-sm text-blue-800">
                <p><strong>Importante:</strong> Usamos o <strong>Timeline</strong> (histórico de localização) do Google Maps, não o aplicativo comum.</p>
                <p className="mt-1">Certifique-se que o Timeline está ativado no seu celular para ter dados suficientes.</p>
              </div>
            </div>
          </div>
        </div>

        {/* Features */}
        <FadeInSection delay={200}>
          <div className="grid md:grid-cols-3 gap-8 mb-12">
            <AnimatedCard className="bg-white p-6 rounded-lg shadow-sm">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                <AnimatedValidation className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Validação Automática</h3>
              <p className="text-gray-600">
                Algoritmos inteligentes analisam sua permanência no local das 18h às 6h 
                por pelo menos 15 dias consecutivos.
              </p>
            </AnimatedCard>

            <AnimatedCard className="bg-white p-6 rounded-lg shadow-sm">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                <AnimatedSecurity className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Seguro e Privado</h3>
              <p className="text-gray-600">
                Seus dados são criptografados e processados seguindo rigorosamente 
                as diretrizes da LGPD.
              </p>
            </AnimatedCard>

            <AnimatedCard className="bg-white p-6 rounded-lg shadow-sm">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Comprovante Digital</h3>
              <p className="text-gray-600">
                Gere certificados em PDF com QR Code para verificação. 
                <strong className="text-amber-600"> Requer autenticação cartorária para validade legal.</strong>
              </p>
            </AnimatedCard>
          </div>
        </FadeInSection>

        {/* CTA Section */}
        <div className="bg-white rounded-xl shadow-lg p-8 text-center">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Comece agora mesmo
          </h3>
          <p className="text-gray-600 mb-6">
            Autorize o acesso aos seus dados de localização e gere seu comprovante 
            de residência em poucos minutos. Lembre-se de autenticar em cartório para validade legal.
          </p>

          {/* Pop-up de Consentimento seria adicionado aqui via modal antes da autorização */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6 max-w-2xl mx-auto">
            <div className="flex items-start">
              <svg className="w-5 h-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div className="text-sm text-blue-800">
                <p><strong>🔒 Seus dados estão seguros:</strong> Processamos seus dados seguindo rigorosamente a LGPD. 
                Você mantém controle total sobre suas informações.</p>
              </div>
            </div>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-lg mb-6">
              <p className="font-medium">Erro:</p>
              <p>{error}</p>
            </div>
          )}

          <AnimatedButton
            onClick={handleStartAuth}
            className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg transition duration-200 flex items-center justify-center mx-auto"
          >
            <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
              <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
              <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
              <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
              <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>
            Autorizar com Google Maps
          </AnimatedButton>

          <p className="text-sm text-gray-500 mt-4">
            Ao continuar, você concorda com nossos{' '}
            <Link href="/termos" className="text-blue-600 hover:underline">
              Termos de Uso
            </Link>{' '}
            e{' '}
            <Link href="/privacidade" className="text-blue-600 hover:underline">
              Política de Privacidade
            </Link>
          </p>
        </div>

        {/* How it Works */}
        <div className="mt-16">
          <h3 className="text-2xl font-bold text-gray-900 text-center mb-8">
            Como funciona?
          </h3>
          <div className="grid md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 font-bold">
                1
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Autorização Timeline</h4>
              <p className="text-sm text-gray-600">
                Autorize o acesso aos seus dados de <strong>Timeline</strong> (histórico de localização) do Google Maps
              </p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 font-bold">
                2
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Coleta</h4>
              <p className="text-sm text-gray-600">
                Coletamos seus dados de localização de forma segura e criptografada
              </p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 font-bold">
                3
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Validação</h4>
              <p className="text-sm text-gray-600">
                Algoritmos analisam sua permanência noturna no endereço informado
              </p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 font-bold">
                4
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Comprovante Digital</h4>
              <p className="text-sm text-gray-600">
                Receba seu documento preliminar em PDF. <strong className="text-amber-600">Autentique em cartório para validade legal</strong>
              </p>
            </div>
          </div>
        </div>

        {/* Timeline Explanation */}
        <div className="mt-16 bg-yellow-50 border border-yellow-200 rounded-xl p-8">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-semibold text-yellow-900 mb-2">
                📍 O que é o Google Timeline?
              </h3>
              <div className="text-yellow-800 space-y-2">
                <p>
                  O <strong>Timeline</strong> é diferente do Google Maps comum. É o <strong>histórico de localização</strong> que 
                  mostra onde você esteve ao longo do tempo.
                </p>
                <div className="grid md:grid-cols-2 gap-4 mt-4">
                  <div>
                    <h4 className="font-medium mb-2">📱 Como acessar no celular:</h4>
                    <ol className="text-sm space-y-1 list-decimal list-inside">
                      <li>Abrir Google Maps</li>
                      <li>Tocar na sua foto (canto superior direito)</li>
                      <li>Selecionar &quot;Sua timeline&quot;</li>
                      <li>Ver histórico de lugares visitados</li>
                    </ol>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">💻 Como acessar no computador:</h4>
                    <ol className="text-sm space-y-1 list-decimal list-inside">
                      <li>Ir para timeline.google.com</li>
                      <li>Fazer login na conta Google</li>
                      <li>Ver mapa com histórico completo</li>
                    </ol>
                  </div>
                </div>
                <div className="bg-yellow-100 border border-yellow-300 rounded-lg p-3 mt-4">
                  <p className="text-sm font-medium text-yellow-900">
                    ⚠️ <strong>Importante:</strong> O Timeline precisa estar <strong>ativado</strong> no seu celular 
                    e ter pelo menos 15-30 dias de dados para funcionar.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="mt-16 bg-white border border-gray-200 rounded-xl p-8">
          <h3 className="text-2xl font-bold text-gray-900 text-center mb-8">
            ❓ Perguntas Frequentes
          </h3>
          <div className="grid md:grid-cols-2 gap-8">
            <div className="space-y-6">
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">
                  🏛️ Por que preciso autenticar em cartório?
                </h4>
                <p className="text-gray-600 text-sm">
                  Para ter validade legal completa, documentos de comprovação de residência precisam ser 
                  autenticados por cartório. Nosso sistema gera o documento preliminar que acelera o processo cartorário.
                </p>
              </div>
              
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">
                  📋 O que levar ao cartório?
                </h4>
                <p className="text-gray-600 text-sm">
                  Leve o PDF gerado pelo sistema, seu documento de identidade e comprovante de endereço tradicional 
                  (conta de luz, água, etc.) como documento de apoio.
                </p>
              </div>

              <div>
                <h4 className="font-semibold text-gray-900 mb-2">
                  🔒 Meus dados estão seguros?
                </h4>
                <p className="text-gray-600 text-sm">
                  Sim! Seguimos rigorosamente a LGPD. Seus dados são criptografados, processados localmente 
                  e você mantém controle total sobre eles.
                </p>
              </div>
            </div>
            
            <div className="space-y-6">
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">
                  ⏰ Quanto tempo demora a validação?
                </h4>
                <p className="text-gray-600 text-sm">
                  A análise dos dados do Timeline leva alguns minutos. O processo completo de autenticação 
                  cartorária pode levar de 1 a 3 dias úteis.
                </p>
              </div>

              <div>
                <h4 className="font-semibold text-gray-900 mb-2">
                  🏢 Quais cartórios aceitam?
                </h4>
                <p className="text-gray-600 text-sm">
                  Todos os cartórios podem autenticar documentos. Recomendamos verificar com o cartório 
                  mais próximo sobre procedimentos específicos para documentos digitais.
                </p>
              </div>

              <div>
                <h4 className="font-semibold text-gray-900 mb-2">
                  💰 Quanto custa?
                </h4>
                <p className="text-gray-600 text-sm">
                  Nossa validação é gratuita. Apenas taxas cartoriais se aplicam 
                  (valores variam por estado, geralmente R$ 8-15).
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-gray-600">
            <p>&copy; 2024 Sistema de Validação de Residência. Todos os direitos reservados.</p>
            <p className="mt-2 text-sm">
              Sistema desenvolvido em conformidade com a LGPD - Lei Geral de Proteção de Dados
            </p>
          </div>
        </div>
      </footer>

      {/* Modal de Consentimento */}
      <ConsentModal
        isOpen={showConsentModal}
        onClose={() => setShowConsentModal(false)}
        onAccept={handleConsentAccept}
      />
    </div>
  );
}
