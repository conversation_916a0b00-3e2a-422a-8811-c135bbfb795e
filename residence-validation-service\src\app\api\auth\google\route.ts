import { NextRequest, NextResponse } from 'next/server';
import { googleAuthService } from '@/lib/google-auth';

export async function GET() {
  try {
    // Gera URL de autenticação do Google
    const authUrl = googleAuthService.getAuthUrl();
    
    return NextResponse.json({ 
      success: true, 
      authUrl 
    });
  } catch (error) {
    console.error('Erro ao gerar URL de autenticação:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
