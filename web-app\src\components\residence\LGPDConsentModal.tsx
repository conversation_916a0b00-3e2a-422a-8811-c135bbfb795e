import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Checkbox,
  FormControlLabel,
  Alert,
  Link,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  Security,
  LocationOn,
  Shield,
  Check,
  Warning
} from '@mui/icons-material';

interface LGPDConsentModalProps {
  open: boolean;
  onClose: () => void;
  onAccept: () => void;
}

const LGPDConsentModal: React.FC<LGPDConsentModalProps> = ({ open, onClose, onAccept }) => {
  const [acceptedTerms, setAcceptedTerms] = useState(false);
  const [acceptedPrivacy, setAcceptedPrivacy] = useState(false);

  const canProceed = acceptedTerms && acceptedPrivacy;

  const handleAccept = () => {
    if (canProceed) {
      onAccept();
    }
  };

  const handleClose = () => {
    setAcceptedTerms(false);
    setAcceptedPrivacy(false);
    onClose();
  };

  return (
    <Dialog 
      open={open} 
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { borderRadius: 2, p: 1 }
      }}
    >
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={1}>
          <Shield color="primary" />
          <Typography variant="h6">
            Consentimento LGPD - Validação de Residência
          </Typography>
        </Box>
      </DialogTitle>
      
      <DialogContent>
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="body2">
            <strong>Transparência Total:</strong> Explicamos exatamente quais dados coletamos e como os usamos.
          </Typography>
        </Alert>

        {/* Dados que coletamos */}
        <Paper elevation={1} sx={{ p: 2, mb: 2 }}>
          <Typography variant="h6" gutterBottom color="primary">
            📊 Dados que Coletamos
          </Typography>
          <List dense>
            <ListItem>
              <ListItemIcon><LocationOn fontSize="small" /></ListItemIcon>
              <ListItemText 
                primary="Histórico de Localização" 
                secondary="Dados do Google Maps Timeline para análise de presença"
              />
            </ListItem>
            <ListItem>
              <ListItemIcon><Security fontSize="small" /></ListItemIcon>
              <ListItemText 
                primary="Informações do Perfil" 
                secondary="Nome e email do Google para identificação"
              />
            </ListItem>
            <ListItem>
              <ListItemIcon><Check fontSize="small" /></ListItemIcon>
              <ListItemText 
                primary="Endereço Informado" 
                secondary="Endereço que você deseja validar"
              />
            </ListItem>
          </List>
        </Paper>

        {/* O que fazemos */}
        <Paper elevation={1} sx={{ p: 2, mb: 2 }}>
          <Typography variant="h6" gutterBottom color="success.main">
            ✅ O que Fazemos com seus Dados
          </Typography>
          <List dense>
            <ListItem>
              <ListItemIcon><Check color="success" fontSize="small" /></ListItemIcon>
              <ListItemText primary="Analisamos apenas período noturno (18h-6h)" />
            </ListItem>
            <ListItem>
              <ListItemIcon><Check color="success" fontSize="small" /></ListItemIcon>
              <ListItemText primary="Calculamos presença em raio de 100m do endereço" />
            </ListItem>
            <ListItem>
              <ListItemIcon><Check color="success" fontSize="small" /></ListItemIcon>
              <ListItemText primary="Geramos certificado preliminar se validado" />
            </ListItem>
            <ListItem>
              <ListItemIcon><Check color="success" fontSize="small" /></ListItemIcon>
              <ListItemText primary="Criptografamos todos os dados" />
            </ListItem>
          </List>
        </Paper>

        {/* O que NÃO fazemos */}
        <Paper elevation={1} sx={{ p: 2, mb: 2 }}>
          <Typography variant="h6" gutterBottom color="error">
            ❌ O que NÃO Fazemos
          </Typography>
          <List dense>
            <ListItem>
              <ListItemIcon><Warning color="error" fontSize="small" /></ListItemIcon>
              <ListItemText primary="Não compartilhamos dados com terceiros" />
            </ListItem>
            <ListItem>
              <ListItemIcon><Warning color="error" fontSize="small" /></ListItemIcon>
              <ListItemText primary="Não vendemos suas informações" />
            </ListItem>
            <ListItem>
              <ListItemIcon><Warning color="error" fontSize="small" /></ListItemIcon>
              <ListItemText primary="Não rastreamos sua navegação" />
            </ListItem>
            <ListItem>
              <ListItemIcon><Warning color="error" fontSize="small" /></ListItemIcon>
              <ListItemText primary="Não acessamos dados fora do período solicitado" />
            </ListItem>
          </List>
        </Paper>

        {/* Aviso Legal */}
        <Alert severity="warning" sx={{ mb: 3 }}>
          <Typography variant="body2">
            <strong>Importante:</strong> O certificado gerado é preliminar e não possui validade legal. 
            Para ter validade legal, deve ser autenticado em cartório competente.
          </Typography>
        </Alert>

        {/* Checkboxes de consentimento */}
        <Box sx={{ mt: 3 }}>
          <FormControlLabel
            control={
              <Checkbox
                checked={acceptedTerms}
                onChange={(e) => setAcceptedTerms(e.target.checked)}
                color="primary"
              />
            }
            label={
              <Typography variant="body2">
                Li e aceito os{' '}
                <Link href="/residence/terms" target="_blank" color="primary">
                  Termos de Uso
                </Link>
              </Typography>
            }
          />
          
          <FormControlLabel
            control={
              <Checkbox
                checked={acceptedPrivacy}
                onChange={(e) => setAcceptedPrivacy(e.target.checked)}
                color="primary"
              />
            }
            label={
              <Typography variant="body2">
                Li e aceito a{' '}
                <Link href="/residence/privacy" target="_blank" color="primary">
                  Política de Privacidade
                </Link>
              </Typography>
            }
          />
        </Box>

        {/* Seus direitos */}
        <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
          <Typography variant="subtitle2" gutterBottom>
            Seus Direitos LGPD:
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Você pode: acessar, corrigir, excluir seus dados, revogar consentimento, 
            solicitar portabilidade dos dados a qualquer momento.
          </Typography>
        </Box>
      </DialogContent>
      
      <DialogActions sx={{ p: 3 }}>
        <Button onClick={handleClose} color="inherit">
          Cancelar
        </Button>
        <Button
          onClick={handleAccept}
          variant="contained"
          color="primary"
          disabled={!canProceed}
        >
          Aceitar e Continuar
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default LGPDConsentModal;
