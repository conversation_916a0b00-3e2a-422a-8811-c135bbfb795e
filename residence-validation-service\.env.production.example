# ==============================================================================
# EXEMPLO DE CONFIGURAÇÃO PARA PRODUÇÃO
# ==============================================================================
# 
# IMPORTANTE:
# 1. Copie este arquivo para .env.production
# 2. Substitua TODOS os valores por valores reais
# 3. NUNCA commite o arquivo .env.production com valores reais!
# 4. Use este arquivo apenas como referência
# ==============================================================================

# ----------------------- CONFIGURAÇÕES OBRIGATÓRIAS -----------------------

# Google OAuth2 (OBRIGATÓRIO)
# Obtenha em: https://console.cloud.google.com/apis/credentials
GOOGLE_CLIENT_ID=SEU_GOOGLE_CLIENT_ID_AQUI.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=SEU_GOOGLE_CLIENT_SECRET_AQUI
NEXT_PUBLIC_GOOGLE_REDIRECT_URI=https://seudominio.com/api/auth/callback

# JWT Secret (OBRIGATÓRIO - mínimo 32 caracteres)
# Gere com: openssl rand -base64 32
JWT_SECRET=SUA_CHAVE_JWT_SUPER_SECRETA_E_COMPLEXA_AQUI_123456789

# Chave de criptografia (OBRIGATÓRIO - exatamente 32 caracteres)
# Gere com: openssl rand -base64 32 | cut -c1-32
ENCRYPTION_KEY=12345678901234567890123456789012

# URL da aplicação (OBRIGATÓRIO)
NEXT_PUBLIC_BASE_URL=https://seudominio.com

# ----------------------- CONFIGURAÇÕES OPCIONAIS -----------------------

# Configurações do banco de dados PostgreSQL
DATABASE_URL=*******************************************************/residencia_db
POSTGRES_PASSWORD=senha_super_segura_do_postgres

# Redis
REDIS_URL=redis://:senha_redis@redis:6379
REDIS_PASSWORD=senha_redis_super_segura

# Configurações de ambiente
NODE_ENV=production
NEXT_TELEMETRY_DISABLED=1

# Configurações de segurança
CORS_ORIGIN=https://seudominio.com
SESSION_SECRET=sua_session_secret_super_complexa

# ----------------------- COMO CONFIGURAR O GOOGLE OAUTH2 -----------------------
#
# 1. Acesse: https://console.cloud.google.com/
# 2. Crie um novo projeto ou selecione um existente
# 3. Ative a Google+ API ou Google Identity API
# 4. Vá em "Credenciais" > "Criar credenciais" > "ID do cliente OAuth 2.0"
# 5. Configure:
#    - Tipo: Aplicação da Web
#    - Origens JavaScript autorizadas: https://seudominio.com
#    - URIs de redirecionamento: https://seudominio.com/api/auth/callback
# 6. Copie o Client ID e Client Secret para as variáveis acima
#
# ----------------------- VALIDAÇÃO RÁPIDA -----------------------
#
# Execute no terminal para validar suas configurações:
# curl http://localhost:3000/api/debug/env
#
# ==============================================================================
