// Rotas da API de Validação de Residência
console.log('[Residence] 🔄 Inicializando módulo de validação de residência...');

const express = require('express');
const router = express.Router();

console.log('[Residence] ✅ Express router criado');
// Importações condicionais para evitar erros de módulos não encontrados
console.log('[Residence] 🔄 Carregando dependências...');

let GoogleTimelineService, ResidenceValidationUtils, GoogleAuthService;

try {
  console.log('[Residence] 🔄 Carregando GoogleTimelineService...');
  GoogleTimelineService = require('./googleTimelineService');
  console.log('[Residence] ✅ GoogleTimelineService carregado');
} catch (error) {
  console.warn('[Residence] ⚠️ GoogleTimelineService não disponível:', error.message);
  GoogleTimelineService = null;
}

try {
  console.log('[Residence] 🔄 Carregando ResidenceValidationUtils...');
  ResidenceValidationUtils = require('./validationUtils');
  console.log('[Residence] ✅ ResidenceValidationUtils carregado');
} catch (error) {
  console.warn('[Residence] ⚠️ ResidenceValidationUtils não disponível:', error.message);
  console.warn('[Residence] ⚠️ Erro detalhado:', error.stack);
  ResidenceValidationUtils = null;
}

try {
  console.log('[Residence] 🔄 Carregando GoogleAuthService...');
  GoogleAuthService = require('../../lib/google-auth');
  console.log('[Residence] ✅ GoogleAuthService carregado');
} catch (error) {
  console.warn('[Residence] ⚠️ GoogleAuthService não disponível:', error.message);
  GoogleAuthService = null;
}
const PDFDocument = require('pdfkit');
const QRCode = require('qrcode');
const fs = require('fs');
const path = require('path');
const jwt = require('jsonwebtoken');

// Middleware para verificar autenticação
const authenticateUser = (req, res, next) => {
  const token = req.headers.authorization?.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ error: 'Token de autenticação necessário' });
  }
  
  // Aqui você pode implementar validação JWT se necessário
  // Por enquanto, vamos simular que está autenticado
  req.user = { id: 1, email: '<EMAIL>' };
  next();
};

// 0. Rota básica de informações do serviço
console.log('[Residence] 🔄 Definindo rota GET /...');
router.get('/', (req, res) => {
  console.log('[Residence] 📥 Requisição recebida em GET /');

  const response = {
    success: true,
    service: 'Serviço de Validação de Residência',
    version: '1.0.0',
    endpoints: [
      'GET /api/residence - Informações do serviço',
      'GET /api/residence/auth/google - Gerar URL de autorização',
      'POST /api/residence/auth/google - Gerar URL de autorização (com dados)',
      'GET /api/residence/auth/callback - Callback OAuth2',
      'POST /api/residence/validate - Validar residência',
      'GET /api/residence/certificate/:id - Obter certificado'
    ],
    status: 'ativo',
    oauth_configured: !!(process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET),
    dependencies: {
      GoogleTimelineService: !!GoogleTimelineService,
      ResidenceValidationUtils: !!ResidenceValidationUtils,
      GoogleAuthService: !!GoogleAuthService
    }
  };

  console.log('[Residence] 📤 Enviando resposta:', response);
  res.json(response);
});
console.log('[Residence] ✅ Rota GET / definida');

// 1. Iniciar processo de autorização OAuth2 (GET - compatibilidade)
router.get('/auth/google', (req, res) => {
  try {
    // Verificar se as configurações do Google estão definidas
    if (!process.env.GOOGLE_CLIENT_ID || !process.env.GOOGLE_CLIENT_SECRET) {
      return res.status(500).json({
        success: false,
        error: 'Configurações do Google OAuth não definidas. Verifique GOOGLE_CLIENT_ID e GOOGLE_CLIENT_SECRET no arquivo .env'
      });
    }

    // Verificar se o GoogleTimelineService está disponível
    if (!GoogleTimelineService) {
      return res.status(500).json({
        success: false,
        error: 'GoogleTimelineService não está disponível. Verifique as dependências.'
      });
    }

    const authUrl = GoogleTimelineService.getAuthUrl();
    res.json({
      success: true,
      authUrl,
      message: 'Redirecione o usuário para esta URL para autorizar acesso'
    });
  } catch (error) {
    console.error('Erro ao gerar URL de autorização:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor: ' + error.message
    });
  }
});

// 1b. Iniciar processo de autorização OAuth2 (POST - para frontend)
router.post('/auth/google', (req, res) => {
  try {
    // Verificar se as configurações do Google estão definidas
    if (!process.env.GOOGLE_CLIENT_ID || !process.env.GOOGLE_CLIENT_SECRET) {
      return res.status(500).json({
        success: false,
        error: 'Configurações do Google OAuth não definidas. Verifique GOOGLE_CLIENT_ID e GOOGLE_CLIENT_SECRET no arquivo .env'
      });
    }

    const { linkId, clientName, scopes } = req.body;
    console.log('Gerando link de autorização para:', { linkId, clientName });

    const authUrl = GoogleTimelineService.getAuthUrl();
    res.json({
      success: true,
      authUrl,
      linkId,
      clientName,
      message: 'Redirecione o usuário para esta URL para autorizar acesso'
    });
  } catch (error) {
    console.error('Erro ao gerar URL de autorização:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor: ' + error.message
    });
  }
});

// 2. Callback do OAuth2
router.get('/auth/callback', async (req, res) => {
  try {
    const { code } = req.query;
    
    if (!code) {
      return res.status(400).json({ 
        success: false, 
        error: 'Código de autorização não fornecido' 
      });
    }
    
    const result = await GoogleTimelineService.handleCallback(code);
    
    if (result.success) {
      // Aqui você pode salvar os tokens no banco de dados
      // Por enquanto, vamos retornar para o frontend
      res.json({
        success: true,
        userInfo: result.userInfo,
        message: 'Autorização realizada com sucesso'
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    console.error('Erro no callback OAuth2:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Erro interno do servidor' 
    });
  }
});

// 3. Coletar dados da Timeline e validar residência
router.post('/validate', authenticateUser, async (req, res) => {
  try {
    const { 
      homeAddress, 
      coordinates, 
      startDate, 
      endDate, 
      useDemo = false 
    } = req.body;
    
    // Validar dados obrigatórios
    if (!homeAddress || !coordinates || !startDate || !endDate) {
      return res.status(400).json({
        success: false,
        error: 'Dados obrigatórios: homeAddress, coordinates, startDate, endDate'
      });
    }
    
    const { latitude, longitude } = coordinates;
    
    if (!latitude || !longitude) {
      return res.status(400).json({
        success: false,
        error: 'Coordenadas inválidas'
      });
    }
    
    let locations = [];
    
    if (useDemo) {
      // Gerar dados de demonstração
      const daysCount = Math.ceil((new Date(endDate) - new Date(startDate)) / (1000 * 60 * 60 * 24));
      locations = ResidenceValidationUtils.generateDemoData(latitude, longitude, daysCount);
    } else {
      // Tentar coletar dados reais da Timeline
      try {
        const timelineData = await GoogleTimelineService.getTimelineData(startDate, endDate);
        locations = timelineData.locations || [];
      } catch (error) {
        console.error('Erro ao coletar dados da Timeline:', error);
        return res.status(500).json({
          success: false,
          error: 'Erro ao coletar dados da Timeline. Tente usar o modo demo.',
          details: error.message
        });
      }
    }
    
    // Validar residência
    const validation = ResidenceValidationUtils.validateResidence(
      locations, 
      latitude, 
      longitude
    );
    
    // Salvar resultado da validação (implementar conforme necessário)
    const validationId = `val_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    res.json({
      success: true,
      validationId,
      validation,
      metadata: {
        homeAddress,
        coordinates,
        period: { startDate, endDate },
        processedAt: new Date().toISOString(),
        demoMode: useDemo
      }
    });
    
  } catch (error) {
    console.error('Erro na validação:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Erro interno do servidor',
      details: error.message
    });
  }
});

// 4. Gerar certificado preliminar em PDF
router.post('/certificate/generate', authenticateUser, async (req, res) => {
  try {
    const { validationId, validationData } = req.body;
    
    if (!validationId || !validationData) {
      return res.status(400).json({
        success: false,
        error: 'ID de validação e dados são obrigatórios'
      });
    }
    
    // Criar PDF
    const doc = new PDFDocument();
    
    // Configurar resposta
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="certificado_residencia_${validationId}.pdf"`);
    
    // Pipe do PDF para a resposta
    doc.pipe(res);
    
    // Cabeçalho
    doc.fontSize(20).text('CERTIFICADO PRELIMINAR DE RESIDÊNCIA', 50, 50);
    doc.fontSize(12).text('⚠️ Este certificado é PRELIMINAR e não possui validade legal por si só', 50, 80);
    doc.text('Para validade legal, deve ser autenticado em cartório competente', 50, 95);
    
    // Linha separadora
    doc.moveTo(50, 120).lineTo(550, 120).stroke();
    
    // Dados da validação
    doc.fontSize(14).text('DADOS DA VALIDAÇÃO', 50, 140);
    doc.fontSize(10);
    
    let y = 160;
    doc.text(`ID da Validação: ${validationId}`, 50, y);
    y += 15;
    doc.text(`Endereço: ${validationData.metadata.homeAddress}`, 50, y);
    y += 15;
    doc.text(`Coordenadas: ${validationData.metadata.coordinates.latitude}, ${validationData.metadata.coordinates.longitude}`, 50, y);
    y += 15;
    doc.text(`Período: ${validationData.metadata.period.startDate} a ${validationData.metadata.period.endDate}`, 50, y);
    y += 15;
    doc.text(`Processado em: ${new Date(validationData.metadata.processedAt).toLocaleString('pt-BR')}`, 50, y);
    
    // Resultado da validação
    y += 30;
    doc.fontSize(14).text('RESULTADO DA VALIDAÇÃO', 50, y);
    doc.fontSize(10);
    
    y += 20;
    doc.text(`Status: ${validationData.validation.summary.status}`, 50, y);
    y += 15;
    doc.text(`Score: ${validationData.validation.summary.scorePercentage}%`, 50, y);
    y += 15;
    doc.text(`Dias Consecutivos: ${validationData.validation.maxConsecutiveDays}`, 50, y);
    y += 15;
    doc.text(`Total de Dias: ${validationData.validation.totalDays}`, 50, y);
    y += 15;
    doc.text(`Dias Válidos: ${validationData.validation.validDays}`, 50, y);
    y += 15;
    doc.text(`Motivo: ${validationData.validation.summary.reason}`, 50, y);
    
    // Aviso legal
    y += 40;
    doc.fontSize(12).text('AVISO LEGAL', 50, y);
    doc.fontSize(9);
    y += 20;
    doc.text('Este certificado é gerado automaticamente baseado em dados de geolocalização', 50, y);
    y += 12;
    doc.text('e serve apenas como documento preliminar para agilizar processos cartoriais.', 50, y);
    y += 12;
    doc.text('Para ter validade legal, deve ser autenticado em cartório competente.', 50, y);
    y += 12;
    doc.text('Custos de autenticação: R$ 8,00 a R$ 15,00 (varia por estado).', 50, y);
    y += 12;
    doc.text('Prazo de autenticação: 1 a 3 dias úteis.', 50, y);
    
    // Rodapé
    doc.fontSize(8).text('Sistema de Validação de Residência - Cartório Tech', 50, 750);
    doc.text(`Gerado em: ${new Date().toLocaleString('pt-BR')}`, 50, 765);
    
    // Finalizar PDF
    doc.end();
    
  } catch (error) {
    console.error('Erro ao gerar certificado:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Erro interno do servidor',
      details: error.message
    });
  }
});

// 5. Obter informações de validação
router.get('/validation/:id', authenticateUser, (req, res) => {
  try {
    const { id } = req.params;
    
    // Aqui você buscaria no banco de dados
    // Por enquanto, vamos simular
    res.json({
      success: true,
      validation: {
        id,
        status: 'completed',
        message: 'Validação encontrada (simulada)'
      }
    });
    
  } catch (error) {
    console.error('Erro ao buscar validação:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Erro interno do servidor' 
    });
  }
});

// 6. Health check
router.get('/health', (req, res) => {
  res.json({
    success: true,
    service: 'Residence Validation API',
    status: 'healthy',
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
