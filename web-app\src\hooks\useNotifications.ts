import { useState, useEffect, useCallback } from 'react';

interface NotificationOptions {
  title: string;
  body: string;
  data?: any;
  userId?: string;
}

interface UseNotificationsReturn {
  isSupported: boolean;
  permission: NotificationPermission;
  isSubscribed: boolean;
  requestPermission: () => Promise<boolean>;
  subscribe: () => Promise<boolean>;
  unsubscribe: () => Promise<boolean>;
  sendNotification: (options: NotificationOptions) => Promise<boolean>;
  sendTimelineReminder: (userId?: string) => Promise<boolean>;
  sendValidationStatus: (status: 'processing' | 'completed' | 'failed', validationId: string, userId?: string) => Promise<boolean>;
}

const VAPID_PUBLIC_KEY = process.env.REACT_APP_VAPID_PUBLIC_KEY || 'BEl62iUYgUivxIkv69yViEuiBIa40HcCWLEaQC7-jCuLKRBjfHUYiSW2UNzSdHoTxKv4t2Vd6tIFvGD8MIbSA1s';

export const useNotifications = (): UseNotificationsReturn => {
  const [isSupported, setIsSupported] = useState(false);
  const [permission, setPermission] = useState<NotificationPermission>('default');
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [registration, setRegistration] = useState<ServiceWorkerRegistration | null>(null);

  useEffect(() => {
    // Verificar suporte a notificações
    const supported = 'Notification' in window && 'serviceWorker' in navigator && 'PushManager' in window;
    setIsSupported(supported);

    if (supported) {
      setPermission(Notification.permission);
      
      // Obter service worker registration
      navigator.serviceWorker.ready.then((reg) => {
        setRegistration(reg);
        
        // Verificar se já está subscrito
        reg.pushManager.getSubscription().then((subscription) => {
          setIsSubscribed(!!subscription);
        });
      });
    }
  }, []);

  const urlBase64ToUint8Array = useCallback((base64String: string) => {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  }, []);

  const requestPermission = useCallback(async (): Promise<boolean> => {
    if (!isSupported) {
      console.warn('[Notifications] Not supported in this browser');
      return false;
    }

    try {
      const result = await Notification.requestPermission();
      setPermission(result);
      return result === 'granted';
    } catch (error) {
      console.error('[Notifications] Error requesting permission:', error);
      return false;
    }
  }, [isSupported]);

  const subscribe = useCallback(async (): Promise<boolean> => {
    if (!isSupported || !registration || permission !== 'granted') {
      console.warn('[Notifications] Cannot subscribe - missing requirements');
      return false;
    }

    try {
      // Verificar se já está subscrito
      const existingSubscription = await registration.pushManager.getSubscription();
      if (existingSubscription) {
        setIsSubscribed(true);
        return true;
      }

      // Criar nova subscription
      const subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: urlBase64ToUint8Array(VAPID_PUBLIC_KEY)
      });

      // Enviar subscription para o servidor
      const response = await fetch('/api/notifications/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(subscription),
      });

      if (response.ok) {
        setIsSubscribed(true);
        console.log('[Notifications] Successfully subscribed');
        return true;
      } else {
        console.error('[Notifications] Failed to subscribe on server');
        return false;
      }
    } catch (error) {
      console.error('[Notifications] Error subscribing:', error);
      return false;
    }
  }, [isSupported, registration, permission, urlBase64ToUint8Array]);

  const unsubscribe = useCallback(async (): Promise<boolean> => {
    if (!isSupported || !registration) {
      return false;
    }

    try {
      const subscription = await registration.pushManager.getSubscription();
      if (subscription) {
        await subscription.unsubscribe();
        
        // Notificar o servidor
        await fetch('/api/notifications/unsubscribe', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ endpoint: subscription.endpoint }),
        });
      }

      setIsSubscribed(false);
      console.log('[Notifications] Successfully unsubscribed');
      return true;
    } catch (error) {
      console.error('[Notifications] Error unsubscribing:', error);
      return false;
    }
  }, [isSupported, registration]);

  const sendNotification = useCallback(async (options: NotificationOptions): Promise<boolean> => {
    try {
      const response = await fetch('/api/notifications/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(options),
      });

      if (response.ok) {
        console.log('[Notifications] Notification sent successfully');
        return true;
      } else {
        console.error('[Notifications] Failed to send notification');
        return false;
      }
    } catch (error) {
      console.error('[Notifications] Error sending notification:', error);
      return false;
    }
  }, []);

  const sendTimelineReminder = useCallback(async (userId?: string): Promise<boolean> => {
    try {
      const response = await fetch('/api/notifications/timeline-reminder', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
      });

      return response.ok;
    } catch (error) {
      console.error('[Notifications] Error sending timeline reminder:', error);
      return false;
    }
  }, []);

  const sendValidationStatus = useCallback(async (
    status: 'processing' | 'completed' | 'failed',
    validationId: string,
    userId?: string
  ): Promise<boolean> => {
    try {
      const response = await fetch('/api/notifications/validation-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status, validationId, userId }),
      });

      return response.ok;
    } catch (error) {
      console.error('[Notifications] Error sending validation status:', error);
      return false;
    }
  }, []);

  return {
    isSupported,
    permission,
    isSubscribed,
    requestPermission,
    subscribe,
    unsubscribe,
    sendNotification,
    sendTimelineReminder,
    sendValidationStatus,
  };
};

export default useNotifications;
