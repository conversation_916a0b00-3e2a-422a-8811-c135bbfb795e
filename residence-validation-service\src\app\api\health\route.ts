import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      checks: {
        server: 'ok',
        database: 'pending',
        redis: 'pending',
        google_api: 'pending',
      },
    };

    // Verificar banco de dados (quando implementado)
    try {
      // TODO: Implementar verificação do banco
      // const dbCheck = await checkDatabase();
      health.checks.database = 'ok';
    } catch (error) {
      health.checks.database = 'error';
      health.status = 'degraded';
    }

    // Verificar Redis (quando implementado)
    try {
      // TODO: Implementar verificação do Redis
      // const redisCheck = await checkRedis();
      health.checks.redis = 'ok';
    } catch (error) {
      health.checks.redis = 'error';
      health.status = 'degraded';
    }

    // Verificar conectividade com Google API
    try {
      const response = await fetch('https://www.googleapis.com/oauth2/v1/tokeninfo?access_token=invalid', {
        method: 'GET',
      });
      // Se chegou até aqui, a API está acessível (mesmo que retorne erro de token)
      health.checks.google_api = 'ok';
    } catch (error) {
      health.checks.google_api = 'error';
      health.status = 'degraded';
    }

    // Determinar status final
    const hasErrors = Object.values(health.checks).includes('error');
    if (hasErrors) {
      health.status = 'unhealthy';
    }

    const statusCode = health.status === 'healthy' ? 200 : 
                      health.status === 'degraded' ? 200 : 503;

    return NextResponse.json(health, { status: statusCode });
  } catch (error) {
    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: 'Health check failed',
      },
      { status: 503 }
    );
  }
}
