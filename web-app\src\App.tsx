import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import MainLayout from './components/MainLayout';
import HomePage from './pages/HomePage';
import RecordPage from './pages/RecordPage';
import RecordingsPage from './pages/RecordingsPage';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import AdminPage from './pages/AdminPage';
import ProfilePage from './pages/ProfilePage';
import SettingsPage from './pages/SettingsPage';
import SubscriptionPage from './pages/SubscriptionPage';
import AnalyticsDashboard from './pages/AnalyticsDashboard';
import NotFoundPage from './pages/NotFoundPage';
import OCRPage from './pages/OCRPage';
import ResidenceRoutes from './pages/ResidenceRoutes';
import { ToastProvider } from './components/Toast';
import { NotificationProvider } from './contexts/NotificationContext';
import { CustomThemeProvider } from './contexts/ThemeContext';
import './App.css';

function App() {
  return (
    <CustomThemeProvider>
      <NotificationProvider>
        <ToastProvider>
          <Router>
          <MainLayout>
            <Routes>
              <Route path="/" element={<HomePage />} />
              <Route path="/record" element={<RecordPage />} />
              <Route path="/recordings" element={<RecordingsPage />} />
              <Route path="/analytics" element={<AnalyticsDashboard />} />
              <Route path="/residence/*" element={<ResidenceRoutes />} />
              <Route path="/login" element={<LoginPage />} />
              <Route path="/register" element={<RegisterPage />} />
              <Route path="/admin" element={<AdminPage />} />
              <Route path="/profile" element={<ProfilePage />} />
              <Route path="/settings" element={<SettingsPage />} />
              <Route path="/subscription" element={<SubscriptionPage />} />
              <Route path="/ocr" element={<OCRPage />} />
              <Route path="*" element={<NotFoundPage />} />
            </Routes>
          </MainLayout>
        </Router>
      </ToastProvider>
    </NotificationProvider>
    </CustomThemeProvider>
  );
}

export default App;