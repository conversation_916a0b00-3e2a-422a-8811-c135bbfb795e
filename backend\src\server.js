require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const { initializeDatabase } = require('./database');
const recordingsRoutes = require('./routes/recordings');
const reportsRoutes = require('./routes/reports');
const analyticsRoutes = require('./routes/analytics');
const residenceRoutes = require('./routes/residence');
const { swaggerUi, specs } = require('./config/swagger');

// Mapear variáveis de ambiente do Docker Compose se necessário
if (!process.env.DB_HOST && process.env.POSTGRES_HOST) {
  process.env.DB_HOST = process.env.POSTGRES_HOST;
}
if (!process.env.DB_NAME && process.env.POSTGRES_DB) {
  process.env.DB_NAME = process.env.POSTGRES_DB;
}
if (!process.env.DB_USER && process.env.POSTGRES_USER) {
  process.env.DB_USER = process.env.POSTGRES_USER;
}
if (!process.env.DB_PASSWORD && process.env.POSTGRES_PASSWORD) {
  process.env.DB_PASSWORD = process.env.POSTGRES_PASSWORD;
}

// Validação de variáveis de ambiente obrigatórias
const requiredEnvVars = ['DB_HOST', 'DB_NAME', 'DB_USER', 'DB_PASSWORD'];
const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
  console.error('❌ Variáveis de ambiente obrigatórias não encontradas:', missingVars);
  console.error('💡 Verifique se o arquivo .env está configurado corretamente');
  console.error('📋 Consulte o arquivo .env.example para referência');
  process.exit(1);
}

const app = express();
const PORT = process.env.PORT || 3001;

// Middlewares
app.use(helmet());
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Middleware de logging simples
app.use((req, res, next) => {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${req.method} ${req.url} - ${req.ip}`);
  next();
});

// Swagger Documentation
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs, {
  customCss: '.swagger-ui .topbar { display: none }',
  customSiteTitle: 'Sistema Cartorial API',
  customfavIcon: '/favicon.ico',
  swaggerOptions: {
    persistAuthorization: true,
    displayRequestDuration: true,
    filter: true,
    showExtensions: true,
    showCommonExtensions: true
  }
}));

// Inicializar banco de dados PostgreSQL
initializeDatabase()
  .then(() => {
    console.log('PostgreSQL conectado e inicializado com sucesso');
  })
  .catch(err => {
    console.error('Erro ao conectar ao PostgreSQL:', err);
    process.exit(1);
  });

// Rotas com prefixo /api
app.use('/api/recordings', recordingsRoutes);
app.use('/api/reports', reportsRoutes);
app.use('/api/analytics', analyticsRoutes);
app.use('/api/residence', residenceRoutes);

/**
 * @swagger
 * /health:
 *   get:
 *     summary: Verificação de saúde do sistema
 *     tags: [System]
 *     description: Endpoint para verificar se o servidor está funcionando corretamente
 *     responses:
 *       200:
 *         description: Servidor funcionando normalmente
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: ok
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                   example: '2025-07-13T14:30:15.123Z'
 *                 uptime:
 *                   type: number
 *                   description: Tempo de atividade em segundos
 *                   example: 3600
 *                 memory:
 *                   type: object
 *                   properties:
 *                     used:
 *                       type: number
 *                       description: Memória utilizada em MB
 *                     total:
 *                       type: number
 *                       description: Memória total em MB
 */
app.get('/health', (req, res) => {
  const memUsage = process.memoryUsage();
  res.status(200).json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: {
      used: Math.round(memUsage.heapUsed / 1024 / 1024),
      total: Math.round(memUsage.heapTotal / 1024 / 1024)
    },
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development'
  });
});

/**
 * @swagger
 * /api-docs:
 *   get:
 *     summary: Documentação da API
 *     tags: [System]
 *     description: Interface Swagger para documentação interativa da API
 *     responses:
 *       200:
 *         description: Página de documentação carregada
 */

// Rota raiz com informações da API
/**
 * @swagger
 * /:
 *   get:
 *     summary: Informações da API
 *     tags: [System]
 *     description: Endpoint raiz com informações básicas da API
 *     responses:
 *       200:
 *         description: Informações da API
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 name:
 *                   type: string
 *                   example: Sistema de Gravação Cartorial API
 *                 version:
 *                   type: string
 *                   example: 1.0.0
 *                 description:
 *                   type: string
 *                   example: API REST para gravação de tela com conformidade LGPD
 *                 docs:
 *                   type: string
 *                   example: /api-docs
 */
app.get('/', (req, res) => {
  res.json({
    name: 'Sistema de Gravação Cartorial API',
    version: '1.0.0',
    description: 'API REST para gravação de tela com conformidade LGPD para cartórios',
    docs: '/api-docs',
    endpoints: {
      recordings: '/api/recordings',
      reports: '/api/reports',
      health: '/health'
    }
  });
});

// Middleware para rotas não encontradas
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Rota não encontrada',
    message: `A rota ${req.method} ${req.originalUrl} não existe`,
    availableEndpoints: {
      root: '/',
      health: '/health',
      docs: '/api-docs',
      recordings: '/api/recordings',
      reports: '/api/reports',
      analytics: '/api/analytics'
    }
  });
});

// Middleware de tratamento de erros
app.use((err, req, res, next) => {
  console.error(`[${new Date().toISOString()}] ERROR:`, err.stack);
  
  // Erro de validação
  if (err.name === 'ValidationError') {
    return res.status(400).json({
      error: 'Erro de validação',
      message: err.message,
      details: err.errors
    });
  }
  
  // Erro de banco de dados
  if (err.code === 'ECONNREFUSED' || err.code === '28P01') {
    return res.status(503).json({
      error: 'Erro de conexão com banco de dados',
      message: 'Serviço temporariamente indisponível'
    });
  }
  
  // Erro genérico
  res.status(err.status || 500).json({
    error: 'Erro interno do servidor',
    message: process.env.NODE_ENV === 'production' ? 'Algo deu errado' : err.message,
    timestamp: new Date().toISOString()
  });
});

app.listen(PORT, () => {
  console.log(`🚀 Servidor rodando na porta ${PORT}`);
  console.log(`📚 Documentação disponível em: http://localhost:${PORT}/api-docs`);
  console.log(`💚 Health check em: http://localhost:${PORT}/health`);
  console.log(`🌍 Ambiente: ${process.env.NODE_ENV || 'development'}`);
}).on('error', (err) => {
  console.error('❌ Erro ao iniciar o servidor:', err.message);
  process.exit(1);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('📴 Recebido SIGTERM, encerrando servidor graciosamente...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('📴 Recebido SIGINT, encerrando servidor graciosamente...');
  process.exit(0);
});

module.exports = app;
