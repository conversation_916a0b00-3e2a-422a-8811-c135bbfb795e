/**
 * Rotas para gerenciamento de certificados
 */
const express = require('express');
const router = express.Router();
const certificateService = require('../services/certificateService');
const authenticateApiKey = require('../middleware/authMiddleware');
const validateRequest = require('../middleware/validationMiddleware');
const logger = require('../utils/logger');

/**
 * @swagger
 * /api/certificate:
 *   get:
 *     summary: Informações sobre o serviço de certificados
 *     tags: [Certificados]
 *     responses:
 *       200:
 *         description: Informações do serviço
 */
router.get('/', (req, res) => {
  res.json({
    success: true,
    service: 'Serviço de Certificados Digitais',
    version: '1.0.0',
    availableEndpoints: [
      'POST /api/certificate/create - Criar certificado',
      'GET /api/certificate/list - Listar certificados',
      'GET /api/certificate/:id - Obter certificado',
      'POST /api/certificate/validate - Validar certificado',
      'DELETE /api/certificate/:id - Revogar certificado',
      'GET /api/certificate/chain/:id - Cadeia de certificação'
    ],
    certificateTypes: [
      'A1 - Certificado de software',
      'A3 - Certificado em hardware',
      'S1 - Certificado de sigilo',
      'T3 - Certificado de carimbo de tempo'
    ],
    features: [
      'Geração de certificados X.509',
      'Suporte a ICP-Brasil',
      'Validação de cadeia',
      'Revogação de certificados',
      'Armazenamento seguro',
      'Backup e recuperação'
    ],
    algorithms: [
      'RSA 2048/4096 bits',
      'ECDSA P-256/P-384',
      'SHA-256/SHA-384/SHA-512'
    ],
    timestamp: new Date().toISOString()
  });
});

/**
 * @swagger
 * /api/certificate/create:
 *   post:
 *     summary: Criar um novo certificado para assinatura
 *     tags: [Certificados]
 *     security:
 *       - apiKey: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - commonName
 *             properties:
 *               commonName:
 *                 type: string
 *                 description: Nome comum do titular do certificado
 *               organization:
 *                 type: string
 *                 description: Organização do titular
 *               email:
 *                 type: string
 *                 description: Email do titular
 *     responses:
 *       200:
 *         description: Certificado criado com sucesso
 */
router.post('/create',
  authenticateApiKey,
  validateRequest(['commonName']),
  async (req, res) => {
    try {
      const { commonName, organization, email } = req.body;
      
      // Criar certificado
      const certificate = await certificateService.createSigningCertificate(
        commonName,
        organization,
        email
      );
      
      res.json({
        success: true,
        message: 'Certificado criado com sucesso',
        certificate: {
          id: certificate.certId,
          commonName,
          organization,
          email,
          validFrom: certificate.validFrom,
          validUntil: certificate.validUntil,
          fingerprint: certificate.fingerprint
        }
      });
      
    } catch (error) {
      logger.error('Erro ao criar certificado:', error);
      
      res.status(500).json({
        success: false,
        message: 'Erro ao criar certificado',
        error: error.message
      });
    }
  }
);

/**
 * @swagger
 * /api/certificate/list:
 *   get:
 *     summary: Listar certificados disponíveis
 *     tags: [Certificados]
 *     security:
 *       - apiKey: []
 *     responses:
 *       200:
 *         description: Lista de certificados
 */
router.get('/list',
  authenticateApiKey,
  async (req, res) => {
    try {
      // Listar certificados
      const certificates = await certificateService.listSigningCertificates();
      
      res.json({
        success: true,
        certificates
      });
      
    } catch (error) {
      logger.error('Erro ao listar certificados:', error);
      
      res.status(500).json({
        success: false,
        message: 'Erro ao listar certificados',
        error: error.message
      });
    }
  }
);

/**
 * @swagger
 * /api/certificate/{certId}:
 *   get:
 *     summary: Obter informações de um certificado
 *     tags: [Certificados]
 *     security:
 *       - apiKey: []
 *     parameters:
 *       - in: path
 *         name: certId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Informações do certificado
 */
router.get('/:certId',
  authenticateApiKey,
  async (req, res) => {
    try {
      const certId = req.params.certId;
      
      // Carregar certificado
      const certificate = await certificateService.loadSigningCertificate(certId);
      
      res.json({
        success: true,
        certificate: {
          id: certificate.id,
          commonName: certificate.commonName,
          organization: certificate.organization,
          email: certificate.email,
          serialNumber: certificate.serialNumber,
          validFrom: certificate.validFrom,
          validUntil: certificate.validUntil,
          fingerprint: certificate.fingerprint
        }
      });
      
    } catch (error) {
      logger.error('Erro ao obter informações do certificado:', error);
      
      res.status(404).json({
        success: false,
        message: 'Certificado não encontrado ou erro ao carregá-lo',
        error: error.message
      });
    }
  }
);

/**
 * @swagger
 * /api/certificate/download/{certId}:
 *   get:
 *     summary: Download de um certificado no formato PEM
 *     tags: [Certificados]
 *     security:
 *       - apiKey: []
 *     parameters:
 *       - in: path
 *         name: certId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Certificado no formato PEM
 *         content:
 *           application/x-pem-file:
 *             schema:
 *               type: string
 */
router.get('/download/:certId',
  authenticateApiKey,
  async (req, res) => {
    try {
      const certId = req.params.certId;
      
      // Carregar certificado
      const certificate = await certificateService.loadSigningCertificate(certId);
      
      res.setHeader('Content-Type', 'application/x-pem-file');
      res.setHeader('Content-Disposition', `attachment; filename=${certId}.cert.pem`);
      res.send(certificate.certPem);
      
    } catch (error) {
      logger.error('Erro ao fazer download do certificado:', error);
      
      res.status(404).json({
        success: false,
        message: 'Certificado não encontrado ou erro ao carregá-lo',
        error: error.message
      });
    }
  }
);

/**
 * @swagger
 * /api/certificate/download/ca:
 *   get:
 *     summary: Download do certificado raiz (CA)
 *     tags: [Certificados]
 *     security:
 *       - apiKey: []
 *     responses:
 *       200:
 *         description: Certificado raiz no formato PEM
 *         content:
 *           application/x-pem-file:
 *             schema:
 *               type: string
 */
router.get('/download/ca',
  authenticateApiKey,
  async (req, res) => {
    try {
      // Carregar CA raiz
      const rootCA = await certificateService.loadRootCA();
      
      res.setHeader('Content-Type', 'application/x-pem-file');
      res.setHeader('Content-Disposition', 'attachment; filename=root-ca.cert.pem');
      res.send(rootCA.certPem);
      
    } catch (error) {
      logger.error('Erro ao fazer download do certificado raiz:', error);
      
      res.status(500).json({
        success: false,
        message: 'Erro ao fazer download do certificado raiz',
        error: error.message
      });
    }
  }
);

module.exports = router;
