<!DOCTYPE html>
<html lang="pt-br">
<head>
  <meta charset="UTF-8">
  <title>ProvaSegura - Slide 2: <PERSON> <PERSON>a</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');
    
    body {
      margin: 0;
      padding: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      font-family: 'Inter', 'Segoe UI', Arial, sans-serif;
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }
      .slide {
      width: 90vw;
      max-width: 1280px;
      height: 80vh;
      max-height: 720px;
      background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
      box-shadow: 0 20px 60px rgba(0,0,0,0.15), 0 8px 32px rgba(0,0,0,0.08);
      border-radius: 24px;
      display: flex;
      flex-direction: row;
      overflow: hidden;
      position: relative;
      border: 1px solid rgba(255,255,255,0.2);
    }
    
    .slide::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #dc2626 0%, #ef4444 50%, #f87171 100%);
      border-radius: 24px 24px 0 0;
    }
      .slide-content {
      padding: 40px 60px;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      position: relative;
      z-index: 2;
      overflow-y: auto;
    }
      .slide-title {
      font-size: 2.5rem;
      font-weight: 800;
      background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-bottom: 24px;
      line-height: 1.1;
      letter-spacing: -0.02em;
    }
      .slide-text {
      font-size: 1rem;
      color: #334155;
      line-height: 1.6;
      font-weight: 400;
    }
    
    .slide-text ul {
      padding-left: 0;
      list-style: none;
    }    .slide-text li {
      position: relative;
      padding: 15px 15px 15px 45px;
      background: rgba(239, 68, 68, 0.05);
      border-left: 4px solid #ef4444;
      border-radius: 8px;
      margin-bottom: 12px;
    }
      .slide-text li::before {
      content: '⚠️';
      position: absolute;
      left: 15px;
      top: 15px;
      font-size: 1.2rem;
    }
    
    .slide-text strong {
      color: #dc2626;
      font-weight: 700;
    }
      .slide-img {
      flex: 0 0 300px;
      background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      overflow: hidden;
      order: -1;
    }
    
    .slide-img::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 70% 30%, rgba(245, 158, 11, 0.1) 0%, transparent 50%);
    }
    
    .slide-img svg {
      filter: drop-shadow(0 15px 35px rgba(220, 38, 38, 0.2));
      z-index: 1;
      position: relative;
    }
    
    .slide-number {
      position: absolute;
      top: 30px;
      right: 40px;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-weight: 700;
      font-size: 1.3rem;
      box-shadow: 0 8px 25px rgba(220, 38, 38, 0.3);
    }    .stats-highlight {
      display: flex;
      gap: 20px;
      margin-top: 20px;
    }

    .stat-card {
      background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
      padding: 15px;
      border-radius: 12px;
      text-align: center;
      border: 2px solid rgba(239, 68, 68, 0.2);
      flex: 1;
      min-width: 0;
    }

    .stat-number {
      font-size: 1.5rem;
      font-weight: 800;
      color: #dc2626;
      display: block;
    }

    .stat-label {
      font-size: 0.8rem;
      color: #7f1d1d;
      font-weight: 600;
      margin-top: 6px;
    }    @media print {
      body { background: #fff; }
      .slide { box-shadow: none; border: 1px solid #e2e8f0; }
    }

    @media (max-width: 768px) {
      .slide {
        flex-direction: column;
        width: 95vw;
        height: 90vh;
      }
      
      .slide-content {
        padding: 30px 20px;
      }
      
      .slide-title {
        font-size: 2rem;
      }
      
      .slide-img {
        flex: 0 0 200px;
      }
      
      .slide-img svg {
        width: 200px;
        height: 200px;
      }
      
      .stats-highlight {
        gap: 10px;
      }
      
      .stat-number {
        font-size: 1.2rem;
      }
    }

    .navigation {
      position: fixed;
      bottom: 30px;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      gap: 15px;
      z-index: 1000;
    }

    .nav-button {
      background: rgba(255, 255, 255, 0.9);
      border: none;
      padding: 12px 24px;
      border-radius: 25px;
      color: #1e293b;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      text-decoration: none;
      display: inline-block;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .nav-button:hover {
      background: white;
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }
  </style>
</head>
<body>

<div class="slide">
  <div class="slide-number">2</div>
  <div class="slide-content">
    <div class="slide-title">Fraudes e Ineficiências na Comprovação de Localização</div>
    <div class="slide-text">
      <ul>
        <li><strong>Fraudes generalizadas:</strong> Comprovantes de residência falsos ou desatualizados custam bilhões em fraudes no Brasil (R$ 12 bilhões/ano em seguros, 8 milhões de processos trabalhistas).</li>
        <li><strong>Processos lentos:</strong> Documentos físicos (ex.: contas de luz) atrasam contratações, financiamentos e decisões judiciais.</li>
        <li><strong>Riscos para empresas:</strong> Endereços falsos geram problemas trabalhistas, logísticos e de segurança.</li>
      </ul>
      
      <div class="stats-highlight">
        <div class="stat-card">
          <span class="stat-number">R$ 12B</span>
          <div class="stat-label">Fraudes/ano</div>
        </div>
        <div class="stat-card">
          <span class="stat-number">8M</span>
          <div class="stat-label">Processos</div>
        </div>
        <div class="stat-card">
          <span class="stat-number">30%</span>
          <div class="stat-label">Docs Falsos</div>
        </div>
      </div>
    </div>
  </div>
  <div class="slide-img">
    <!-- Documentos com problemas melhorado -->
    <svg width="300" height="300" viewBox="0 0 300 300">
      <!-- Pilha de documentos problemáticos -->
      <rect x="50" y="120" width="200" height="140" rx="16" fill="#fff" stroke="#dc2626" stroke-width="4"/>
      <rect x="60" y="110" width="200" height="140" rx="16" fill="#fef2f2" stroke="#ef4444" stroke-width="3"/>
      <rect x="70" y="100" width="200" height="140" rx="16" fill="#fff" stroke="#f87171" stroke-width="2"/>
      
      <!-- X grande sobre os documentos -->
      <line x1="90" y1="130" x2="230" y2="230" stroke="#dc2626" stroke-width="12" stroke-linecap="round"/>
      <line x1="230" y1="130" x2="90" y2="230" stroke="#dc2626" stroke-width="12" stroke-linecap="round"/>
      
      <!-- Ícones de problemas -->
      <circle cx="50" cy="50" r="25" fill="#dc2626"/>
      <text x="50" y="60" text-anchor="middle" font-size="24" fill="#fff" font-weight="bold">!</text>
      
      <circle cx="250" cy="60" r="25" fill="#f59e0b"/>
      <text x="250" y="70" text-anchor="middle" font-size="20" fill="#fff">⚠️</text>
      
      <circle cx="280" cy="180" r="20" fill="#ef4444"/>
      <text x="280" y="188" text-anchor="middle" font-size="16" fill="#fff">✕</text>
      
      <!-- Documentos voando/dispersos -->
      <rect x="20" y="260" width="60" height="30" rx="6" fill="#f87171" opacity="0.7" transform="rotate(-15 50 275)"/>
      <rect x="240" y="250" width="50" height="25" rx="4" fill="#fca5a5" opacity="0.6" transform="rotate(20 265 262)"/>
      <rect x="10" y="180" width="40" height="20" rx="4" fill="#dc2626" opacity="0.5" transform="rotate(-30 30 190)"/>
      
      <!-- Linhas de texto nos documentos -->
      <rect x="90" y="140" width="120" height="6" rx="3" fill="#ef4444" opacity="0.3"/>
      <rect x="90" y="155" width="100" height="6" rx="3" fill="#dc2626" opacity="0.3"/>
      <rect x="90" y="170" width="140" height="6" rx="3" fill="#f87171" opacity="0.3"/>
      <rect x="90" y="185" width="80" height="6" rx="3" fill="#ef4444" opacity="0.3"/>
      
      <!-- Efeito de "papel rasgado" -->
      <path d="M70 95 L90 85 L110 95 L130 85 L150 95 L170 85 L190 95 L210 85 L230 95 L250 85 L270 95" 
            stroke="#dc2626" stroke-width="3" fill="none"/>
    </svg>  </div>
</div>

<div class="navigation">
  <a href="slide-01-capa.html" class="nav-button">← Anterior</a>
  <a href="index.html" class="nav-button">📋 Índice</a>
  <a href="slide-03-solucao.html" class="nav-button">Próximo →</a>
</div>

<script>
document.addEventListener('keydown', function(e) {
  switch(e.key) {
    case 'ArrowLeft':
      window.location.href = 'slide-01-capa.html';
      break;
    case 'ArrowRight':
      window.location.href = 'slide-03-solucao.html';
      break;
    case 'Home':
      window.location.href = 'index.html';
      break;
    case 'F11':
      e.preventDefault();
      if (document.fullscreenElement) {
        document.exitFullscreen();
      } else {
        document.documentElement.requestFullscreen();
      }
      break;
  }
});
</script>

</body>
</html>
