# 🗺️ Google Maps vs Google Timeline - <PERSON><PERSON><PERSON>

## ❓ **Qual a diferença?**

### **Google Maps** 📱
- **O que é**: Aplicativo de navegação e mapas
- **Função**: Mostra rotas, lugares, navegação GPS
- **Dados**: Mapas atuais, tr<PERSON><PERSON><PERSON>, pontos de interesse
- **Uso no sistema**: NÃO usamos diretamente

### **Google Timeline** 📍
- **O que é**: Histórico de localização dentro do Google Maps
- **Função**: Armazena onde você esteve ao longo do tempo
- **Dados**: Histórico de lugares visitados, tempo de permanência
- **Uso no sistema**: **É ISSO que nosso sistema acessa!**

## 🔍 **Como o usuário acessa o Timeline?**

### **No Celular (Android/iPhone):**
1. Abrir **Google Maps**
2. Tocar na **foto do perfil** (canto superior direito)
3. Selecionar **"Sua timeline"**
4. Ver histórico de lugares visitados

### **No Computador:**
1. Ir para [timeline.google.com](https://timeline.google.com)
2. Fazer login na conta Google
3. Ver mapa com histórico de localizações

## ⚙️ **Pré-requisitos para funcionar:**

### **1. Timeline deve estar ATIVADO:**
- **Android**: Configurações > Google > Dados da conta > Dados de localização
- **iPhone**: Google Maps > Perfil > Configurações de dados > Histórico de localizações

### **2. Histórico de Localização:**
```
✅ Ativado: Sistema funciona
❌ Desativado: Sistema NÃO funciona
```

### **3. Dados disponíveis:**
- Mínimo 15-30 dias de histórico
- Dados precisos de GPS/Wi-Fi
- Permanência em locais por períodos longos

## 🔧 **APIs que nosso sistema usa:**

### **Timeline API (Location History)**
```typescript
// URL da API real do Google Timeline
https://timeline.googleapis.com/v1/users/me/timeline

// Escopos necessários
'https://www.googleapis.com/auth/timeline.readonly'
```

### **OAuth2 Scopes necessários:**
```javascript
const scopes = [
  'https://www.googleapis.com/auth/userinfo.email',
  'https://www.googleapis.com/auth/userinfo.profile', 
  'https://www.googleapis.com/auth/timeline.readonly', // 👈 ESSENCIAL!
];
```

## 📱 **Fluxo no nosso sistema:**

```mermaid
graph TD
    A[Usuário acessa sistema] --> B[Clica "Autorizar Google"]
    B --> C[Redirect para OAuth Google]
    C --> D[Google pede permissão Timeline]
    D --> E[Usuário autoriza]
    E --> F[Sistema coleta dados Timeline]
    F --> G[Algoritmo analisa permanência]
    G --> H[Gera certificado se válido]
```

## ⚠️ **Limitações importantes:**

### **1. Timeline precisa estar ativo:**
- Muitos usuários têm desabilitado por privacidade
- Sistema deve orientar como ativar

### **2. API pode ter restrições:**
- Google pode limitar acesso a dados Timeline
- Verificar documentação oficial regularmente

### **3. Qualidade dos dados:**
- Depende de GPS/Wi-Fi do dispositivo
- Locais internos podem ter menos precisão

## 🛠️ **Para desenvolvedores:**

### **Configuração no Google Cloud:**
1. Ativar **Timeline API** (não Maps API comum)
2. Configurar OAuth2 com escopos corretos
3. Adicionar domínio autorizado

### **Teste de funcionamento:**
```bash
# Verificar se usuário tem Timeline ativo
curl -H "Authorization: Bearer $TOKEN" \
  "https://timeline.googleapis.com/v1/users/me/timeline?startTime=2024-01-01"
```

### **Fallback se Timeline não disponível:**
- Permitir upload manual de dados
- Integração com outras fontes de localização
- Sistema híbrido de validação

## 🎯 **Resumo para usuários:**

1. **Timeline ≠ Google Maps comum**
2. **Timeline = Histórico de onde você esteve**
3. **Precisa estar ATIVADO no seu celular**
4. **Nosso sistema lê esse histórico para validar residência**

---

💡 **Dica**: Se o usuário nunca usou Timeline, não terá dados históricos suficientes para validação!
