/**
 * Serviço de OCR e processamento de imagem
 * Utiliza Tesseract.js para reconhecimento óptico de caracteres
 * e Sharp para pré-processamento de imagens
 */
const { createWorker, createScheduler, PSM, OEM } = require('tesseract.js');
const sharp = require('sharp');
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const logger = require('../utils/logger');

// Linguagens suportadas
const SUPPORTED_LANGUAGES = {
  pt: 'por',  // Português
  en: 'eng',  // Inglês
  es: 'spa'   // Espanhol
};

// Classe para gerenciar o processamento OCR
class OCRService {
  constructor() {
    this.scheduler = createScheduler();
    this.workers = {};
    this.initialized = false;
    this.initPromise = null;
  }

  /**
   * Inicializa os workers do OCR para as linguagens suportadas
   */
  async initialize() {
    if (this.initialized) return;
    
    // Se já existe uma inicialização em andamento, aguarde
    if (this.initPromise) return this.initPromise;
    
    this.initPromise = (async () => {
      try {
        logger.info('Inicializando workers de OCR...');
        
        for (const [key, lang] of Object.entries(SUPPORTED_LANGUAGES)) {
          const worker = await createWorker(lang);
          
          // Configurar parâmetros do Tesseract
          await worker.setParameters({
            tessedit_ocr_engine_mode: OEM.LSTM_ONLY,
            tessedit_pageseg_mode: PSM.AUTO,
            tessedit_char_whitelist: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789.,;:!?()[]{}#@%$&*+-/\\\'"`~<>=_áàâãéèêíìîóòôõúùûçÁÀÂÃÉÈÊÍÌÎÓÒÔÕÚÙÛÇ ',
            preserve_interword_spaces: '1',
          });
          
          this.workers[key] = worker;
          this.scheduler.addWorker(worker);
          logger.info(`Worker OCR inicializado para ${key} (${lang})`);
        }
        
        this.initialized = true;
        logger.info('Todos os workers OCR inicializados com sucesso');
      } catch (error) {
        logger.error('Erro ao inicializar workers OCR:', error);
        throw error;
      }
    })();
    
    return this.initPromise;
  }

  /**
   * Pré-processa a imagem para melhorar a precisão do OCR
   * @param {string} imagePath - Caminho da imagem original
   * @param {object} options - Opções de processamento
   * @returns {Promise<string>} - Caminho da imagem processada
   */
  async preprocessImage(imagePath, options = {}) {
    const {
      grayscale = true,
      threshold = false,
      thresholdValue = 128,
      normalize = true,
      sharpen = true,
      removeNoise = true,
      resize = false,
      width = 1800,
      contrast = 1.3,
      brightness = 1.1
    } = options;
    
    try {
      logger.info(`Pré-processando imagem: ${imagePath}`);
      const outputPath = path.join(path.dirname(imagePath), `preprocessed_${path.basename(imagePath)}`);
      
      let pipeline = sharp(imagePath);
      
      if (grayscale) {
        pipeline = pipeline.grayscale();
      }
      
      if (normalize) {
        pipeline = pipeline.normalize();
      }
      
      if (sharpen) {
        pipeline = pipeline.sharpen({
          sigma: 1.5,
          flat: 1.0,
          jagged: 0.7
        });
      }
      
      if (threshold) {
        pipeline = pipeline.threshold(thresholdValue);
      }
      
      if (resize) {
        pipeline = pipeline.resize({
          width: width,
          fit: 'inside',
          withoutEnlargement: true
        });
      }
      
      // Ajustar contraste e brilho
      if (contrast !== 1 || brightness !== 1) {
        pipeline = pipeline.modulate({
          brightness: brightness,
          saturation: 1.0
        });
        
        if (contrast !== 1) {
          pipeline = pipeline.linear(contrast, -(128 * contrast) + 128);
        }
      }
      
      await pipeline.toFile(outputPath);
      logger.info(`Imagem pré-processada salva em: ${outputPath}`);
      
      return outputPath;
    } catch (error) {
      logger.error('Erro no pré-processamento da imagem:', error);
      throw error;
    }
  }

  /**
   * Realiza o OCR em uma imagem usando todas as linguagens configuradas
   * @param {string} imagePath - Caminho da imagem para processamento
   * @param {Array<string>} languages - Lista de códigos de idioma (pt, en, es)
   * @param {object} preprocessOptions - Opções para pré-processamento
   * @returns {Promise<object>} - Resultado do OCR com texto e informações de confiança
   */
  async processImage(imagePath, languages = ['pt'], preprocessOptions = {}) {
    try {
      await this.initialize();
      
      // Verificar se o arquivo existe
      if (!fs.existsSync(imagePath)) {
        throw new Error(`Arquivo de imagem não encontrado: ${imagePath}`);
      }
      
      // Pré-processar a imagem para melhorar o OCR
      const processedImagePath = await this.preprocessImage(imagePath, preprocessOptions);
      
      // Filtrar apenas idiomas suportados
      const validLanguages = languages.filter(lang => SUPPORTED_LANGUAGES[lang]);
      
      if (validLanguages.length === 0) {
        validLanguages.push('pt'); // Default para português
      }
      
      // Processar em todos os idiomas solicitados
      const results = {};
      let bestResult = { text: '', confidence: 0 };
      
      for (const lang of validLanguages) {
        const worker = this.workers[lang];
        if (!worker) continue;
        
        const result = await worker.recognize(processedImagePath);
        results[lang] = {
          text: result.data.text,
          confidence: result.data.confidence,
          words: result.data.words.map(w => ({
            text: w.text,
            confidence: w.confidence,
            bbox: w.bbox
          }))
        };
        
        // Registrar o resultado com maior confiança
        if (result.data.confidence > bestResult.confidence) {
          bestResult = {
            language: lang,
            text: result.data.text,
            confidence: result.data.confidence
          };
        }
      }
      
      // Limpar a imagem pré-processada
      try {
        fs.unlinkSync(processedImagePath);
      } catch (err) {
        logger.warn(`Não foi possível excluir a imagem pré-processada: ${processedImagePath}`);
      }
      
      return {
        results,
        bestResult,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Erro no processamento OCR:', error);
      throw error;
    }
  }

  /**
   * Extrai texto de uma captura de tela ou imagem
   * @param {string|Buffer} imageInput - Caminho do arquivo ou buffer da imagem
   * @param {string[]} languages - Idiomas a serem usados (pt, en, es)
   * @returns {Promise<object>} - Resultado da extração de texto
   */
  async extractTextFromImage(imageInput, languages = ['pt']) {
    try {
      let imagePath;
      
      // Se for um buffer, salvar como arquivo temporário
      if (Buffer.isBuffer(imageInput)) {
        const tempDir = path.join(__dirname, '../../temp');
        
        // Criar diretório temporário se não existir
        if (!fs.existsSync(tempDir)) {
          fs.mkdirSync(tempDir, { recursive: true });
        }
        
        imagePath = path.join(tempDir, `temp_image_${uuidv4()}.png`);
        await fs.promises.writeFile(imagePath, imageInput);
      } else {
        imagePath = imageInput;
      }
      
      const result = await this.processImage(imagePath, languages);
      
      // Limpar arquivo temporário se foi criado de um buffer
      if (Buffer.isBuffer(imageInput)) {
        try {
          fs.unlinkSync(imagePath);
        } catch (err) {
          logger.warn(`Não foi possível excluir a imagem temporária: ${imagePath}`);
        }
      }
      
      return result;
    } catch (error) {
      logger.error('Erro na extração de texto da imagem:', error);
      throw error;
    }
  }

  /**
   * Finaliza todos os workers do OCR
   */
  async terminate() {
    try {
      if (!this.initialized) return;
      
      for (const worker of Object.values(this.workers)) {
        await worker.terminate();
      }
      
      this.workers = {};
      this.initialized = false;
      logger.info('Todos os workers OCR foram finalizados');
    } catch (error) {
      logger.error('Erro ao finalizar workers OCR:', error);
    }
  }
}

// Exportar a classe
module.exports = OCRService;
