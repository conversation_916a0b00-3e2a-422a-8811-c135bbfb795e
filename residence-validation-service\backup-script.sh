#!/bin/bash
# ==============================================================================
# Script de Backup Automático para PostgreSQL
# ==============================================================================

set -e

# Configurações
BACKUP_DIR="/backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="$BACKUP_DIR/residencia_db_backup_$TIMESTAMP.sql"
RETENTION_DAYS=7

# Criar diretório de backup se não existir
mkdir -p $BACKUP_DIR

echo "Iniciando backup do banco de dados..."

# Executar backup
pg_dump -h $PGHOST -U $POSTGRES_USER -d $POSTGRES_DB > $BACKUP_FILE

# Comprimir backup
gzip $BACKUP_FILE

echo "Backup criado: ${BACKUP_FILE}.gz"

# Remover backups antigos
find $BACKUP_DIR -name "*.gz" -type f -mtime +$RETENTION_DAYS -delete

echo "Backup concluído e backups antigos removidos."

# Verificar integridade do backup
if [ -f "${BACKUP_FILE}.gz" ]; then
    echo "Backup verificado com sucesso!"
else
    echo "ERRO: Backup não foi criado corretamente!"
    exit 1
fi
