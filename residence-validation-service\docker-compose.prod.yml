# ==============================================================================
# Docker Compose para Sistema de Validação de Residência - PRODUÇÃO
# ==============================================================================
version: '3.8'

services:  # Aplicação Next.js  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: sistema-residencia-app
    restart: unless-stopped
    ports:
      - "3000:3000"
    env_file:
      - .env.production
    environment:
      # Sobrescrever variáveis críticas do arquivo .env
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
      # Garantir que variáveis OAuth sejam passadas explicitamente
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID:-}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET:-}
      - NEXT_PUBLIC_GOOGLE_REDIRECT_URI=${NEXT_PUBLIC_GOOGLE_REDIRECT_URI:-}
      - JWT_SECRET=${JWT_SECRET:-}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY:-}
      - NEXT_PUBLIC_BASE_URL=${NEXT_PUBLIC_BASE_URL:-}
      - DATABASE_URL=${DATABASE_URL:-}
      - CORS_ORIGIN=${CORS_ORIGIN:-}
    networks:
      - sistema-residencia-network
    depends_on:
      - postgres
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    volumes:
      # Volume para logs
      - ./logs:/app/logs
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.sistema-residencia.rule=Host(`seu-dominio.com`)"
      - "traefik.http.routers.sistema-residencia.tls=true"
      - "traefik.http.routers.sistema-residencia.tls.certresolver=letsencrypt"

  # Banco de Dados PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: sistema-residencia-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: residencia_db
      POSTGRES_USER: residencia_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - sistema-residencia-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U residencia_user -d residencia_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    ports:
      - "5432:5432" # Remover em produção por segurança

  # Redis para Cache e Sessões
  redis:
    image: redis:7-alpine
    container_name: sistema-residencia-redis
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - sistema-residencia-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: sistema-residencia-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
      - nginx_logs:/var/log/nginx
    networks:
      - sistema-residencia-network
    depends_on:
      - app

  # Backup Automático
  backup:
    image: postgres:15-alpine
    container_name: sistema-residencia-backup
    restart: unless-stopped
    environment:
      POSTGRES_DB: residencia_db
      POSTGRES_USER: residencia_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      PGHOST: postgres
    volumes:
      - ./backups:/backups
      - ./backup-script.sh:/backup-script.sh
    command: |
      sh -c "
        echo '0 2 * * * /backup-script.sh' | crontab -
        crond -f
      "
    networks:
      - sistema-residencia-network
    depends_on:
      - postgres

# Volumes persistentes
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  nginx_logs:
    driver: local

# Rede interna
networks:
  sistema-residencia-network:
    driver: bridge
