import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  AppBar,
  Toolbar,
  Typography,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Box,
  IconButton,
  useTheme,
  useMediaQuery,
  Divider,
  Badge,
  Tooltip,
  Avatar,
  Chip,
  alpha
} from '@mui/material';
import {
  Menu as MenuIcon,
  Home,
  VideoCall,
  VideoLibrary,
  AdminPanelSettings,
  Person,
  Settings,
  Security,
  ExitToApp,
  Gavel,
  Analytics,
  DarkMode,
  LightMode,
  TextFields,
  Notifications,
  Search,
  LocationOn
} from '@mui/icons-material';
import { useTheme as useCustomTheme } from '../contexts/ThemeContext';

interface MainLayoutProps {
  children: React.ReactNode;
}

const drawerWidth = 280;

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const [mobileOpen, setMobileOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const theme = useTheme();
  const { mode, toggleTheme } = useCustomTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const menuItems = [
    {
      text: 'Dashboard',
      icon: <Home />,
      path: '/',
      color: 'primary',
      description: 'Visão geral do sistema'
    },
    {
      text: 'Nova Gravação',
      icon: <VideoCall />,
      path: '/record',
      color: 'success',
      description: 'Iniciar nova gravação',
      highlight: true
    },
    {
      text: 'Minhas Gravações',
      icon: <VideoLibrary />,
      path: '/recordings',
      color: 'info',
      description: 'Gerenciar gravações',
      badge: 3
    },
    {
      text: 'Analytics',
      icon: <Analytics />,
      path: '/analytics',
      color: 'warning',
      description: 'Relatórios e métricas'
    },
    {
      text: 'OCR e Extração',
      icon: <TextFields />,
      path: '/ocr',
      color: 'secondary',
      description: 'Reconhecimento de texto'
    },
    {
      text: 'Validação de Residência',
      icon: <LocationOn />,
      path: '/residence',
      color: 'info',
      description: 'Validação de comprovante de residência'
    },
    {
      text: 'Administração',
      icon: <AdminPanelSettings />,
      path: '/admin',
      color: 'error',
      description: 'Painel administrativo',
      adminOnly: true
    },
    {
      text: 'Perfil',
      icon: <Person />,
      path: '/profile',
      color: 'default',
      description: 'Configurações do usuário'
    },
    {
      text: 'Configurações',
      icon: <Settings />,
      path: '/settings',
      color: 'default',
      description: 'Configurações do sistema'
    },
  ];

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleNavigation = (path: string) => {
    navigate(path);
    if (isMobile) {
      setMobileOpen(false);
    }
  };

  const drawer = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{
        p: 3,
        background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
        color: 'white'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
          <Avatar sx={{
            bgcolor: 'rgba(255,255,255,0.2)',
            width: 48,
            height: 48,
            backdropFilter: 'blur(10px)'
          }}>
            <Gavel sx={{ fontSize: 28 }} />
          </Avatar>
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 700, mb: 0.5 }}>
              CartórioTech
            </Typography>
            <Chip
              label="Pro"
              size="small"
              sx={{
                bgcolor: 'rgba(255,255,255,0.2)',
                color: 'white',
                fontSize: '0.75rem',
                height: 20
              }}
            />
          </Box>
        </Box>

        {/* User Info */}
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 1.5,
          p: 1.5,
          bgcolor: 'rgba(255,255,255,0.1)',
          borderRadius: 2,
          backdropFilter: 'blur(10px)'
        }}>
          <Avatar sx={{ width: 32, height: 32, bgcolor: 'rgba(255,255,255,0.2)' }}>
            J
          </Avatar>
          <Box sx={{ flex: 1, minWidth: 0 }}>
            <Typography variant="body2" sx={{ fontWeight: 500, color: 'white' }}>
              João Silva
            </Typography>
            <Typography variant="caption" sx={{ color: 'rgba(255,255,255,0.8)' }}>
              Administrador
            </Typography>
          </Box>
          <IconButton size="small" sx={{ color: 'rgba(255,255,255,0.8)' }}>
            <Notifications fontSize="small" />
          </IconButton>
        </Box>
      </Box>
      
      {/* Navigation */}
      <Box sx={{ flex: 1, px: 2, py: 1 }}>
        <List sx={{ py: 0 }}>
          {menuItems.map((item, index) => (
            <ListItem key={item.text} disablePadding sx={{ mb: 0.5 }}>
              <ListItemButton
                selected={location.pathname === item.path}
                onClick={() => handleNavigation(item.path)}
                sx={{
                  borderRadius: 2,
                  mx: 1,
                  minHeight: 48,
                  transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                  '&.Mui-selected': {
                    backgroundColor: alpha(theme.palette.primary.main, 0.1),
                    color: theme.palette.primary.main,
                    '&:hover': {
                      backgroundColor: alpha(theme.palette.primary.main, 0.15),
                    },
                    '& .MuiListItemIcon-root': {
                      color: theme.palette.primary.main,
                    },
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      left: 0,
                      top: '50%',
                      transform: 'translateY(-50%)',
                      width: 3,
                      height: 24,
                      backgroundColor: theme.palette.primary.main,
                      borderRadius: '0 2px 2px 0',
                    }
                  },
                  '&:hover': {
                    backgroundColor: alpha(theme.palette.action.hover, 0.08),
                    transform: 'translateX(4px)',
                  },
                  ...(item.highlight && {
                    background: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.1)} 0%, ${alpha(theme.palette.success.light, 0.05)} 100%)`,
                    border: `1px solid ${alpha(theme.palette.success.main, 0.2)}`,
                  })
                }}
              >
                <ListItemIcon sx={{ minWidth: 40 }}>
                  {item.badge ? (
                    <Badge badgeContent={item.badge} color="error" variant="dot">
                      {item.icon}
                    </Badge>
                  ) : (
                    item.icon
                  )}
                </ListItemIcon>
                <ListItemText
                  primary={item.text}
                  secondary={!isMobile ? item.description : undefined}
                  primaryTypographyProps={{
                    fontSize: '0.9rem',
                    fontWeight: location.pathname === item.path ? 600 : 500
                  }}
                  secondaryTypographyProps={{
                    fontSize: '0.75rem',
                    sx: { mt: 0.5 }
                  }}
                />
                {item.highlight && (
                  <Chip
                    label="Novo"
                    size="small"
                    color="success"
                    sx={{
                      height: 20,
                      fontSize: '0.7rem',
                      '& .MuiChip-label': { px: 1 }
                    }}
                  />
                )}
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </Box>

      {/* Footer */}
      <Box sx={{ p: 2, borderTop: `1px solid ${theme.palette.divider}` }}>
        <ListItemButton
          onClick={() => navigate('/login')}
          sx={{
            borderRadius: 2,
            color: theme.palette.error.main,
            '&:hover': {
              backgroundColor: alpha(theme.palette.error.main, 0.08),
            }
          }}
        >
          <ListItemIcon sx={{ color: 'inherit', minWidth: 40 }}>
            <ExitToApp />
          </ListItemIcon>
          <ListItemText
            primary="Sair"
            primaryTypographyProps={{ fontSize: '0.9rem', fontWeight: 500 }}
          />
        </ListItemButton>
      </Box>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex' }}>
      <AppBar
        position="fixed"
        sx={{
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          ml: { sm: `${drawerWidth}px` },
          zIndex: (theme) => theme.zIndex.drawer + 1,
          backgroundColor: 'rgba(255,255,255,0.8)',
          backdropFilter: 'blur(20px)',
          borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
          color: theme.palette.text.primary,
        }}
        elevation={0}
      >
        <Toolbar sx={{ minHeight: '72px !important' }}>
          <IconButton
            color="inherit"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{
              mr: 2,
              display: { sm: 'none' },
              bgcolor: alpha(theme.palette.primary.main, 0.1),
              '&:hover': {
                bgcolor: alpha(theme.palette.primary.main, 0.2),
              }
            }}
          >
            <MenuIcon />
          </IconButton>

          {/* Search Bar */}
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            flexGrow: 1,
            maxWidth: 600,
            mr: 3
          }}>
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              bgcolor: alpha(theme.palette.grey[100], 0.8),
              borderRadius: 3,
              px: 2,
              py: 1,
              flex: 1,
              border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
              '&:hover': {
                bgcolor: alpha(theme.palette.grey[100], 1),
                borderColor: alpha(theme.palette.primary.main, 0.3),
              },
              '&:focus-within': {
                bgcolor: alpha(theme.palette.grey[100], 1),
                borderColor: theme.palette.primary.main,
                boxShadow: `0 0 0 2px ${alpha(theme.palette.primary.main, 0.1)}`,
              }
            }}>
              <Search sx={{ color: theme.palette.text.secondary, mr: 1 }} />
              <input
                placeholder="Buscar gravações, documentos..."
                style={{
                  border: 'none',
                  outline: 'none',
                  background: 'transparent',
                  flex: 1,
                  fontSize: '0.95rem',
                  color: theme.palette.text.primary,
                }}
              />
            </Box>
          </Box>

          {/* Right Actions */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Tooltip title="Notificações">
              <IconButton
                sx={{
                  color: theme.palette.text.secondary,
                  '&:hover': {
                    bgcolor: alpha(theme.palette.primary.main, 0.1),
                    color: theme.palette.primary.main,
                  }
                }}
              >
                <Badge badgeContent={2} color="error" variant="dot">
                  <Notifications />
                </Badge>
              </IconButton>
            </Tooltip>

            <Tooltip title={`Alternar para modo ${mode === 'light' ? 'escuro' : 'claro'}`}>
              <IconButton
                onClick={toggleTheme}
                sx={{
                  color: theme.palette.text.secondary,
                  '&:hover': {
                    bgcolor: alpha(theme.palette.primary.main, 0.1),
                    color: theme.palette.primary.main,
                  }
                }}
              >
                {mode === 'light' ? <DarkMode /> : <LightMode />}
              </IconButton>
            </Tooltip>

            <Chip
              label="LGPD Compliant"
              size="small"
              icon={<Security />}
              sx={{
                bgcolor: alpha(theme.palette.success.main, 0.1),
                color: theme.palette.success.main,
                border: `1px solid ${alpha(theme.palette.success.main, 0.2)}`,
                display: { xs: 'none', md: 'flex' }
              }}
            />
          </Box>
        </Toolbar>
      </AppBar>

      <Box
        component="nav"
        sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
      >
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true,
          }}
          sx={{
            display: { xs: 'block', sm: 'none' },
            '& .MuiDrawer-paper': { 
              boxSizing: 'border-box', 
              width: drawerWidth,
              height: '100%'
            },
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', sm: 'block' },
            '& .MuiDrawer-paper': { 
              boxSizing: 'border-box', 
              width: drawerWidth,
              height: '100%'
            },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          mt: '72px',
          minHeight: 'calc(100vh - 72px)',
          backgroundColor: theme.palette.background.default,
          position: 'relative',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '200px',
            background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.03)} 100%)`,
            zIndex: -1,
          }
        }}
      >
        <Box sx={{
          p: { xs: 2, sm: 3, md: 4 },
          maxWidth: '1400px',
          mx: 'auto'
        }}>
          {children}
        </Box>
      </Box>
    </Box>
  );
};

export default MainLayout;
