# Instruções para o GitHub Copilot

<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

## Contexto do Projeto

Este é um **Sistema Integrador de Validação de Residência** que utiliza dados da Linha do Tempo do Google Maps para validar comprovantes de residência automaticamente.

### Funcionalidades Principais:
1. **Autorização OAuth2** para acessar Google Maps Timeline API
2. **Coleta e Processamento** de dados de localização
3. **Algoritmos de Validação** que verificam permanência no mesmo local das 18h às 6h por pelo menos 15 dias consecutivos
4. **Geração Automática** de comprovantes de residência em PDF
5. **Compliance LGPD** com criptografia e segurança de dados

### Tecnologias Utilizadas:
- **Frontend**: Next.js 15 + TypeScript + Tailwind CSS
- **Backend**: Next.js API Routes
- **Integração**: Google Maps Timeline API + OAuth2
- **Validação**: Algoritmos personalizados de geolocalização
- **Geração PDF**: jsPDF/PDFKit
- **Banco de Dados**: PostgreSQL/MongoDB (a definir)
- **Segurança**: JWT, bcrypt, HTTPS

### Padrões de Código:
- Use **TypeScript** para type safety
- Siga **princípios SOLID** e **clean architecture**
- Implemente **tratamento de erros** robusto
- Use **componentes reutilizáveis** React
- Aplique **responsive design** mobile-first
- Mantenha **código limpo** e bem documentado

### Considerações de Segurança:
- **Nunca** exponha chaves de API no frontend
- Use **variáveis de ambiente** para credenciais
- Implemente **rate limiting** nas APIs
- **Criptografe** dados sensíveis
- Valide **todas as entradas** do usuário
- Siga **princípios LGPD** para privacidade

### Estrutura de Pastas:
```
src/
├── app/                 # App Router (Next.js 13+)
├── components/          # Componentes React reutilizáveis
├── lib/                 # Utilitários e configurações
├── types/              # Definições TypeScript
├── utils/              # Funções auxiliares
└── styles/             # Estilos globais
```

### APIs a Implementar:
- `/api/auth/google` - Autenticação OAuth2
- `/api/timeline/collect` - Coleta dados do Google
- `/api/validation/residence` - Validação de residência
- `/api/certificate/generate` - Geração de comprovantes
- `/api/user/data` - Gerenciamento de dados do usuário
