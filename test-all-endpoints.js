#!/usr/bin/env node

/**
 * Script para testar todos os endpoints dos serviços CartorioTech
 */

const axios = require('axios');

const BACKEND_URL = 'http://localhost:3001';
const AI_SERVICE_URL = 'http://localhost:3002';

// Configurar timeout
axios.defaults.timeout = 10000;

async function testEndpoint(url, method = 'GET', data = null, description = '') {
  try {
    console.log(`🧪 Testando: ${description || `${method} ${url}`}`);
    
    const config = {
      method,
      url,
      validateStatus: () => true // Aceitar qualquer status
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    
    if (response.status === 200 && response.data.success !== false) {
      console.log(`✅ OK - Status: ${response.status}`);
      if (response.data.service) {
        console.log(`   Serviço: ${response.data.service}`);
      }
      if (response.data.availableEndpoints) {
        console.log(`   Endpoints: ${Object.keys(response.data.availableEndpoints).length}`);
      }
      return true;
    } else {
      console.log(`❌ FALHOU - Status: ${response.status}`);
      if (response.data.message) {
        console.log(`   Erro: ${response.data.message}`);
      }
      return false;
    }
  } catch (error) {
    console.log(`💥 ERRO DE CONEXÃO`);
    console.log(`   ${error.message}`);
    return false;
  }
}

async function testBackendEndpoints() {
  console.log('\n🔧 TESTANDO BACKEND ENDPOINTS\n');
  
  const endpoints = [
    { url: `${BACKEND_URL}/health`, desc: 'Health Check' },
    { url: `${BACKEND_URL}/api`, desc: 'API Info' },
    { url: `${BACKEND_URL}/api/analytics/dashboard`, desc: 'Analytics Dashboard' },
    { url: `${BACKEND_URL}/api/analytics/trends?period=month`, desc: 'Analytics Trends' },
    { url: `${BACKEND_URL}/api/analytics/users`, desc: 'Analytics Users' },
    { url: `${BACKEND_URL}/api/analytics/compliance`, desc: 'Analytics Compliance' },
    { url: `${BACKEND_URL}/api/digital-signature`, desc: 'Digital Signature Info' },
    { url: `${BACKEND_URL}/api/recordings`, desc: 'Recordings' },
    { url: `${BACKEND_URL}/api/reports`, desc: 'Reports' }
  ];
  
  let passed = 0;
  let total = endpoints.length;
  
  for (const endpoint of endpoints) {
    const success = await testEndpoint(endpoint.url, 'GET', null, endpoint.desc);
    if (success) passed++;
    console.log('');
  }
  
  console.log(`📊 Backend: ${passed}/${total} endpoints funcionando\n`);
  return { passed, total };
}

async function testAIServiceEndpoints() {
  console.log('🤖 TESTANDO AI SERVICE ENDPOINTS\n');
  
  const endpoints = [
    { url: `${AI_SERVICE_URL}/health`, desc: 'AI Health Check' },
    { url: `${AI_SERVICE_URL}/api`, desc: 'AI API Info' },
    { url: `${AI_SERVICE_URL}/api/ai`, desc: 'AI Service Info' },
    { url: `${AI_SERVICE_URL}/api/ai/transcription`, desc: 'Transcription Info' },
    { url: `${AI_SERVICE_URL}/api/ai/semantic-search`, desc: 'Semantic Search Info' },
    { url: `${AI_SERVICE_URL}/api/ai/compliance`, desc: 'Compliance Info' },
    { url: `${AI_SERVICE_URL}/api/ai/quality`, desc: 'Quality Info' },
    { url: `${AI_SERVICE_URL}/api/ai/analytics`, desc: 'AI Analytics Info' },
    { url: `${AI_SERVICE_URL}/api/ai/analytics/summary`, desc: 'AI Analytics Summary' },
    { url: `${AI_SERVICE_URL}/api/ai/ocr`, desc: 'OCR Info' }
  ];
  
  let passed = 0;
  let total = endpoints.length;
  
  for (const endpoint of endpoints) {
    const success = await testEndpoint(endpoint.url, 'GET', null, endpoint.desc);
    if (success) passed++;
    console.log('');
  }
  
  console.log(`📊 AI Service: ${passed}/${total} endpoints funcionando\n`);
  return { passed, total };
}

async function testServiceConnectivity() {
  console.log('🔌 TESTANDO CONECTIVIDADE DOS SERVIÇOS\n');
  
  const services = [
    { name: 'Backend', url: `${BACKEND_URL}/health` },
    { name: 'AI Service', url: `${AI_SERVICE_URL}/health` }
  ];
  
  for (const service of services) {
    try {
      const response = await axios.get(service.url, { timeout: 5000 });
      if (response.status === 200) {
        console.log(`✅ ${service.name}: ONLINE`);
        if (response.data.uptime) {
          console.log(`   Uptime: ${Math.round(response.data.uptime)}s`);
        }
        if (response.data.memory) {
          console.log(`   Memória: ${response.data.memory.used}MB/${response.data.memory.total}MB`);
        }
      } else {
        console.log(`⚠️  ${service.name}: RESPOSTA ANÔMALA (${response.status})`);
      }
    } catch (error) {
      console.log(`❌ ${service.name}: OFFLINE`);
      console.log(`   Erro: ${error.message}`);
    }
    console.log('');
  }
}

async function testSpecificEndpoints() {
  console.log('🎯 TESTANDO ENDPOINTS ESPECÍFICOS\n');
  
  // Testar endpoint que estava falhando
  console.log('🔍 Testando endpoints que estavam com problema...\n');
  
  const problematicEndpoints = [
    { 
      url: `${AI_SERVICE_URL}/api/ai/transcription/nonexistent`, 
      desc: 'Endpoint inexistente (deve retornar 404 com info)',
      expectError: true
    },
    { 
      url: `${BACKEND_URL}/api/digital-signature/nonexistent`, 
      desc: 'Endpoint inexistente backend (deve retornar 404 com info)',
      expectError: true
    }
  ];
  
  for (const endpoint of problematicEndpoints) {
    try {
      const response = await axios.get(endpoint.url, { 
        timeout: 5000,
        validateStatus: () => true 
      });
      
      console.log(`🧪 ${endpoint.desc}`);
      console.log(`   Status: ${response.status}`);
      
      if (response.status === 404) {
        if (response.data.availableEndpoints) {
          console.log(`✅ OK - Retorna lista de endpoints disponíveis`);
          console.log(`   Endpoints listados: ${Object.keys(response.data.availableEndpoints).length}`);
        } else {
          console.log(`⚠️  404 sem informações úteis`);
        }
      } else {
        console.log(`⚠️  Status inesperado: ${response.status}`);
      }
      
    } catch (error) {
      console.log(`💥 Erro: ${error.message}`);
    }
    console.log('');
  }
}

async function generateReport(backendResults, aiResults) {
  console.log('📋 RELATÓRIO FINAL\n');
  
  const totalPassed = backendResults.passed + aiResults.passed;
  const totalEndpoints = backendResults.total + aiResults.total;
  const successRate = Math.round((totalPassed / totalEndpoints) * 100);
  
  console.log(`📊 ESTATÍSTICAS GERAIS:`);
  console.log(`   Total de endpoints testados: ${totalEndpoints}`);
  console.log(`   Endpoints funcionando: ${totalPassed}`);
  console.log(`   Taxa de sucesso: ${successRate}%`);
  console.log('');
  
  console.log(`🔧 BACKEND:`);
  console.log(`   Funcionando: ${backendResults.passed}/${backendResults.total}`);
  console.log(`   Taxa: ${Math.round((backendResults.passed / backendResults.total) * 100)}%`);
  console.log('');
  
  console.log(`🤖 AI SERVICE:`);
  console.log(`   Funcionando: ${aiResults.passed}/${aiResults.total}`);
  console.log(`   Taxa: ${Math.round((aiResults.passed / aiResults.total) * 100)}%`);
  console.log('');
  
  if (successRate >= 90) {
    console.log('🎉 EXCELENTE! Todos os serviços estão funcionando bem.');
  } else if (successRate >= 70) {
    console.log('✅ BOM! A maioria dos endpoints está funcionando.');
  } else if (successRate >= 50) {
    console.log('⚠️  ATENÇÃO! Alguns endpoints precisam de correção.');
  } else {
    console.log('❌ CRÍTICO! Muitos endpoints não estão funcionando.');
  }
  
  console.log('\n💡 DICAS:');
  console.log('   - Verifique se todos os serviços estão rodando');
  console.log('   - Execute: docker-compose ps');
  console.log('   - Verifique logs: docker-compose logs [service]');
  console.log('   - Para rebuild: docker-compose build --no-cache');
}

async function main() {
  console.log('🚀 TESTE COMPLETO DE ENDPOINTS - CARTORIOTECH\n');
  console.log('=' .repeat(60));
  
  // Testar conectividade básica
  await testServiceConnectivity();
  
  // Testar endpoints do backend
  const backendResults = await testBackendEndpoints();
  
  // Testar endpoints do AI service
  const aiResults = await testAIServiceEndpoints();
  
  // Testar endpoints específicos
  await testSpecificEndpoints();
  
  // Gerar relatório final
  await generateReport(backendResults, aiResults);
  
  console.log('\n✨ Teste concluído!');
}

// Executar se chamado diretamente
if (require.main === module) {
  main().catch(error => {
    console.error('💥 Erro fatal no teste:', error.message);
    process.exit(1);
  });
}

module.exports = { testBackendEndpoints, testAIServiceEndpoints };
