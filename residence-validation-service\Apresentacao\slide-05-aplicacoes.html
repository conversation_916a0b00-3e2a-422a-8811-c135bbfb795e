<!DOCTYPE html>
<html lang="pt-br">
<head>
  <meta charset="UTF-8">
  <title>ProvaSegura - Slide 5: Aplica<PERSON><PERSON><PERSON>rá<PERSON></title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');
    
    body {
      margin: 0;
      padding: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      font-family: 'Inter', 'Segoe UI', Arial, sans-serif;
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }
      .slide {
      width: 90vw;
      max-width: 1280px;
      height: 80vh;
      max-height: 720px;
      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
      box-shadow: 0 20px 60px rgba(0,0,0,0.15), 0 8px 32px rgba(0,0,0,0.08);
      border-radius: 24px;
      overflow: hidden;
      position: relative;
      border: 1px solid rgba(255,255,255,0.2);
      padding: 40px 60px;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
    
    .slide::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
      border-radius: 24px 24px 0 0;
    }
      .slide-title {
      font-size: 2.5rem;
      font-weight: 800;
      background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-bottom: 30px;
      line-height: 1.1;
      letter-spacing: -0.02em;
      text-align: center;
    }      .use-cases {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 25px;
      margin-top: 20px;
    }
      .use-case {
      background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
      border-radius: 16px;
      padding: 25px 20px;
      text-align: center;
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.1);
      border: 2px solid rgba(102, 126, 234, 0.1);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }
      .use-case::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      border-radius: 16px 16px 0 0;
    }
    
    .use-case:hover {
      transform: translateY(-8px);
      box-shadow: 0 20px 40px rgba(102, 126, 234, 0.2);
    }
      .use-case-rh::before {
      background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
    }
    
    .use-case-bens::before {
      background: linear-gradient(90deg, #10b981 0%, #047857 100%);
    }
    
    .use-case-judicial::before {
      background: linear-gradient(90deg, #8b5cf6 0%, #6d28d9 100%);
    }
    
    .use-case-residencia::before {
      background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%);
    }
      .use-case-icon {
      width: 60px;
      height: 60px;
      border-radius: 15px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 2rem;
      color: #fff;
      margin: 0 auto 18px;
      box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    }
    
    .icon-rh {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    }
    
    .icon-bens {
      background: linear-gradient(135deg, #10b981 0%, #047857 100%);
    }
      .icon-judicial {
      background: linear-gradient(135deg, #8b5cf6 0%, #6d28d9 100%);
    }
    
    .icon-residencia {
      background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    }
      .use-case-title {
      font-size: 1.2rem;
      font-weight: 700;
      color: #1e293b;
      margin-bottom: 12px;
    }
    
    .use-case-description {
      font-size: 0.9rem;
      color: #475569;
      line-height: 1.5;
      margin-bottom: 15px;
    }
    
    .use-case-example {
      background: rgba(102, 126, 234, 0.05);
      padding: 12px;
      border-radius: 8px;
      font-size: 0.8rem;
      color: #334155;
      font-style: italic;
      border-left: 3px solid #667eea;
    }
    
    .slide-number {
      position: absolute;
      top: 30px;
      right: 40px;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-weight: 700;
      font-size: 1.3rem;
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }    @media print {
      body { background: #fff; }
      .slide { box-shadow: none; border: 1px solid #e2e8f0; }
    }

    @media (max-width: 768px) {
      .slide {
        width: 95vw;
        height: 90vh;
        padding: 30px 20px;
      }
      
      .slide-title {
        font-size: 2rem;
      }      
      .use-cases {
        grid-template-columns: 1fr;
        gap: 20px;
      }
      
      .use-case {
        padding: 20px 15px;
      }
      
      .use-case-icon {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
        margin-bottom: 15px;
      }
      
      .use-case-title {
        font-size: 1.1rem;
      }
      
      .use-case-description {
        font-size: 0.85rem;
      }
    }

    .navigation {
      position: fixed;
      bottom: 30px;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      gap: 15px;
      z-index: 1000;
    }

    .nav-button {
      background: rgba(255, 255, 255, 0.9);
      border: none;
      padding: 12px 24px;
      border-radius: 25px;
      color: #1e293b;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      text-decoration: none;
      display: inline-block;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .nav-button:hover {
      background: white;
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }
  </style>
</head>
<body>

<div class="slide">
  <div class="slide-number">5</div>
  <div class="slide-title">Onde ProvaSegura Faz a Diferença</div>
    <div class="use-cases">
    <div class="use-case use-case-rh">
      <div class="use-case-icon icon-rh">👥</div>
      <div class="use-case-title">Recursos Humanos</div>
      <div class="use-case-description">
        Evita fraudes em contratações, garantindo que candidatos moram onde declaram.
      </div>
      <div class="use-case-example">
        <strong>Exemplo:</strong> Uma empresa valida que um funcionário mora em São Paulo, não em uma área de risco distante.
      </div>
    </div>
    
    <div class="use-case use-case-bens">
      <div class="use-case-icon icon-bens">🏠</div>
      <div class="use-case-title">Bens de Alto Valor</div>
      <div class="use-case-description">
        Acelera financiamentos de carros e imóveis com comprovantes digitais.
      </div>
      <div class="use-case-example">
        <strong>Exemplo:</strong> Um cliente financia um carro em minutos com um relatório ProvaSegura.
      </div>
    </div>
    
    <div class="use-case use-case-judicial">
      <div class="use-case-icon icon-judicial">⚖️</div>
      <div class="use-case-title">Sistema Judicial</div>
      <div class="use-case-description">
        Fornece provas confiáveis para álibis ou disputas legais.
      </div>
      <div class="use-case-example">
        <strong>Exemplo:</strong> Um réu prova que estava na farmácia, não no local de um crime.
      </div>
    </div>
    
    <div class="use-case use-case-residencia">
      <div class="use-case-icon icon-residencia">📍</div>
      <div class="use-case-title">Comprovante de Residência</div>
      <div class="use-case-description">
        Elimina a necessidade de contas de luz ou documentos tradicionais com prova digital inequívoca.
      </div>
      <div class="use-case-example">
        <strong>Exemplo:</strong> Um cliente abre conta bancária instantaneamente com relatório de 30 dias comprovando residência.
      </div>
    </div>  </div>
</div>

<div class="navigation">
  <a href="slide-04-mercado.html" class="nav-button">← Anterior</a>
  <a href="index.html" class="nav-button">Índice</a>
  <a href="slide-06-diferencial.html" class="nav-button">Próximo →</a>
</div>

<script>
document.addEventListener('keydown', function(event) {
  if (event.key === 'ArrowLeft') {
    window.location.href = 'slide-04-mercado.html';
  } else if (event.key === 'ArrowRight') {
    window.location.href = 'slide-06-diferencial.html';
  } else if (event.key === 'Escape') {
    window.location.href = 'index.html';
  }
});
</script>

</body>
</html>
