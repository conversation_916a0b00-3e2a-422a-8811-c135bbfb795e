'use client';

import { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Alert,
  Checkbox,
  FormControlLabel,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Paper,
  Grid
} from '@mui/material';
import {
  Security,
  LocationOn,
  CheckCircle,
  Warning,
  Info,
  Gavel,
  Assignment,
  Shield
} from '@mui/icons-material';

interface ConsentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAccept: () => void;
}

export const ConsentModal = ({ isOpen, onClose, onAccept }: ConsentModalProps) => {
  const [hasReadTerms, setHasReadTerms] = useState(false);
  const [hasReadPrivacy, setHasReadPrivacy] = useState(false);

  const canProceed = hasReadTerms && hasReadPrivacy;

  return (
    <Dialog 
      open={isOpen} 
      onClose={onClose}
      maxWidth="md" 
      fullWidth
      PaperProps={{
        sx: { borderRadius: 2 }
      }}
    >
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Security color="primary" />
        <Typography variant="h6">
          Consentimento para Coleta de Dados
        </Typography>
      </DialogTitle>

      <DialogContent dividers>
        <Box sx={{ mb: 3 }}>
          <Alert severity="warning" icon={<Gavel />}>
            <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
              Importante - Validade Legal
            </Typography>
            <Typography variant="body2">
              O comprovante gerado é PRELIMINAR e só possui validade legal após autenticação em cartório.
            </Typography>
          </Alert>
        </Box>

        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Paper elevation={1} sx={{ p: 2, height: '100%' }}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <LocationOn color="primary" />
                Dados que Vamos Acessar
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <CheckCircle color="success" fontSize="small" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Histórico de localização"
                    secondary="Google Timeline (onde você esteve e quando)"
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <CheckCircle color="success" fontSize="small" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Informações básicas"
                    secondary="Nome e email da sua conta Google"
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <CheckCircle color="success" fontSize="small" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Endereço informado"
                    secondary="Para validação de residência"
                  />
                </ListItem>
              </List>
            </Paper>
          </Grid>

          <Grid item xs={12} md={6}>
            <Paper elevation={1} sx={{ p: 2, height: '100%' }}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Shield color="primary" />
                Como Usamos seus Dados
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <CheckCircle color="success" fontSize="small" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Analisar padrões de permanência"
                    secondary="No endereço (18h às 6h)"
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <CheckCircle color="success" fontSize="small" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Calcular tempo de residência"
                    secondary="Baseado em dados de localização"
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <CheckCircle color="success" fontSize="small" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Gerar comprovante preliminar"
                    secondary="Para apresentação em cartório"
                  />
                </ListItem>
              </List>
            </Paper>
          </Grid>
        </Grid>

        <Box sx={{ mt: 3 }}>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Info color="primary" />
            Garantias de Segurança
          </Typography>
          <Alert severity="info" sx={{ mb: 2 }}>
            <List dense>
              <ListItem disablePadding>
                <ListItemText primary="🔒 Dados processados apenas durante a validação" />
              </ListItem>
              <ListItem disablePadding>
                <ListItemText primary="🗑️ Informações excluídas após geração do comprovante" />
              </ListItem>
              <ListItem disablePadding>
                <ListItemText primary="🚫 Não compartilhamos dados com terceiros" />
              </ListItem>
              <ListItem disablePadding>
                <ListItemText primary="🔐 Conexão segura e criptografada" />
              </ListItem>
            </List>
          </Alert>
        </Box>

        <Divider sx={{ my: 2 }} />

        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <FormControlLabel
            control={
              <Checkbox
                checked={hasReadTerms}
                onChange={(e) => setHasReadTerms(e.target.checked)}
                color="primary"
              />
            }
            label={
              <Typography variant="body2">
                Li e concordo com os{' '}
                <Button
                  variant="text"
                  size="small"
                  href="/termos"
                  target="_blank"
                  sx={{ p: 0, textTransform: 'none' }}
                >
                  Termos de Uso
                </Button>
              </Typography>
            }
          />

          <FormControlLabel
            control={
              <Checkbox
                checked={hasReadPrivacy}
                onChange={(e) => setHasReadPrivacy(e.target.checked)}
                color="primary"
              />
            }
            label={
              <Typography variant="body2">
                Li e concordo com a{' '}
                <Button
                  variant="text"
                  size="small"
                  href="/privacidade"
                  target="_blank"
                  sx={{ p: 0, textTransform: 'none' }}
                >
                  Política de Privacidade
                </Button>
              </Typography>
            }
          />

          <Alert severity="warning">
            <Typography variant="body2">
              <strong>Importante:</strong> Você pode revogar este consentimento a qualquer momento.
              Seus dados serão excluídos imediatamente após a revogação.
            </Typography>
          </Alert>
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 2 }}>
        <Button 
          onClick={onClose}
          variant="outlined"
          startIcon={<Warning />}
        >
          Cancelar
        </Button>
        <Button
          onClick={onAccept}
          variant="contained"
          disabled={!canProceed}
          startIcon={<CheckCircle />}
        >
          Autorizar Acesso
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ConsentModal;
