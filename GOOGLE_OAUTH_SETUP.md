# 🔧 Configuração Google OAuth - Sistema de Validação de Residência

## ❌ Problema Atual
O erro `"Erro ao gerar URL de autorização"` acontece porque as credenciais do Google OAuth não estão configuradas.

## 🏗️ Passo 1: Configurar Google Cloud Console

### 1.1 Acessar o Console
1. Vá para [Google Cloud Console](https://console.cloud.google.com)
2. Faça login com sua conta Google
3. Selecione ou crie um projeto

### 1.2 Habilitar APIs Necessárias
1. Vá para **APIs & Services** → **Library**
2. Procure e habilite:
   - **Google Maps Timeline API** (se disponível)
   - **Google+ API** (para informações do usuário)
   - **Google OAuth2 API**

### 1.3 Configurar OAuth Consent Screen
1. Vá para **APIs & Services** → **OAuth consent screen**
2. Escolha **External** (para testes)
3. Preencha:
   - **App name**: CartorioTech - Validação de Residência
   - **User support email**: seu email
   - **Developer contact information**: seu email
4. Clique **Save and Continue**
5. Em **Scopes**, adicione:
   - `userinfo.email`
   - `userinfo.profile`
   - `timeline.readonly` (se disponível)
6. Clique **Save and Continue**
7. Em **Test users**, adicione seu email para testes
8. Clique **Save and Continue**

### 1.4 Criar Credenciais OAuth2
1. Vá para **APIs & Services** → **Credentials**
2. Clique **+ CREATE CREDENTIALS** → **OAuth 2.0 Client IDs**
3. Escolha **Web application**
4. Preencha:
   - **Name**: CartorioTech Residence Validation
   - **Authorized JavaScript origins**: 
     - `http://localhost:3000`
     - `http://localhost:3001`
   - **Authorized redirect URIs**:
     - `http://localhost:3001/api/residence/auth/callback`
5. Clique **CREATE**
6. **COPIE** o Client ID e Client Secret que aparecem

## 🔧 Passo 2: Configurar Variáveis de Ambiente

### 2.1 Editar arquivo .env
Abra o arquivo `.env` na raiz do projeto e substitua:

```env
# Google OAuth (Para validação de residência)
GOOGLE_CLIENT_ID=123456789-abcdefghijklmnop.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_REDIRECT_URI=http://localhost:3001/api/residence/auth/callback
```

**⚠️ IMPORTANTE**: Substitua pelos valores REAIS copiados do Google Cloud Console!

## 🚀 Passo 3: Testar a Configuração

### 3.1 Reiniciar o Backend
```bash
docker-compose restart backend
```

### 3.2 Testar a API
Abra no navegador ou use curl:
```bash
curl http://localhost:3001/api/residence/auth/google
```

**Resposta esperada:**
```json
{
  "success": true,
  "authUrl": "https://accounts.google.com/oauth/authorize?...",
  "message": "Redirecione o usuário para esta URL para autorizar acesso"
}
```

### 3.3 Testar no Frontend
1. Acesse `http://localhost:3000/residence`
2. Clique no botão **"🏢 Gerar Link para Cliente (Cartório)"**
3. Preencha os dados do cliente
4. Gere o link
5. Acesse o link gerado
6. Clique em **"Autorizar com Google"**
7. Deve redirecionar para o Google OAuth

## 🔍 Solução de Problemas

### Erro: "OAuth client was not found"
- Verifique se o Client ID está correto no .env
- Verifique se o projeto no Google Cloud Console está ativo

### Erro: "redirect_uri_mismatch"
- Verifique se a URL `http://localhost:3001/api/residence/auth/callback` está nas "Authorized redirect URIs"
- URLs devem ser EXATAMENTE iguais (incluindo http/https)

### Erro: "access_denied"
- Adicione seu email em "Test users" no OAuth consent screen
- Verifique se as APIs necessárias estão habilitadas

### Erro: "invalid_scope"
- O escopo `timeline.readonly` pode não estar disponível
- Use apenas `userinfo.email` e `userinfo.profile` para testes iniciais

## 📋 Checklist Final

- [ ] Projeto criado no Google Cloud Console
- [ ] APIs habilitadas (pelo menos Google+ API)
- [ ] OAuth consent screen configurado
- [ ] Credenciais OAuth2 criadas
- [ ] Client ID e Secret copiados para .env
- [ ] Redirect URI configurada corretamente
- [ ] Backend reiniciado
- [ ] Teste da API funcionando
- [ ] Frontend consegue gerar links
- [ ] Autorização Google funciona

## 🎯 Resultado Esperado

Após seguir este guia:
- ✅ Botão "Gerar Link para Cliente" funciona
- ✅ Links únicos são gerados para clientes
- ✅ Cliente consegue autorizar com Google
- ✅ Processo de validação de residência funciona

---

**💡 DICA**: Para produção, você precisará:
1. Domínio próprio (ex: `https://cartorio.com.br`)
2. Certificado SSL
3. Configurar URLs de produção no Google Cloud Console
4. Submeter app para revisão do Google (se usar escopos sensíveis)

**🔒 SEGURANÇA**: 
- Nunca commite o arquivo .env com credenciais reais
- Use credenciais diferentes para desenvolvimento e produção
- Mantenha o Client Secret sempre privado
