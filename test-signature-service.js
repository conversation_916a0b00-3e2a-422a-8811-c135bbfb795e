#!/usr/bin/env node

/**
 * Script para testar especificamente o signature-service na porta 3004
 */

const axios = require('axios');

const SIGNATURE_SERVICE_URL = 'http://localhost:3004';

// Configurar timeout
axios.defaults.timeout = 10000;

async function testEndpoint(url, method = 'GET', data = null, description = '') {
  try {
    console.log(`🧪 Testando: ${description || `${method} ${url}`}`);
    
    const config = {
      method,
      url,
      validateStatus: () => true // Aceitar qualquer status
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    
    console.log(`   Status: ${response.status}`);
    
    if (response.status === 200 && response.data.success !== false) {
      console.log(`✅ OK`);
      if (response.data.service) {
        console.log(`   Serviço: ${response.data.service}`);
      }
      if (response.data.availableEndpoints) {
        console.log(`   Endpoints: ${Object.keys(response.data.availableEndpoints).length}`);
      }
      if (response.data.features) {
        console.log(`   Features: ${response.data.features.length}`);
      }
      return true;
    } else {
      console.log(`❌ FALHOU`);
      if (response.data.message) {
        console.log(`   Erro: ${response.data.message}`);
      }
      return false;
    }
  } catch (error) {
    console.log(`💥 ERRO DE CONEXÃO`);
    console.log(`   ${error.message}`);
    if (error.code === 'ECONNREFUSED') {
      console.log(`   🔍 O serviço pode não estar rodando na porta 3004`);
      console.log(`   💡 Execute: docker-compose up signature-service`);
    }
    return false;
  }
}

async function testSignatureServiceConnectivity() {
  console.log('🔌 TESTANDO CONECTIVIDADE DO SIGNATURE SERVICE\n');
  
  try {
    const response = await axios.get(`${SIGNATURE_SERVICE_URL}/health`, { timeout: 5000 });
    if (response.status === 200) {
      console.log(`✅ Signature Service: ONLINE`);
      console.log(`   Status: ${response.data.status}`);
      if (response.data.uptime) {
        console.log(`   Uptime: ${Math.round(response.data.uptime)}s`);
      }
      if (response.data.memory) {
        console.log(`   Memória: ${response.data.memory.used}MB/${response.data.memory.total}MB`);
      }
      console.log(`   Versão: ${response.data.version}`);
      return true;
    } else {
      console.log(`⚠️  Signature Service: RESPOSTA ANÔMALA (${response.status})`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Signature Service: OFFLINE`);
    console.log(`   Erro: ${error.message}`);
    if (error.code === 'ECONNREFUSED') {
      console.log(`   🔍 Serviço não está rodando na porta 3004`);
    }
    return false;
  }
}

async function testSignatureServiceEndpoints() {
  console.log('\n🔏 TESTANDO ENDPOINTS DO SIGNATURE SERVICE\n');
  
  const endpoints = [
    { url: `${SIGNATURE_SERVICE_URL}/health`, desc: 'Health Check' },
    { url: `${SIGNATURE_SERVICE_URL}/api`, desc: 'API Info' },
    { url: `${SIGNATURE_SERVICE_URL}/api/signature`, desc: 'Signature Service Info' },
    { url: `${SIGNATURE_SERVICE_URL}/api/timestamp`, desc: 'Timestamp Service Info' },
    { url: `${SIGNATURE_SERVICE_URL}/api/certificate`, desc: 'Certificate Service Info' },
    { url: `${SIGNATURE_SERVICE_URL}/api-docs`, desc: 'API Documentation' }
  ];
  
  let passed = 0;
  let total = endpoints.length;
  
  for (const endpoint of endpoints) {
    const success = await testEndpoint(endpoint.url, 'GET', null, endpoint.desc);
    if (success) passed++;
    console.log('');
  }
  
  console.log(`📊 Signature Service: ${passed}/${total} endpoints funcionando\n`);
  return { passed, total };
}

async function testNonExistentEndpoints() {
  console.log('🎯 TESTANDO ENDPOINTS INEXISTENTES (DEVE RETORNAR 404 COM INFO)\n');
  
  const nonExistentEndpoints = [
    { 
      url: `${SIGNATURE_SERVICE_URL}/api/nonexistent`, 
      desc: 'Endpoint inexistente (deve retornar 404 com info)'
    },
    { 
      url: `${SIGNATURE_SERVICE_URL}/api/signature/nonexistent`, 
      desc: 'Endpoint de assinatura inexistente'
    }
  ];
  
  for (const endpoint of nonExistentEndpoints) {
    try {
      const response = await axios.get(endpoint.url, { 
        timeout: 5000,
        validateStatus: () => true 
      });
      
      console.log(`🧪 ${endpoint.desc}`);
      console.log(`   Status: ${response.status}`);
      
      if (response.status === 404) {
        if (response.data.availableEndpoints) {
          console.log(`✅ OK - Retorna lista de endpoints disponíveis`);
          console.log(`   Endpoints listados: ${Object.keys(response.data.availableEndpoints).length}`);
        } else {
          console.log(`⚠️  404 sem informações úteis`);
        }
      } else {
        console.log(`⚠️  Status inesperado: ${response.status}`);
      }
      
    } catch (error) {
      console.log(`💥 Erro: ${error.message}`);
    }
    console.log('');
  }
}

async function checkDockerContainer() {
  console.log('🐳 VERIFICANDO CONTAINER DOCKER\n');
  
  const { exec } = require('child_process');
  
  return new Promise((resolve) => {
    exec('docker ps --filter "name=signature-service" --format "table {{.Names}}\\t{{.Status}}\\t{{.Ports}}"', (error, stdout, stderr) => {
      if (error) {
        console.log('❌ Erro ao verificar containers Docker');
        console.log(`   ${error.message}`);
        resolve(false);
        return;
      }
      
      if (stdout.includes('signature-service')) {
        console.log('✅ Container signature-service encontrado:');
        console.log(stdout);
        resolve(true);
      } else {
        console.log('❌ Container signature-service não encontrado');
        console.log('💡 Execute: docker-compose up -d signature-service');
        resolve(false);
      }
    });
  });
}

async function generateDiagnosticReport(results) {
  console.log('📋 RELATÓRIO DIAGNÓSTICO - SIGNATURE SERVICE\n');
  
  const successRate = Math.round((results.passed / results.total) * 100);
  
  console.log(`📊 ESTATÍSTICAS:`);
  console.log(`   Endpoints testados: ${results.total}`);
  console.log(`   Endpoints funcionando: ${results.passed}`);
  console.log(`   Taxa de sucesso: ${successRate}%`);
  console.log('');
  
  if (successRate >= 90) {
    console.log('🎉 EXCELENTE! O signature-service está funcionando perfeitamente.');
  } else if (successRate >= 70) {
    console.log('✅ BOM! A maioria dos endpoints está funcionando.');
  } else if (successRate >= 50) {
    console.log('⚠️  ATENÇÃO! Alguns endpoints precisam de correção.');
  } else {
    console.log('❌ CRÍTICO! O signature-service não está funcionando adequadamente.');
  }
  
  console.log('\n🔧 COMANDOS ÚTEIS:');
  console.log('   docker-compose ps signature-service');
  console.log('   docker-compose logs signature-service');
  console.log('   docker-compose restart signature-service');
  console.log('   docker-compose build --no-cache signature-service');
  
  console.log('\n🌐 URLS PARA TESTAR:');
  console.log(`   Health: ${SIGNATURE_SERVICE_URL}/health`);
  console.log(`   API Info: ${SIGNATURE_SERVICE_URL}/api`);
  console.log(`   Docs: ${SIGNATURE_SERVICE_URL}/api-docs`);
}

async function main() {
  console.log('🚀 TESTE ESPECÍFICO - SIGNATURE SERVICE (PORTA 3004)\n');
  console.log('=' .repeat(60));
  
  // Verificar container Docker
  const containerRunning = await checkDockerContainer();
  console.log('');
  
  // Testar conectividade básica
  const isOnline = await testSignatureServiceConnectivity();
  console.log('');
  
  if (!isOnline) {
    console.log('💡 DICAS PARA RESOLVER:');
    console.log('   1. Verifique se o container está rodando: docker-compose ps');
    console.log('   2. Inicie o serviço: docker-compose up -d signature-service');
    console.log('   3. Verifique os logs: docker-compose logs signature-service');
    console.log('   4. Rebuild se necessário: docker-compose build signature-service');
    return;
  }
  
  // Testar endpoints
  const results = await testSignatureServiceEndpoints();
  
  // Testar endpoints inexistentes
  await testNonExistentEndpoints();
  
  // Gerar relatório
  await generateDiagnosticReport(results);
  
  console.log('\n✨ Teste concluído!');
}

// Executar se chamado diretamente
if (require.main === module) {
  main().catch(error => {
    console.error('💥 Erro fatal no teste:', error.message);
    process.exit(1);
  });
}

module.exports = { testSignatureServiceEndpoints };
