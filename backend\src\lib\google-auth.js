const { google } = require('googleapis');
const { OAuth2Client } = require('google-auth-library');

// Configuração do Google OAuth
const CONFIG = {
  GOOGLE: {
    CLIENT_ID: process.env.GOOGLE_CLIENT_ID || '',
    CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET || '',
    REDIRECT_URI: process.env.GOOGLE_REDIRECT_URI || 'http://localhost:3001/api/residence/auth/callback',
    SCOPES: [
      'https://www.googleapis.com/auth/userinfo.email',
      'https://www.googleapis.com/auth/userinfo.profile',
      'https://www.googleapis.com/auth/location.timeline.readonly',
    ],
  },
  JWT: {
    SECRET: process.env.JWT_SECRET || 'default_secret_key',
  },
};

class GoogleAuthService {
  constructor() {
    this.oauth2Client = new google.auth.OAuth2(
      CONFIG.GOOGLE.CLIENT_ID,
      CONFIG.GOOGLE.CLIENT_SECRET,
      CONFIG.GOOGLE.REDIRECT_URI
    );
  }

  /**
   * Gera URL de autenticação do Google
   */
  getAuthUrl() {
    return this.oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: CONFIG.GOOGLE.SCOPES,
      prompt: 'consent',
    });
  }

  /**
   * Troca código de autorização por tokens
   */
  async getTokens(code) {
    try {
      const { tokens } = await this.oauth2Client.getToken(code);
      return tokens;
    } catch (error) {
      console.error('Erro ao obter tokens:', error);
      throw error;
    }
  }

  /**
   * Define credenciais do cliente OAuth
   */
  setCredentials(tokens) {
    this.oauth2Client.setCredentials(tokens);
  }

  /**
   * Obtém informações do usuário autenticado
   */
  async getUserInfo() {
    const oauth2 = google.oauth2({ version: 'v2', auth: this.oauth2Client });
    const { data } = await oauth2.userinfo.get();
    return data;
  }

  /**
   * Acessa os dados do Google Timeline (histórico de localização)
   * IMPORTANTE: Esta é a API que acessa o histórico de onde o usuário esteve,
   * não o Google Maps comum. É necessário que o usuário tenha o Timeline ativado.
   */
  async getTimelineData(startDate, endDate) {
    try {
      // A Timeline API real do Google usa um endpoint diferente
      // Nota: A API oficial pode ter mudanças, verifique a documentação mais recente
      const response = await fetch(
        `https://timeline.googleapis.com/v1/users/me/timeline?startTime=${startDate}&endTime=${endDate}`,
        {
          headers: {
            'Authorization': `Bearer ${this.oauth2Client.credentials.access_token}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Timeline API error: ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Erro ao acessar Timeline API:', error);
      throw error;
    }
  }

  /**
   * Método alternativo usando a API do Google My Activity
   * (pode ser mais estável que a Timeline API)
   */
  async getLocationHistory(startDate, endDate) {
    try {
      // Usando a API do Google My Activity para acessar dados de localização
      const myActivity = google.myactivity({ version: 'v1', auth: this.oauth2Client });
      
      const response = await myActivity.activity.search({
        requestBody: {
          filter: {
            activityType: 'LOCATION_HISTORY',
            startTime: new Date(startDate).toISOString(),
            endTime: new Date(endDate).toISOString(),
          },
        },
      });

      return response.data;
    } catch (error) {
      console.error('Erro ao acessar My Activity API:', error);
      throw error;
    }
  }

  /**
   * Refresh do token de acesso
   */
  async refreshToken() {
    try {
      const { credentials } = await this.oauth2Client.refreshAccessToken();
      this.oauth2Client.setCredentials(credentials);
      return credentials;
    } catch (error) {
      console.error('Erro ao renovar token:', error);
      throw error;
    }
  }

  /**
   * Verifica se o token ainda é válido
   */
  isTokenValid() {
    const { expiry_date } = this.oauth2Client.credentials;
    if (!expiry_date) return false;
    return Date.now() < expiry_date;
  }

  /**
   * Revoga o token de acesso
   */
  async revokeToken() {
    try {
      await this.oauth2Client.revokeCredentials();
    } catch (error) {
      console.error('Erro ao revogar token:', error);
      throw error;
    }
  }
}

// Instância singleton do serviço
const googleAuthService = new GoogleAuthService();

module.exports = googleAuthService;
module.exports.GoogleAuthService = GoogleAuthService;
