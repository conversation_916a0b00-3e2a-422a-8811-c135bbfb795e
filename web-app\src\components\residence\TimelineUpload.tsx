import React, { useState, useCallback } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Typo<PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>abel,
  StepContent,
  LinearProgress,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  IconButton,
  Link
} from '@mui/material';
import {
  CloudUpload,
  GetApp,
  CheckCircle,
  Warning,
  Info,
  Close,
  FolderZip,
  LocationOn,
  Schedule,
  OpenInNew
} from '@mui/icons-material';
import { useDropzone } from 'react-dropzone';

interface TimelineUploadProps {
  open: boolean;
  onClose: () => void;
  onUploadComplete?: (data: any) => void;
}

interface UploadedFile {
  file: File;
  name: string;
  size: number;
  type: string;
}

interface ParsedTimelineData {
  totalLocations: number;
  dateRange: {
    start: string;
    end: string;
  };
  locations: Array<{
    lat: number;
    lon: number;
    timestamp: string;
    accuracy?: number;
  }>;
  summary: {
    uniqueDays: number;
    nighttimeLocations: number;
    averageAccuracy: number;
  };
}

const TimelineUpload: React.FC<TimelineUploadProps> = ({
  open,
  onClose,
  onUploadComplete
}) => {
  const [activeStep, setActiveStep] = useState(0);
  const [uploadedFile, setUploadedFile] = useState<UploadedFile | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingProgress, setProcessingProgress] = useState(0);
  const [parsedData, setParsedData] = useState<ParsedTimelineData | null>(null);
  const [error, setError] = useState<string | null>(null);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file) {
      setUploadedFile({
        file,
        name: file.name,
        size: file.size,
        type: file.type
      });
      setError(null);
      setActiveStep(1);
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/json': ['.json'],
      'application/zip': ['.zip'],
      'application/x-zip-compressed': ['.zip']
    },
    maxFiles: 1,
    maxSize: 100 * 1024 * 1024, // 100MB
  });

  const processTimelineFile = async () => {
    if (!uploadedFile) return;

    setIsProcessing(true);
    setProcessingProgress(0);
    setError(null);

    try {
      // Simular progresso de processamento
      const progressInterval = setInterval(() => {
        setProcessingProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      // Processar arquivo
      let timelineData;
      
      if (uploadedFile.type === 'application/json') {
        // Arquivo JSON direto
        const text = await uploadedFile.file.text();
        timelineData = JSON.parse(text);
      } else {
        // Arquivo ZIP (Google Takeout)
        // Em produção, usar biblioteca como JSZip para extrair
        throw new Error('Processamento de arquivos ZIP ainda não implementado. Use arquivos JSON por enquanto.');
      }

      // Processar dados do Timeline
      const processedData = await processTimelineData(timelineData);
      
      clearInterval(progressInterval);
      setProcessingProgress(100);
      setParsedData(processedData);
      setActiveStep(2);

    } catch (err) {
      console.error('Erro ao processar arquivo:', err);
      setError(err instanceof Error ? err.message : 'Erro desconhecido ao processar arquivo');
    } finally {
      setIsProcessing(false);
    }
  };

  const processTimelineData = async (rawData: any): Promise<ParsedTimelineData> => {
    // Simular processamento dos dados do Timeline
    // Em produção, implementar parser real dos dados do Google Takeout
    
    await new Promise(resolve => setTimeout(resolve, 1000)); // Simular processamento

    // Estrutura esperada do Google Takeout Timeline
    const locations = rawData.timelineObjects || rawData.locations || [];
    
    if (!Array.isArray(locations) || locations.length === 0) {
      throw new Error('Nenhum dado de localização encontrado no arquivo');
    }

    // Processar localizações
    const processedLocations = locations
      .filter((item: any) => item.placeVisit || item.activitySegment)
      .map((item: any) => {
        const location = item.placeVisit?.location || item.activitySegment?.startLocation;
        const timestamp = item.placeVisit?.duration?.startTimestampMs || 
                         item.activitySegment?.duration?.startTimestampMs;
        
        if (!location || !timestamp) return null;

        return {
          lat: location.latitudeE7 / 1e7,
          lon: location.longitudeE7 / 1e7,
          timestamp: new Date(parseInt(timestamp)).toISOString(),
          accuracy: location.accuracy
        };
      })
      .filter(Boolean)
      .slice(0, 10000); // Limitar para performance

    if (processedLocations.length === 0) {
      throw new Error('Nenhuma localização válida encontrada nos dados');
    }

    // Calcular estatísticas
    const validLocations = processedLocations.filter((loc): loc is NonNullable<typeof loc> => loc != null);
    const timestamps = validLocations.map(loc => new Date(loc.timestamp));
    const uniqueDays = new Set(timestamps.map(ts => ts.toDateString())).size;
    
    // Contar localizações noturnas (18h às 6h)
    const nighttimeLocations = validLocations.filter(loc => {
      const hour = new Date(loc.timestamp).getHours();
      return hour >= 18 || hour <= 6;
    }).length;

    // Calcular precisão média
    const accuracyValues = validLocations
      .map(loc => loc.accuracy)
      .filter(acc => acc && acc > 0);
    const averageAccuracy = accuracyValues.length > 0 
      ? accuracyValues.reduce((sum, acc) => sum + acc, 0) / accuracyValues.length
      : 0;

    return {
      totalLocations: validLocations.length,
      dateRange: {
        start: Math.min(...timestamps.map(ts => ts.getTime())).toString(),
        end: Math.max(...timestamps.map(ts => ts.getTime())).toString()
      },
      locations: validLocations,
      summary: {
        uniqueDays,
        nighttimeLocations,
        averageAccuracy: Math.round(averageAccuracy)
      }
    };
  };

  const handleConfirmUpload = () => {
    if (parsedData) {
      onUploadComplete?.(parsedData);
      onClose();
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const steps = [
    'Selecionar Arquivo',
    'Processar Dados',
    'Confirmar Upload'
  ];

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { minHeight: '60vh' }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <CloudUpload color="primary" />
            <Typography variant="h6">
              Upload Manual do Timeline
            </Typography>
          </Box>
          <IconButton onClick={onClose}>
            <Close />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        {/* Instruções Iniciais */}
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="body2" sx={{ mb: 1 }}>
            <strong>Como obter seus dados do Timeline:</strong>
          </Typography>
          <Typography variant="body2" component="div">
            1. Acesse{' '}
            <Link href="https://takeout.google.com" target="_blank" rel="noopener">
              Google Takeout
              <OpenInNew sx={{ fontSize: 14, ml: 0.5 }} />
            </Link>
            <br />
            2. Selecione apenas "Maps (seus lugares)"
            <br />
            3. Escolha formato JSON e baixe o arquivo
            <br />
            4. Faça upload do arquivo aqui
          </Typography>
        </Alert>

        {/* Stepper */}
        <Stepper activeStep={activeStep} orientation="vertical">
          {/* Passo 1: Selecionar Arquivo */}
          <Step>
            <StepLabel>Selecionar Arquivo</StepLabel>
            <StepContent>
              <Box
                {...getRootProps()}
                sx={{
                  border: '2px dashed',
                  borderColor: isDragActive ? 'primary.main' : 'grey.300',
                  borderRadius: 2,
                  p: 4,
                  textAlign: 'center',
                  cursor: 'pointer',
                  bgcolor: isDragActive ? 'action.hover' : 'background.paper',
                  transition: 'all 0.2s ease-in-out',
                  '&:hover': {
                    borderColor: 'primary.main',
                    bgcolor: 'action.hover'
                  }
                }}
              >
                <input {...getInputProps()} />
                <FolderZip sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  {isDragActive ? 'Solte o arquivo aqui' : 'Arraste o arquivo ou clique para selecionar'}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Formatos aceitos: JSON, ZIP (até 100MB)
                </Typography>
              </Box>

              {uploadedFile && (
                <Card sx={{ mt: 2 }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <FolderZip color="primary" />
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="body1" sx={{ fontWeight: 500 }}>
                          {uploadedFile.name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {formatFileSize(uploadedFile.size)}
                        </Typography>
                      </Box>
                      <Chip 
                        label="Pronto" 
                        color="success" 
                        size="small"
                        icon={<CheckCircle />}
                      />
                    </Box>
                  </CardContent>
                </Card>
              )}

              {uploadedFile && (
                <Box sx={{ mt: 2 }}>
                  <Button
                    variant="contained"
                    onClick={processTimelineFile}
                    disabled={isProcessing}
                  >
                    Processar Arquivo
                  </Button>
                </Box>
              )}
            </StepContent>
          </Step>

          {/* Passo 2: Processar Dados */}
          <Step>
            <StepLabel>Processar Dados</StepLabel>
            <StepContent>
              {isProcessing && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" gutterBottom>
                    Processando dados do Timeline...
                  </Typography>
                  <LinearProgress 
                    variant="determinate" 
                    value={processingProgress}
                    sx={{ mb: 1 }}
                  />
                  <Typography variant="caption" color="text.secondary">
                    {processingProgress}% concluído
                  </Typography>
                </Box>
              )}

              {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {error}
                </Alert>
              )}

              {parsedData && (
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Dados Processados
                    </Typography>
                    <List dense>
                      <ListItem>
                        <ListItemIcon>
                          <LocationOn />
                        </ListItemIcon>
                        <ListItemText
                          primary="Total de Localizações"
                          secondary={parsedData.totalLocations.toLocaleString()}
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon>
                          <Schedule />
                        </ListItemIcon>
                        <ListItemText
                          primary="Período dos Dados"
                          secondary={`${new Date(parseInt(parsedData.dateRange.start)).toLocaleDateString()} - ${new Date(parseInt(parsedData.dateRange.end)).toLocaleDateString()}`}
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon>
                          <Info />
                        </ListItemIcon>
                        <ListItemText
                          primary="Dias Únicos"
                          secondary={parsedData.summary.uniqueDays}
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon>
                          <CheckCircle />
                        </ListItemIcon>
                        <ListItemText
                          primary="Localizações Noturnas"
                          secondary={`${parsedData.summary.nighttimeLocations} (${Math.round((parsedData.summary.nighttimeLocations / parsedData.totalLocations) * 100)}%)`}
                        />
                      </ListItem>
                    </List>
                  </CardContent>
                </Card>
              )}

              {parsedData && (
                <Box sx={{ mt: 2 }}>
                  <Button
                    variant="contained"
                    onClick={() => setActiveStep(2)}
                  >
                    Continuar
                  </Button>
                </Box>
              )}
            </StepContent>
          </Step>

          {/* Passo 3: Confirmar Upload */}
          <Step>
            <StepLabel>Confirmar Upload</StepLabel>
            <StepContent>
              <Alert severity="success" sx={{ mb: 2 }}>
                <Typography variant="body2">
                  Dados processados com sucesso! Os dados serão usados para validar sua residência.
                </Typography>
              </Alert>

              {parsedData && parsedData.summary.uniqueDays < 15 && (
                <Alert severity="warning" sx={{ mb: 2 }}>
                  <Typography variant="body2">
                    <strong>Atenção:</strong> Seus dados cobrem apenas {parsedData.summary.uniqueDays} dias únicos. 
                    Para melhor precisão, recomendamos pelo menos 15 dias de dados.
                  </Typography>
                </Alert>
              )}

              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="contained"
                  onClick={handleConfirmUpload}
                  startIcon={<CheckCircle />}
                >
                  Confirmar e Usar Dados
                </Button>
                <Button
                  variant="outlined"
                  onClick={() => setActiveStep(0)}
                >
                  Voltar
                </Button>
              </Box>
            </StepContent>
          </Step>
        </Stepper>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>
          Cancelar
        </Button>
        <Button
          variant="outlined"
          startIcon={<GetApp />}
          href="https://takeout.google.com"
          target="_blank"
        >
          Abrir Google Takeout
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default TimelineUpload;
