'use client';

import { useState, useEffect } from 'react';
import { User, ResidenceValidation, ResidenceCertificate } from '@/types';

export default function DashboardPage() {
  const [user, setUser] = useState<User | null>(null);
  const [validations, setValidations] = useState<ResidenceValidation[]>([]);
  const [certificates, setCertificates] = useState<ResidenceCertificate[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'validation' | 'certificates' | 'cartorio'>('validation');

  // Estados do formulário de validação
  const [homeAddress, setHomeAddress] = useState('');
  const [coordinates, setCoordinates] = useState({ latitude: 0, longitude: 0 });
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [isValidating, setIsValidating] = useState(false);

  useEffect(() => {
    // Simula carregamento de dados do usuário
    // Em produção, isso viria de uma API
    const mockUser: User = {
      id: '1',
      email: '<EMAIL>',
      name: 'Usuário Teste',
      googleId: 'google_123',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    setUser(mockUser);
    setLoading(false);

    // Define datas padrão (último mês)
    const today = new Date();
    const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
    setEndDate(today.toISOString().split('T')[0]);
    setStartDate(lastMonth.toISOString().split('T')[0]);
  }, []);

  const handleAddressSearch = async () => {
    if (!homeAddress.trim()) return;

    try {
      // Simula geocodificação do endereço
      // Em produção, usaria Google Geocoding API
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(homeAddress)}&key=YOUR_API_KEY`
      );
      
      // Mock para demonstração
      setCoordinates({
        latitude: -23.5505,
        longitude: -46.6333,
      });
    } catch (error) {
      console.error('Erro ao buscar coordenadas:', error);
    }
  };

  const startValidation = async () => {
    if (!homeAddress || !startDate || !endDate) {
      alert('Por favor, preencha todos os campos obrigatórios');
      return;
    }

    setIsValidating(true);

    try {
      const response = await fetch('/api/timeline/collect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          homeAddress,
          coordinates,
          startDate,
          endDate,
        }),
      });

      const result = await response.json();

      if (result.success) {
        // Adiciona nova validação à lista
        setValidations(prev => [result.data.validation, ...prev]);
        
        // Limpa formulário
        setHomeAddress('');
        setCoordinates({ latitude: 0, longitude: 0 });
        
        alert('Validação concluída com sucesso!');
      } else {
        alert(`Erro na validação: ${result.error}`);
      }
    } catch (error) {
      console.error('Erro ao iniciar validação:', error);
      alert('Erro ao conectar com o servidor');
    } finally {
      setIsValidating(false);
    }
  };

  const generateCertificate = async (validationId: string) => {
    try {
      const response = await fetch('/api/certificate/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ validationId }),
      });

      const result = await response.json();

      if (result.success) {
        setCertificates(prev => [result.data.certificate, ...prev]);
        alert('Certificado gerado com sucesso!');
      } else {
        alert(`Erro ao gerar certificado: ${result.error}`);
      }
    } catch (error) {
      console.error('Erro ao gerar certificado:', error);
      alert('Erro ao conectar com o servidor');
    }
  };

  const downloadCertificate = async (certificateId: string) => {
    try {
      const response = await fetch(`/api/certificate/generate?id=${certificateId}`);
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `certificado-${certificateId}.pdf`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
      } else {
        alert('Erro ao baixar certificado');
      }
    } catch (error) {
      console.error('Erro ao baixar certificado:', error);
      alert('Erro ao baixar certificado');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-900">
              Dashboard - Validação de Residência
            </h1>
            <div className="flex items-center space-x-4">
              <span className="text-gray-600">Olá, {user?.name}</span>
              <button
                onClick={() => window.location.href = '/'}
                className="bg-gray-200 hover:bg-gray-300 px-4 py-2 rounded-md text-sm font-medium"
              >
                Sair
              </button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Aviso Legal */}
        <div className="bg-amber-50 border-l-4 border-amber-400 p-4 mb-8 rounded-r-lg">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-amber-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-amber-700">
                <strong>⚖️ Lembrete importante:</strong> Os comprovantes gerados aqui são <strong>preliminares</strong>. 
                Para validade legal completa, é necessário autenticá-los em cartório.
              </p>
            </div>
          </div>
        </div>
        {/* Tabs */}
        <div className="mb-8">
          <nav className="flex space-x-8">
            <button
              onClick={() => setActiveTab('validation')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'validation'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Nova Validação
            </button>
            <button
              onClick={() => setActiveTab('certificates')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'certificates'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >              Meus Certificados
            </button>
            <button
              onClick={() => setActiveTab('cartorio')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'cartorio'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              🏛️ Autenticação Cartorária
            </button>
          </nav>
        </div>

        {/* Conteúdo das Tabs */}
        {activeTab === 'validation' && (
          <div className="space-y-8">
            {/* Formulário de Nova Validação */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">
                Solicitar Nova Validação de Residência
              </h2>

              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Endereço de Residência *
                    </label>
                    <input
                      type="text"
                      value={homeAddress}
                      onChange={(e) => setHomeAddress(e.target.value)}
                      onBlur={handleAddressSearch}
                      placeholder="Ex: Rua das Flores, 123, Centro, São Paulo - SP"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Data Inicial *
                      </label>
                      <input
                        type="date"
                        value={startDate}
                        onChange={(e) => setStartDate(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Data Final *
                      </label>
                      <input
                        type="date"
                        value={endDate}
                        onChange={(e) => setEndDate(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>

                  {coordinates.latitude !== 0 && (
                    <div className="bg-green-50 p-3 rounded-md">
                      <p className="text-sm text-green-700">
                        📍 Coordenadas: {coordinates.latitude.toFixed(6)}, {coordinates.longitude.toFixed(6)}
                      </p>
                    </div>
                  )}
                </div>

                <div className="bg-blue-50 p-6 rounded-lg">
                  <h3 className="font-semibold text-blue-900 mb-3">ℹ️ Critérios de Validação</h3>
                  <ul className="text-sm text-blue-800 space-y-2">
                    <li>• Permanência no local das 18h às 6h</li>
                    <li>• Mínimo de 15 dias consecutivos</li>
                    <li>• Raio máximo de 100 metros do endereço</li>
                    <li>• Confiabilidade mínima de 70%</li>
                  </ul>
                </div>
              </div>

              <div className="mt-6">
                <button
                  onClick={startValidation}
                  disabled={isValidating || !homeAddress || !startDate || !endDate}
                  className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-semibold py-3 px-6 rounded-lg transition duration-200"
                >
                  {isValidating ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white inline-block mr-2"></div>
                      Validando...
                    </>
                  ) : (
                    'Iniciar Validação'
                  )}
                </button>
              </div>
            </div>

            {/* Lista de Validações */}
            {validations.length > 0 && (
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">
                  Histórico de Validações
                </h2>

                <div className="space-y-4">
                  {validations.map((validation) => (
                    <div
                      key={validation.id}
                      className="border border-gray-200 rounded-lg p-4"
                    >
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h3 className="font-medium text-gray-900">
                            {validation.address}
                          </h3>
                          <p className="text-sm text-gray-500">
                            Período: {new Date(validation.validationPeriod.startDate).toLocaleDateString()} - {new Date(validation.validationPeriod.endDate).toLocaleDateString()}
                          </p>
                        </div>
                        <span
                          className={`px-2 py-1 rounded-full text-xs font-medium ${
                            validation.status === 'APPROVED'
                              ? 'bg-green-100 text-green-800'
                              : validation.status === 'REJECTED'
                              ? 'bg-red-100 text-red-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}
                        >
                          {validation.status === 'APPROVED' ? 'Aprovado' : 
                           validation.status === 'REJECTED' ? 'Rejeitado' : 'Pendente'}
                        </span>
                      </div>

                      <div className="flex justify-between items-center">
                        <div className="text-sm text-gray-600">
                          Score: {(validation.validationScore * 100).toFixed(1)}%
                        </div>
                        {validation.status === 'APPROVED' && (
                          <button
                            onClick={() => generateCertificate(validation.id)}
                            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                          >
                            Gerar Certificado
                          </button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}        {activeTab === 'certificates' && (
          <div className="space-y-8">
            {certificates.length === 0 ? (
              <div className="bg-white rounded-lg shadow-sm p-8 text-center">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Nenhum certificado encontrado
                </h3>
                <p className="text-gray-500">
                  Complete uma validação aprovada para gerar seu primeiro certificado.
                </p>
              </div>
            ) : (
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">
                  Meus Certificados de Residência
                </h2>

                <div className="grid md:grid-cols-2 gap-6">
                  {certificates.map((certificate) => (
                    <div
                      key={certificate.id}
                      className="border border-gray-200 rounded-lg p-6"
                    >
                      <div className="flex justify-between items-start mb-4">
                        <div>
                          <h3 className="font-medium text-gray-900">
                            Certificado #{certificate.certificateNumber}
                          </h3>
                          <p className="text-sm text-gray-500">
                            Emitido em {new Date(certificate.issueDate).toLocaleDateString()}
                          </p>
                        </div>
                        <span
                          className={`px-2 py-1 rounded-full text-xs font-medium ${
                            certificate.status === 'ACTIVE'
                              ? 'bg-green-100 text-green-800'
                              : certificate.status === 'EXPIRED'
                              ? 'bg-red-100 text-red-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}
                        >
                          {certificate.status === 'ACTIVE' ? 'Ativo' : 
                           certificate.status === 'EXPIRED' ? 'Expirado' : 'Revogado'}
                        </span>
                      </div>

                      <div className="space-y-2 text-sm">
                        <p><strong>Endereço:</strong> {certificate.address}</p>
                        <p><strong>Válido até:</strong> {new Date(certificate.validUntil).toLocaleDateString()}</p>
                        <p><strong>Dias validados:</strong> {certificate.validDays}</p>
                        <p><strong>Score:</strong> {(certificate.validationScore * 100).toFixed(1)}%</p>
                      </div>

                      {/* Aviso de Autenticação */}
                      <div className="mt-3 p-2 bg-amber-50 border border-amber-200 rounded text-xs text-amber-700">
                        ⚖️ <strong>Lembrete:</strong> Autentique em cartório para validade legal
                      </div>

                      <div className="mt-4 flex space-x-2">
                        <button
                          onClick={() => downloadCertificate(certificate.id)}
                          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium flex-1"
                        >
                          📄 Baixar PDF
                        </button>
                        <button
                          onClick={() => alert(`QR Code: ${certificate.qrCode}`)}
                          className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                        >
                          📱 QR Code
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'cartorio' && (
          <div className="space-y-8">
            {/* Guia de Autenticação Cartorária */}
            <div className="bg-white rounded-lg shadow-sm p-8">
              <div className="text-center mb-8">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.586-5.586l-7.778 7.778-2.12-2.121" />
                  </svg>
                </div>
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  🏛️ Autenticação Cartorária
                </h2>
                <p className="text-gray-600 max-w-2xl mx-auto">
                  Para que seu comprovante tenha validade legal completa, é necessário autenticá-lo em cartório. 
                  Siga o passo a passo abaixo:
                </p>
              </div>

              <div className="grid md:grid-cols-2 gap-8">
                {/* Passo a Passo */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">📋 Passo a Passo</h3>
                  <div className="space-y-4">
                    <div className="flex items-start">
                      <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
                        1
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">Gere seu certificado</h4>
                        <p className="text-sm text-gray-600">Complete a validação e baixe o PDF gerado pelo sistema</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
                        2
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">Reúna os documentos</h4>
                        <p className="text-sm text-gray-600">Certificado PDF, RG/CNH e comprovante de endereço tradicional</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
                        3
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">Vá ao cartório</h4>
                        <p className="text-sm text-gray-600">Qualquer cartório pode autenticar documentos digitais</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
                        4
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">Receba o documento autenticado</h4>
                        <p className="text-sm text-gray-600">Agora seu comprovante tem validade legal completa</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Informações Adicionais */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">ℹ️ Informações Importantes</h3>
                  
                  <div className="space-y-4">
                    <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                      <h4 className="font-medium text-green-900 mb-1">💰 Custo</h4>
                      <p className="text-sm text-green-800">
                        Apenas taxas cartoriais (R$ 8-15, varia por estado). Nossa validação é gratuita.
                      </p>
                    </div>
                    
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                      <h4 className="font-medium text-blue-900 mb-1">⏱️ Tempo</h4>
                      <p className="text-sm text-blue-800">
                        Processo cartorário: 1-3 dias úteis. Alguns cartórios fazem no mesmo dia.
                      </p>
                    </div>
                    
                    <div className="bg-purple-50 border border-purple-200 rounded-lg p-3">
                      <h4 className="font-medium text-purple-900 mb-1">📍 Localização</h4>
                      <p className="text-sm text-purple-800">
                        Pode ser feito em qualquer cartório do Brasil. Procure o mais próximo.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* CTA Principal */}
              <div className="mt-8 text-center">
                <button
                  onClick={() => window.open('https://www.google.com/search?q=cartorio+próximo+de+mim', '_blank')}
                  className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg transition duration-200 inline-flex items-center"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  Encontrar Cartório Próximo
                </button>
              </div>
            </div>

            {/* FAQ Cartorária */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">❓ Dúvidas Frequentes</h3>
              
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Posso usar o certificado sem autenticar?</h4>
                    <p className="text-sm text-gray-600">
                      Para uso informal, sim. Para processos oficiais (bancos, órgãos públicos), 
                      é necessário autenticar.
                    </p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Preciso imprimir o PDF?</h4>
                    <p className="text-sm text-gray-600">
                      Sim, a maioria dos cartórios requer documento físico para autenticação. 
                      Alguns já aceitam documentos digitais.
                    </p>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">O QR Code funciona depois da autenticação?</h4>
                    <p className="text-sm text-gray-600">
                      Sim, o QR Code continua funcionando e permite verificar a autenticidade 
                      do documento original.
                    </p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Posso autenticar em qualquer cartório?</h4>
                    <p className="text-sm text-gray-600">
                      Sim, qualquer cartório brasileiro pode autenticar documentos. 
                      Recomendamos ligar antes para confirmar procedimentos.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
