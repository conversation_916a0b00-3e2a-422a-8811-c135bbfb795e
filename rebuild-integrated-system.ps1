# Script PowerShell para reconstruir e testar o sistema integrado com validação de residência

Write-Host "=== Iniciando rebuild do sistema integrado ===" -ForegroundColor Green

# Passo 1: Parar containers existentes
Write-Host "Parando containers existentes..." -ForegroundColor Yellow
docker-compose down

# Passo 2: Remover imagens antigas para garantir rebuild completo
Write-Host "Removendo imagens antigas..." -ForegroundColor Yellow
docker-compose down --rmi all --volumes --remove-orphans

# Passo 3: Limpar cache do Docker
Write-Host "Limpando cache do Docker..." -ForegroundColor Yellow
docker system prune -f

# Passo 4: Rebuild com cache limpo
Write-Host "Reconstruindo containers..." -ForegroundColor Yellow
docker-compose build --no-cache

# Passo 5: Subir o sistema
Write-Host "Subindo o sistema..." -ForegroundColor Yellow
docker-compose up -d

# Passo 6: Aguardar inicialização
Write-Host "Aguardando inicialização dos serviços..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# Passo 7: Verificar status dos containers
Write-Host "Verificando status dos containers..." -ForegroundColor Yellow
docker-compose ps

# Passo 8: Verificar logs
Write-Host "Verificando logs do backend..." -ForegroundColor Yellow
docker-compose logs backend --tail=50

Write-Host "Verificando logs do web-app..." -ForegroundColor Yellow
docker-compose logs web-app --tail=50

# Passo 9: Testar endpoints
Write-Host "Testando endpoints..." -ForegroundColor Green
Write-Host "Frontend: http://localhost" -ForegroundColor Cyan
Write-Host "Backend: http://localhost:3001" -ForegroundColor Cyan
Write-Host "Residence API: http://localhost:3001/api/residence" -ForegroundColor Cyan

# Passo 10: Verificar conectividade
Write-Host "Testando conectividade..." -ForegroundColor Yellow
try {
    Invoke-WebRequest -Uri "http://localhost:3001/health" -UseBasicParsing
    Write-Host "Backend está respondendo" -ForegroundColor Green
} catch {
    Write-Host "Backend não está respondendo" -ForegroundColor Red
}

try {
    Invoke-WebRequest -Uri "http://localhost" -UseBasicParsing
    Write-Host "Frontend está respondendo" -ForegroundColor Green
} catch {
    Write-Host "Frontend não está respondendo" -ForegroundColor Red
}

Write-Host "=== Rebuild concluído ===" -ForegroundColor Green
Write-Host "Acesse o sistema em: http://localhost" -ForegroundColor Cyan
Write-Host "A nova funcionalidade de Validação de Residência estará disponível no menu lateral" -ForegroundColor Cyan
