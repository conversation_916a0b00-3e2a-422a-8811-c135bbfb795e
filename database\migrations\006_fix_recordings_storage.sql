-- Migração 006: Correção do sistema de armazenamento de gravações
-- Data: 2025-07-20
-- Descrição: Corrige caminhos de arquivos e garante estrutura adequada

-- 1. Adicionar coluna original_filename se não existir (para compatibilidade)
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'recordings' AND column_name = 'original_filename'
    ) THEN
        ALTER TABLE recordings ADD COLUMN original_filename VARCHAR(255);
        COMMENT ON COLUMN recordings.original_filename IS 'Nome original do arquivo enviado pelo usuário';
    END IF;
END $$;

-- 2. Atualizar original_filename com base no filename existente (para dados existentes)
UPDATE recordings 
SET original_filename = filename 
WHERE original_filename IS NULL;

-- 3. Função para migrar caminhos de arquivos do diretório temp para recordings
CREATE OR REPLACE FUNCTION migrate_recording_paths()
RETURNS TABLE(
    recording_id UUID,
    old_path TEXT,
    new_path TEXT,
    status TEXT
) AS $$
DECLARE
    rec RECORD;
    old_file_path TEXT;
    new_file_path TEXT;
    recordings_dir TEXT := '/app/storage/recordings/';
    temp_dir TEXT := '/app/storage/temp/';
BEGIN
    -- Iterar sobre todas as gravações
    FOR rec IN SELECT id, filename, file_path FROM recordings WHERE deleted_at IS NULL
    LOOP
        old_file_path := rec.file_path;
        
        -- Se o arquivo está no diretório temp, corrigir para recordings
        IF old_file_path LIKE '%/temp/%' THEN
            new_file_path := recordings_dir || rec.filename;
            
            -- Atualizar o caminho no banco
            UPDATE recordings 
            SET file_path = new_file_path,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = rec.id;
            
            RETURN QUERY SELECT 
                rec.id,
                old_file_path,
                new_file_path,
                'MIGRATED'::TEXT;
        ELSE
            RETURN QUERY SELECT 
                rec.id,
                old_file_path,
                old_file_path,
                'NO_CHANGE'::TEXT;
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- 4. Executar migração de caminhos
SELECT * FROM migrate_recording_paths();

-- 5. Criar índice para original_filename se não existir
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE tablename = 'recordings' AND indexname = 'idx_recordings_original_filename'
    ) THEN
        CREATE INDEX idx_recordings_original_filename ON recordings(original_filename);
    END IF;
END $$;

-- 6. Adicionar constraint para garantir que file_path aponte para diretório correto
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_name = 'recordings_file_path_check'
    ) THEN
        ALTER TABLE recordings 
        ADD CONSTRAINT recordings_file_path_check 
        CHECK (file_path LIKE '/app/storage/recordings/%' OR file_path LIKE '/storage/recordings/%');
    END IF;
EXCEPTION
    WHEN others THEN
        -- Ignorar se a constraint já existe ou há conflito
        NULL;
END $$;

-- 7. Função para verificar integridade dos arquivos
CREATE OR REPLACE FUNCTION check_recordings_integrity()
RETURNS TABLE(
    recording_id UUID,
    filename VARCHAR(255),
    file_path TEXT,
    file_exists BOOLEAN,
    size_matches BOOLEAN,
    hash_matches BOOLEAN,
    status TEXT
) AS $$
DECLARE
    rec RECORD;
BEGIN
    FOR rec IN SELECT id, filename, file_path, file_size, hash FROM recordings WHERE deleted_at IS NULL
    LOOP
        RETURN QUERY SELECT 
            rec.id,
            rec.filename,
            rec.file_path,
            -- Estas verificações serão feitas pela aplicação
            NULL::BOOLEAN as file_exists,
            NULL::BOOLEAN as size_matches, 
            NULL::BOOLEAN as hash_matches,
            'PENDING_CHECK'::TEXT as status;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- 8. Comentários para documentação
COMMENT ON FUNCTION migrate_recording_paths() IS 'Migra caminhos de arquivos do diretório temp para recordings';
COMMENT ON FUNCTION check_recordings_integrity() IS 'Verifica integridade dos arquivos de gravação';

-- 9. Log da migração
INSERT INTO migration_log (migration_name, applied_at, description) 
VALUES (
    '006_fix_recordings_storage', 
    CURRENT_TIMESTAMP, 
    'Correção do sistema de armazenamento de gravações - adiciona original_filename e migra caminhos'
) ON CONFLICT (migration_name) DO NOTHING;

-- 10. Criar tabela de log de migrações se não existir
CREATE TABLE IF NOT EXISTS migration_log (
    id SERIAL PRIMARY KEY,
    migration_name VARCHAR(255) UNIQUE NOT NULL,
    applied_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    description TEXT
);

COMMENT ON TABLE migration_log IS 'Log de migrações aplicadas ao banco de dados';
