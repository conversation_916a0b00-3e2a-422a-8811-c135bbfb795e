@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Melhorias de Acessibilidade */
/* Garante contraste mínimo para elementos interativos */
.btn-primary {
  min-height: 44px; /* Tamanho mínimo de toque para dispositivos móveis */
}

/* Indicadores de foco melhorados */
button:focus,
input:focus,
select:focus,
textarea:focus,
a:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Animações respeitam preferências do usuário */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Melhora a legibilidade */
.prose {
  line-height: 1.6;
}

/* <PERSON><PERSON><PERSON> que links sejam distinguíveis */
a:not(.btn) {
  text-decoration: underline;
}

a:not(.btn):hover {
  text-decoration: none;
}

/* Melhora contraste para texto em fundos coloridos */
.bg-amber-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 251 235 / var(--tw-bg-opacity));
}

.text-amber-700 {
  --tw-text-opacity: 1;
  color: rgb(180 83 9 / var(--tw-text-opacity));
}

/* Adiciona suporte para modo escuro respeitando preferências */
@media (prefers-color-scheme: dark) {
  .bg-white {
    background-color: #1f2937;
  }
  
  .text-gray-900 {
    color: #f9fafb;
  }
  
  .text-gray-600 {
    color: #d1d5db;
  }
  
  .border-gray-200 {
    border-color: #374151;
  }
}
