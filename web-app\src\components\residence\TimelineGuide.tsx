import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  <PERSON>alogContent,
  <PERSON>alogActions,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  StepLabel,
  StepContent,
  Typography,
  Box,
  Card,
  CardMedia,
  CardContent,
  Alert,
  Chip,
  IconButton,
  Link,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  Close,
  PhoneAndroid,
  Computer,
  LocationOn,
  Security,
  CheckCircle,
  Warning,
  ExpandMore,
  OpenInNew,
  PlayArrow
} from '@mui/icons-material';

interface TimelineGuideProps {
  open: boolean;
  onClose: () => void;
  onComplete?: () => void;
}

const TimelineGuide: React.FC<TimelineGuideProps> = ({ open, onClose, onComplete }) => {
  const [activeStep, setActiveStep] = useState(0);
  const [platform, setPlatform] = useState<'mobile' | 'desktop'>('mobile');

  const mobileSteps = [
    {
      label: 'Abrir Google Maps',
      content: (
        <Box>
          <Typography variant="body1" paragraph>
            Abra o aplicativo <strong>Google Maps</strong> no seu celular.
          </Typography>
          <Card sx={{ maxWidth: 300, mx: 'auto', mb: 2 }}>
            <CardMedia
              component="img"
              height="200"
              image="/images/guide/step1-open-maps.png"
              alt="Abrir Google Maps"
              sx={{ objectFit: 'contain', bgcolor: 'grey.100' }}
            />
            <CardContent>
              <Typography variant="caption" color="text.secondary">
                Toque no ícone do Google Maps na tela inicial
              </Typography>
            </CardContent>
          </Card>
          <Alert severity="info" sx={{ mt: 2 }}>
            Se não tiver o app instalado, baixe na{' '}
            <Link href="https://play.google.com/store/apps/details?id=com.google.android.apps.maps" target="_blank">
              Play Store
            </Link>{' '}
            ou{' '}
            <Link href="https://apps.apple.com/app/google-maps/id585027354" target="_blank">
              App Store
            </Link>
          </Alert>
        </Box>
      )
    },
    {
      label: 'Acessar Seu Perfil',
      content: (
        <Box>
          <Typography variant="body1" paragraph>
            Toque na sua <strong>foto de perfil</strong> no canto superior direito.
          </Typography>
          <Card sx={{ maxWidth: 300, mx: 'auto', mb: 2 }}>
            <CardMedia
              component="img"
              height="200"
              image="/images/guide/step2-profile.png"
              alt="Acessar perfil"
              sx={{ objectFit: 'contain', bgcolor: 'grey.100' }}
            />
            <CardContent>
              <Typography variant="caption" color="text.secondary">
                Sua foto aparece no canto superior direito da tela
              </Typography>
            </CardContent>
          </Card>
        </Box>
      )
    },
    {
      label: 'Ir para Cronologia',
      content: (
        <Box>
          <Typography variant="body1" paragraph>
            No menu que abrir, toque em <strong>"Sua cronologia"</strong>.
          </Typography>
          <Card sx={{ maxWidth: 300, mx: 'auto', mb: 2 }}>
            <CardMedia
              component="img"
              height="200"
              image="/images/guide/step3-timeline.png"
              alt="Sua cronologia"
              sx={{ objectFit: 'contain', bgcolor: 'grey.100' }}
            />
            <CardContent>
              <Typography variant="caption" color="text.secondary">
                Procure pela opção "Sua cronologia" no menu
              </Typography>
            </CardContent>
          </Card>
        </Box>
      )
    },
    {
      label: 'Ativar Histórico de Localização',
      content: (
        <Box>
          <Typography variant="body1" paragraph>
            Se aparecer uma mensagem sobre ativar o histórico, toque em <strong>"Ativar"</strong>.
          </Typography>
          <Card sx={{ maxWidth: 300, mx: 'auto', mb: 2 }}>
            <CardMedia
              component="img"
              height="200"
              image="/images/guide/step4-enable.png"
              alt="Ativar histórico"
              sx={{ objectFit: 'contain', bgcolor: 'grey.100' }}
            />
            <CardContent>
              <Typography variant="caption" color="text.secondary">
                Confirme a ativação do histórico de localização
              </Typography>
            </CardContent>
          </Card>
          <Alert severity="warning" sx={{ mt: 2 }}>
            <Typography variant="body2">
              <strong>Importante:</strong> O histórico precisa estar ativo por pelo menos 15 dias 
              para gerar dados suficientes para validação.
            </Typography>
          </Alert>
        </Box>
      )
    },
    {
      label: 'Verificar Configurações',
      content: (
        <Box>
          <Typography variant="body1" paragraph>
            Verifique se as configurações estão corretas:
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, mb: 2 }}>
            <Chip icon={<CheckCircle />} label="Histórico de localização: ATIVADO" color="success" />
            <Chip icon={<CheckCircle />} label="Atividade na Web e de apps: ATIVADO" color="success" />
            <Chip icon={<CheckCircle />} label="Precisão da localização: ALTA" color="success" />
          </Box>
          <Alert severity="success">
            <Typography variant="body2">
              Perfeito! Agora o Google Maps está coletando seu histórico de localização. 
              Aguarde alguns dias e depois volte para fazer a validação.
            </Typography>
          </Alert>
        </Box>
      )
    }
  ];

  const desktopSteps = [
    {
      label: 'Acessar Google Maps Web',
      content: (
        <Box>
          <Typography variant="body1" paragraph>
            Acesse{' '}
            <Link href="https://maps.google.com" target="_blank" rel="noopener">
              maps.google.com
            </Link>{' '}
            no seu navegador.
          </Typography>
          <Button
            variant="outlined"
            startIcon={<OpenInNew />}
            href="https://maps.google.com"
            target="_blank"
            sx={{ mb: 2 }}
          >
            Abrir Google Maps
          </Button>
        </Box>
      )
    },
    {
      label: 'Fazer Login',
      content: (
        <Box>
          <Typography variant="body1" paragraph>
            Clique em <strong>"Fazer login"</strong> e entre com sua conta Google.
          </Typography>
          <Alert severity="info">
            Use a mesma conta Google do seu celular para sincronizar os dados.
          </Alert>
        </Box>
      )
    },
    {
      label: 'Acessar Cronologia',
      content: (
        <Box>
          <Typography variant="body1" paragraph>
            Acesse{' '}
            <Link href="https://timeline.google.com" target="_blank" rel="noopener">
              timeline.google.com
            </Link>{' '}
            para ver e configurar sua cronologia.
          </Typography>
          <Button
            variant="outlined"
            startIcon={<OpenInNew />}
            href="https://timeline.google.com"
            target="_blank"
            sx={{ mb: 2 }}
          >
            Abrir Timeline
          </Button>
        </Box>
      )
    }
  ];

  const steps = platform === 'mobile' ? mobileSteps : desktopSteps;

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleComplete = () => {
    onComplete?.();
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { minHeight: '70vh' }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <LocationOn color="primary" />
            <Typography variant="h6">
              Como Ativar o Google Maps Timeline
            </Typography>
          </Box>
          <IconButton onClick={onClose}>
            <Close />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        {/* Seletor de Plataforma */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            Escolha sua plataforma:
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant={platform === 'mobile' ? 'contained' : 'outlined'}
              startIcon={<PhoneAndroid />}
              onClick={() => setPlatform('mobile')}
            >
              Celular
            </Button>
            <Button
              variant={platform === 'desktop' ? 'contained' : 'outlined'}
              startIcon={<Computer />}
              onClick={() => setPlatform('desktop')}
            >
              Computador
            </Button>
          </Box>
        </Box>

        {/* Stepper */}
        <Stepper activeStep={activeStep} orientation="vertical">
          {steps.map((step, index) => (
            <Step key={step.label}>
              <StepLabel>
                <Typography variant="h6">{step.label}</Typography>
              </StepLabel>
              <StepContent>
                {step.content}
                <Box sx={{ mb: 2, mt: 2 }}>
                  <Button
                    variant="contained"
                    onClick={index === steps.length - 1 ? handleComplete : handleNext}
                    sx={{ mr: 1 }}
                  >
                    {index === steps.length - 1 ? 'Concluir' : 'Próximo'}
                  </Button>
                  <Button
                    disabled={index === 0}
                    onClick={handleBack}
                  >
                    Voltar
                  </Button>
                </Box>
              </StepContent>
            </Step>
          ))}
        </Stepper>

        {/* FAQ */}
        <Box sx={{ mt: 4 }}>
          <Typography variant="h6" gutterBottom>
            Perguntas Frequentes
          </Typography>
          
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Typography>Por que preciso ativar o Timeline?</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2">
                O Timeline do Google Maps registra onde você esteve ao longo do tempo. 
                Usamos esses dados para verificar se você realmente mora no endereço informado, 
                analisando sua presença noturna (18h às 6h) durante um período de pelo menos 15 dias.
              </Typography>
            </AccordionDetails>
          </Accordion>

          <Accordion>
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Typography>Meus dados estão seguros?</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2">
                Sim! Seguimos rigorosamente a LGPD. Seus dados são criptografados, 
                usados apenas para validação de residência, e você pode revogá-los a qualquer momento. 
                Não compartilhamos com terceiros.
              </Typography>
            </AccordionDetails>
          </Accordion>

          <Accordion>
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Typography>Quanto tempo demora para ter dados suficientes?</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2">
                Recomendamos aguardar pelo menos 15 dias após ativar o Timeline. 
                Quanto mais dados, mais precisa será a validação. Para melhores resultados, 
                aguarde 30 dias.
              </Typography>
            </AccordionDetails>
          </Accordion>
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>
          Fechar
        </Button>
        <Button
          variant="contained"
          startIcon={<PlayArrow />}
          onClick={() => window.open('https://timeline.google.com', '_blank')}
        >
          Abrir Timeline Agora
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default TimelineGuide;
