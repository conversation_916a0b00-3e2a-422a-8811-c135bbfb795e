import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Alert,
  Button,
  Tabs,
  Tab,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Divider,
  Link,
  AccordionDetails,
  AccordionSummary,
  Accordion
} from '@mui/material';
import {
  Home,
  Security,
  Assessment,
  FileDownload,
  LocationOn,
  Schedule,
  CheckCircle,
  Warning,
  Info,
  HelpOutline,
  Business,
  ExpandMore
} from '@mui/icons-material';
import ValidationForm from './ValidationForm';
import TimelineGuide from './TimelineGuide';
import NotificationManager from './NotificationManager';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => (
  <div role="tabpanel" hidden={value !== index}>
    {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
  </div>
);

const ResidenceValidationPage: React.FC = () => {
  const [currentTab, setCurrentTab] = useState(0);
  const [validationResults, setValidationResults] = useState<any[]>([]);
  const [showTimelineGuide, setShowTimelineGuide] = useState(false);

  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };

  const handleValidationComplete = (result: any) => {
    setValidationResults(prev => [result, ...prev]);
    setCurrentTab(1); // Ir para aba de certificados
  };

  const faqItems = [
    {
      question: "O que é validação de residência por geolocalização?",
      answer: "É um processo que usa dados do Google Maps Timeline para comprovar onde você mora, analisando padrões de localização noturna de forma automática e segura."
    },
    {
      question: "Meus dados ficam seguros?",
      answer: "Sim! Coletamos apenas dados de localização do período solicitado, criptografamos tudo e não compartilhamos com terceiros. Você tem controle total sobre seus dados."
    },
    {
      question: "O certificado tem validade legal?",
      answer: "O certificado gerado é preliminar. Para validade legal, deve ser autenticado em cartório competente. Isso custa R$ 8-15 e leva 1-3 dias úteis."
    },
    {
      question: "Como funciona o algoritmo de validação?",
      answer: "Analisamos sua presença no período noturno (18h-6h) em um raio de 100m do endereço informado. Precisamos de pelo menos 15 dias consecutivos com 70% de presença."
    },
    {
      question: "Posso usar sem ter Timeline ativado?",
      answer: "Infelizmente não. O Timeline deve estar ativado no Google Maps com histórico de pelo menos 15-30 dias para funcionar adequadamente."
    },
    {
      question: "Quanto custa o serviço?",
      answer: "O certificado preliminar é gratuito. A autenticação cartorária custa R$ 8-15 (varia por estado) e é opcional, mas necessária para validade legal."
    },
    {
      question: "Posso usar para processos bancários?",
      answer: "Apenas após autenticação cartorária. Bancos e instituições financeiras aceitam apenas documentos com validade legal oficial."
    },
    {
      question: "Como revogo meu consentimento?",
      answer: "Você pode revogar o consentimento a qualquer momento. Todos os dados coletados serão excluídos permanentemente de nossos sistemas."
    }
  ];

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 4, textAlign: 'center' }}>
        <Typography variant="h3" component="h1" gutterBottom>
          🏠 Validação de Residência
        </Typography>
        <Typography variant="h6" color="text.secondary" gutterBottom>
          Comprove sua residência usando dados de geolocalização do Google Maps
        </Typography>

        <Box sx={{ mt: 2, mb: 2 }}>
          <Button
            variant="outlined"
            startIcon={<HelpOutline />}
            onClick={() => setShowTimelineGuide(true)}
            sx={{ mr: 2 }}
          >
            Como Ativar Timeline
          </Button>
          <Button
            variant="text"
            startIcon={<Info />}
            href="https://support.google.com/maps/answer/6258979"
            target="_blank"
          >
            Ajuda Google Maps
          </Button>
        </Box>

        <Alert severity="warning" sx={{ mt: 2, maxWidth: 800, mx: 'auto' }}>
          <Typography variant="body2">
            <strong>Importante:</strong> Os certificados gerados são preliminares e não possuem validade legal. 
            Para validade legal, é necessária autenticação em cartório competente (R$ 8-15, 1-3 dias úteis).
          </Typography>
        </Alert>
      </Box>

      {/* Tabs */}
      <Paper elevation={1} sx={{ mb: 3 }}>
        <Tabs value={currentTab} onChange={handleTabChange} centered>
          <Tab icon={<Assessment />} label="Validação" />
          <Tab icon={<FileDownload />} label="Certificados" />
          <Tab icon={<Business />} label="Autenticação Cartorária" />
          <Tab icon={<HelpOutline />} label="FAQ" />
        </Tabs>
      </Paper>

      {/* Tab: Validação */}
      <TabPanel value={currentTab} index={0}>
        <ValidationForm onValidationComplete={handleValidationComplete} />
      </TabPanel>

      {/* Tab: Certificados */}
      <TabPanel value={currentTab} index={1}>
        <Typography variant="h5" gutterBottom>
          Certificados Gerados
        </Typography>
        
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="body2">
            <strong>Lembre-se:</strong> Estes certificados são preliminares. Para validade legal, 
            leve-os ao cartório para autenticação oficial.
          </Typography>
        </Alert>

        {validationResults.length === 0 ? (
          <Paper elevation={1} sx={{ p: 4, textAlign: 'center' }}>
            <Typography variant="h6" color="text.secondary">
              Nenhum certificado gerado ainda
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Complete uma validação para gerar seu primeiro certificado
            </Typography>
            <Button
              variant="outlined"
              onClick={() => setCurrentTab(0)}
              sx={{ mt: 2 }}
            >
              Iniciar Validação
            </Button>
          </Paper>
        ) : (
          <Grid container spacing={2}>
            {validationResults.map((result, index) => (
              <Grid item xs={12} md={6} key={index}>
                <Card>
                  <CardContent>
                    <Box display="flex" justifyContent="space-between" alignItems="start" mb={2}>
                      <Typography variant="h6">
                        Certificado #{result.validationId.slice(-8)}
                      </Typography>
                      <Chip
                        label={result.validation.summary.status}
                        color={result.validation.isValid ? 'success' : 'warning'}
                        size="small"
                      />
                    </Box>
                    
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      {result.metadata.homeAddress}
                    </Typography>
                    
                    <Box display="flex" gap={2} mb={2}>
                      <Chip
                        label={`${result.validation.summary.scorePercentage}% confiança`}
                        size="small"
                        variant="outlined"
                      />
                      <Chip
                        label={`${result.validation.maxConsecutiveDays} dias consecutivos`}
                        size="small"
                        variant="outlined"
                      />
                    </Box>
                    
                    <Typography variant="body2" color="text.secondary">
                      Gerado em: {new Date(result.metadata.processedAt).toLocaleString('pt-BR')}
                    </Typography>
                    
                    <Button
                      variant="outlined"
                      startIcon={<FileDownload />}
                      fullWidth
                      sx={{ mt: 2 }}
                      onClick={() => {
                        // Implementar download do certificado
                        console.log('Download certificado:', result.validationId);
                      }}
                    >
                      Baixar Certificado
                    </Button>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
      </TabPanel>

      {/* Tab: Autenticação Cartorária */}
      <TabPanel value={currentTab} index={2}>
        <Typography variant="h5" gutterBottom>
          Autenticação Cartorária
        </Typography>
        
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="body2">
            <strong>Passo obrigatório:</strong> Para que seu certificado tenha validade legal, 
            deve ser autenticado em cartório competente.
          </Typography>
        </Alert>

        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom color="primary">
                  📋 Passo a Passo
                </Typography>
                <List>
                  <ListItem>
                    <ListItemIcon><span style={{ fontSize: '1.2rem' }}>1️⃣</span></ListItemIcon>
                    <ListItemText 
                      primary="Gere o certificado preliminar"
                      secondary="Use nosso sistema para criar o documento base"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon><span style={{ fontSize: '1.2rem' }}>2️⃣</span></ListItemIcon>
                    <ListItemText 
                      primary="Reúna documentos"
                      secondary="Certificado + RG/CNH + comprovante tradicional"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon><span style={{ fontSize: '1.2rem' }}>3️⃣</span></ListItemIcon>
                    <ListItemText 
                      primary="Vá ao cartório"
                      secondary="Qualquer cartório brasileiro pode autenticar"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon><span style={{ fontSize: '1.2rem' }}>4️⃣</span></ListItemIcon>
                    <ListItemText 
                      primary="Receba documento legal"
                      secondary="Agora tem validade legal para todos os usos"
                    />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom color="secondary">
                  💰 Custos e Prazos
                </Typography>
                <List>
                  <ListItem>
                    <ListItemIcon><Business /></ListItemIcon>
                    <ListItemText 
                      primary="Custo: R$ 8,00 a R$ 15,00"
                      secondary="Varia conforme estado e cartório"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon><Schedule /></ListItemIcon>
                    <ListItemText 
                      primary="Prazo: 1 a 3 dias úteis"
                      secondary="Depende da demanda do cartório"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon><CheckCircle /></ListItemIcon>
                    <ListItemText 
                      primary="Validade: Permanente"
                      secondary="Documento autenticado não expira"
                    />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        <Box sx={{ mt: 3, textAlign: 'center' }}>
          <Button
            variant="contained"
            size="large"
            startIcon={<Business />}
            onClick={() => window.open('https://www.google.com/maps/search/cartório+próximo', '_blank')}
          >
            Encontrar Cartório Próximo
          </Button>
        </Box>
      </TabPanel>

      {/* Tab: FAQ */}
      <TabPanel value={currentTab} index={3}>
        <Typography variant="h5" gutterBottom>
          Perguntas Frequentes
        </Typography>
        
        {faqItems.map((item, index) => (
          <Accordion key={index}>
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Typography variant="subtitle1">{item.question}</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2" color="text.secondary">
                {item.answer}
              </Typography>
            </AccordionDetails>
          </Accordion>
        ))}

        <Alert severity="info" sx={{ mt: 3 }}>
          <Typography variant="body2">
            <strong>Ainda tem dúvidas?</strong> Entre em contato conosco pelo email: 
            <Link href="mailto:<EMAIL>" sx={{ ml: 1 }}>
              <EMAIL>
            </Link>
          </Typography>
        </Alert>
      </TabPanel>

      {/* Timeline Guide Modal */}
      <TimelineGuide
        open={showTimelineGuide}
        onClose={() => setShowTimelineGuide(false)}
        onComplete={() => {
          // Opcional: mostrar notificação de sucesso
          console.log('Timeline guide completed');
        }}
      />

      {/* Notification Manager */}
      <NotificationManager
        userId="current_user_id" // Em produção, obter do contexto de autenticação
        onTimelineReminderSent={() => {
          console.log('Timeline reminder sent to user');
        }}
      />
    </Container>
  );
};

export default ResidenceValidationPage;
