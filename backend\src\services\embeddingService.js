const { Pool } = require('pg');
const OpenAI = require('openai');

// Configurar cliente OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || 'demo_key_placeholder'
});

// Pool de conexões PostgreSQL
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'cartorio_db',
  user: process.env.DB_USER || 'cartorio_user',
  password: process.env.DB_PASSWORD || 'cartorio_secure_pass',
});

class EmbeddingService {
  /**
   * Gerar embedding usando OpenAI
   */
  static async generateEmbedding(text) {
    try {
      if (!text || text.trim().length === 0) {
        throw new Error('Texto vazio para gerar embedding');
      }

      // Limitar tamanho do texto (OpenAI tem limite de tokens)
      const maxLength = 8000; // Aproximadamente 8k tokens
      const truncatedText = text.length > maxLength ? text.substring(0, maxLength) : text;

      const response = await openai.embeddings.create({
        model: 'text-embedding-ada-002',
        input: truncatedText,
      });

      return response.data[0].embedding;
    } catch (error) {
      console.error('[EmbeddingService] Erro ao gerar embedding:', error);
      
      // Fallback: retornar embedding dummy para desenvolvimento
      if (process.env.NODE_ENV === 'development') {
        console.warn('[EmbeddingService] Usando embedding dummy para desenvolvimento');
        return Array(1536).fill(0).map(() => Math.random() * 2 - 1); // 1536 dimensões aleatórias
      }
      
      throw error;
    }
  }

  /**
   * Salvar embedding de certificado de residência
   */
  static async saveResidenceCertificateEmbedding(certificateData) {
    try {
      const { certificateId, userId, address, description, metadata } = certificateData;
      
      // Criar texto para embedding
      const textForEmbedding = `
        Certificado de Residência
        Endereço: ${address}
        Descrição: ${description || ''}
        Metadados: ${JSON.stringify(metadata || {})}
      `.trim();

      // Gerar embedding
      const embedding = await this.generateEmbedding(textForEmbedding);

      // Salvar no banco
      const query = `
        INSERT INTO residence_certificate_embeddings 
        (certificate_id, user_id, address, description, metadata, embedding)
        VALUES ($1, $2, $3, $4, $5, $6)
        ON CONFLICT (certificate_id) 
        DO UPDATE SET 
          address = EXCLUDED.address,
          description = EXCLUDED.description,
          metadata = EXCLUDED.metadata,
          embedding = EXCLUDED.embedding,
          updated_at = CURRENT_TIMESTAMP
        RETURNING id
      `;

      const values = [certificateId, userId, address, description, metadata, `[${embedding.join(',')}]`];
      const result = await pool.query(query, values);

      console.log(`[EmbeddingService] Embedding salvo para certificado ${certificateId}`);
      return result.rows[0].id;
    } catch (error) {
      console.error('[EmbeddingService] Erro ao salvar embedding de certificado:', error);
      throw error;
    }
  }

  /**
   * Salvar embedding de gravação
   */
  static async saveRecordingEmbedding(recordingData) {
    try {
      const { recordingId, userId, title, description, transcript, metadata } = recordingData;
      
      // Criar texto para embedding
      const textForEmbedding = `
        Gravação: ${title || ''}
        Descrição: ${description || ''}
        Transcrição: ${transcript || ''}
        Metadados: ${JSON.stringify(metadata || {})}
      `.trim();

      // Gerar embedding
      const embedding = await this.generateEmbedding(textForEmbedding);

      // Salvar no banco
      const query = `
        INSERT INTO recording_embeddings 
        (recording_id, user_id, title, description, transcript, metadata, embedding)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        ON CONFLICT (recording_id) 
        DO UPDATE SET 
          title = EXCLUDED.title,
          description = EXCLUDED.description,
          transcript = EXCLUDED.transcript,
          metadata = EXCLUDED.metadata,
          embedding = EXCLUDED.embedding,
          updated_at = CURRENT_TIMESTAMP
        RETURNING id
      `;

      const values = [recordingId, userId, title, description, transcript, metadata, `[${embedding.join(',')}]`];
      const result = await pool.query(query, values);

      console.log(`[EmbeddingService] Embedding salvo para gravação ${recordingId}`);
      return result.rows[0].id;
    } catch (error) {
      console.error('[EmbeddingService] Erro ao salvar embedding de gravação:', error);
      throw error;
    }
  }

  /**
   * Salvar embedding de documento OCR
   */
  static async saveOCRDocumentEmbedding(documentData) {
    try {
      const { documentId, userId, filename, extractedText, documentType, metadata } = documentData;
      
      // Criar texto para embedding
      const textForEmbedding = `
        Documento OCR: ${filename || ''}
        Tipo: ${documentType || ''}
        Texto extraído: ${extractedText || ''}
        Metadados: ${JSON.stringify(metadata || {})}
      `.trim();

      // Gerar embedding
      const embedding = await this.generateEmbedding(textForEmbedding);

      // Salvar no banco
      const query = `
        INSERT INTO ocr_document_embeddings 
        (document_id, user_id, filename, extracted_text, document_type, metadata, embedding)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        ON CONFLICT (document_id) 
        DO UPDATE SET 
          filename = EXCLUDED.filename,
          extracted_text = EXCLUDED.extracted_text,
          document_type = EXCLUDED.document_type,
          metadata = EXCLUDED.metadata,
          embedding = EXCLUDED.embedding,
          updated_at = CURRENT_TIMESTAMP
        RETURNING id
      `;

      const values = [documentId, userId, filename, extractedText, documentType, metadata, `[${embedding.join(',')}]`];
      const result = await pool.query(query, values);

      console.log(`[EmbeddingService] Embedding salvo para documento OCR ${documentId}`);
      return result.rows[0].id;
    } catch (error) {
      console.error('[EmbeddingService] Erro ao salvar embedding de documento OCR:', error);
      throw error;
    }
  }

  /**
   * Salvar embedding de dados de Timeline
   */
  static async saveTimelineLocationEmbedding(timelineData) {
    try {
      const { validationId, userId, address, locationSummary, timePeriodDescription, validationResult, metadata } = timelineData;
      
      // Criar texto para embedding
      const textForEmbedding = `
        Validação de Residência Timeline
        Endereço: ${address || ''}
        Resumo de localização: ${locationSummary || ''}
        Período: ${timePeriodDescription || ''}
        Resultado: ${JSON.stringify(validationResult || {})}
        Metadados: ${JSON.stringify(metadata || {})}
      `.trim();

      // Gerar embedding
      const embedding = await this.generateEmbedding(textForEmbedding);

      // Salvar no banco
      const query = `
        INSERT INTO timeline_location_embeddings 
        (validation_id, user_id, address, location_summary, time_period_description, validation_result, metadata, embedding)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        ON CONFLICT (validation_id) 
        DO UPDATE SET 
          address = EXCLUDED.address,
          location_summary = EXCLUDED.location_summary,
          time_period_description = EXCLUDED.time_period_description,
          validation_result = EXCLUDED.validation_result,
          metadata = EXCLUDED.metadata,
          embedding = EXCLUDED.embedding,
          updated_at = CURRENT_TIMESTAMP
        RETURNING id
      `;

      const values = [validationId, userId, address, locationSummary, timePeriodDescription, validationResult, metadata, `[${embedding.join(',')}]`];
      const result = await pool.query(query, values);

      console.log(`[EmbeddingService] Embedding salvo para Timeline ${validationId}`);
      return result.rows[0].id;
    } catch (error) {
      console.error('[EmbeddingService] Erro ao salvar embedding de Timeline:', error);
      throw error;
    }
  }

  /**
   * Busca semântica unificada
   */
  static async searchSemantic(queryText, options = {}) {
    try {
      const {
        userId = null,
        similarityThreshold = 0.7,
        maxResults = 10,
        contentTypes = ['residence_certificate', 'recording', 'ocr_document', 'timeline_location']
      } = options;

      // Gerar embedding da consulta
      const queryEmbedding = await this.generateEmbedding(queryText);

      // Executar busca semântica
      const query = `
        SELECT * FROM search_semantic_content(
          $1::vector,
          $2::float,
          $3::integer,
          $4::varchar
        )
      `;

      const values = [`[${queryEmbedding.join(',')}]`, similarityThreshold, maxResults, userId];
      const result = await pool.query(query, values);

      // Filtrar por tipos de conteúdo se especificado
      const filteredResults = result.rows.filter(row => 
        contentTypes.includes(row.content_type)
      );

      console.log(`[EmbeddingService] Busca semântica retornou ${filteredResults.length} resultados`);
      return filteredResults;
    } catch (error) {
      console.error('[EmbeddingService] Erro na busca semântica:', error);
      throw error;
    }
  }

  /**
   * Buscar conteúdo similar
   */
  static async findSimilarContent(contentType, contentId, maxResults = 5) {
    try {
      let tableName, idColumn;
      
      switch (contentType) {
        case 'residence_certificate':
          tableName = 'residence_certificate_embeddings';
          idColumn = 'certificate_id';
          break;
        case 'recording':
          tableName = 'recording_embeddings';
          idColumn = 'recording_id';
          break;
        case 'ocr_document':
          tableName = 'ocr_document_embeddings';
          idColumn = 'document_id';
          break;
        case 'timeline_location':
          tableName = 'timeline_location_embeddings';
          idColumn = 'validation_id';
          break;
        default:
          throw new Error(`Tipo de conteúdo não suportado: ${contentType}`);
      }

      // Obter embedding do conteúdo de referência
      const refQuery = `SELECT embedding FROM ${tableName} WHERE ${idColumn} = $1`;
      const refResult = await pool.query(refQuery, [contentId]);
      
      if (refResult.rows.length === 0) {
        throw new Error(`Conteúdo não encontrado: ${contentId}`);
      }

      const referenceEmbedding = refResult.rows[0].embedding;

      // Buscar conteúdo similar
      const searchQuery = `
        SELECT 
          '${contentType}'::varchar as content_type,
          ${idColumn} as content_id,
          (1 - (embedding <=> $1::vector))::float as similarity_score,
          created_at
        FROM ${tableName}
        WHERE ${idColumn} != $2
        ORDER BY embedding <=> $1::vector
        LIMIT $3
      `;

      const result = await pool.query(searchQuery, [referenceEmbedding, contentId, maxResults]);
      
      console.log(`[EmbeddingService] Encontrados ${result.rows.length} itens similares para ${contentId}`);
      return result.rows;
    } catch (error) {
      console.error('[EmbeddingService] Erro ao buscar conteúdo similar:', error);
      throw error;
    }
  }

  /**
   * Estatísticas dos embeddings
   */
  static async getEmbeddingStats() {
    try {
      const queries = [
        'SELECT COUNT(*) as count FROM residence_certificate_embeddings',
        'SELECT COUNT(*) as count FROM recording_embeddings',
        'SELECT COUNT(*) as count FROM ocr_document_embeddings',
        'SELECT COUNT(*) as count FROM timeline_location_embeddings'
      ];

      const results = await Promise.all(
        queries.map(query => pool.query(query))
      );

      return {
        residence_certificates: parseInt(results[0].rows[0].count),
        recordings: parseInt(results[1].rows[0].count),
        ocr_documents: parseInt(results[2].rows[0].count),
        timeline_locations: parseInt(results[3].rows[0].count),
        total: results.reduce((sum, result) => sum + parseInt(result.rows[0].count), 0)
      };
    } catch (error) {
      console.error('[EmbeddingService] Erro ao obter estatísticas:', error);
      throw error;
    }
  }
}

module.exports = EmbeddingService;
