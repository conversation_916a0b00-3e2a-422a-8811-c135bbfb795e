/**
 * Rotas para assinatura digital
 */
const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const digitalSignatureService = require('../services/digitalSignatureService');
const authenticateApiKey = require('../middleware/authMiddleware');
const validateRequest = require('../middleware/validationMiddleware');
const logger = require('../utils/logger');

// Configuração de armazenamento para upload de arquivos
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../storage/documents');
    
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueName = `${Date.now()}-${uuidv4()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

const fileFilter = (req, file, cb) => {
  // Aceitar apenas PDFs para assinatura
  if (file.mimetype === 'application/pdf') {
    cb(null, true);
  } else {
    cb(new Error('Formato de arquivo não suportado. Apenas PDFs são aceitos para assinatura.'), false);
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
  }
});

/**
 * @swagger
 * /api/signature:
 *   get:
 *     summary: Informações sobre o serviço de assinatura digital
 *     tags: [Digital Signature]
 *     responses:
 *       200:
 *         description: Informações do serviço
 */
router.get('/', (req, res) => {
  res.json({
    success: true,
    service: 'Serviço de Assinatura Digital',
    version: '1.0.0',
    availableEndpoints: [
      'POST /api/signature/sign - Assinar documento PDF',
      'GET /api/signature/verify/:id - Verificar assinatura',
      'GET /api/signature/download/:id - Download documento assinado',
      'GET /api/signature/status/:id - Status da assinatura',
      'POST /api/signature/batch - Assinatura em lote',
      'GET /api/signature/history - Histórico de assinaturas'
    ],
    supportedFormats: ['application/pdf'],
    maxFileSize: '10MB',
    features: [
      'Assinatura digital ICP-Brasil',
      'Certificados A1 e A3',
      'Validação de cadeia de certificação',
      'Carimbo de tempo integrado',
      'Assinatura em lote',
      'Auditoria completa',
      'Verificação de integridade'
    ],
    security: [
      'Autenticação por API Key',
      'Criptografia AES-256',
      'Hash SHA-256',
      'Certificados X.509'
    ],
    timestamp: new Date().toISOString()
  });
});

/**
 * @swagger
 * /api/signature/sign:
 *   post:
 *     summary: Assinar um documento PDF
 *     tags: [Assinaturas Digitais]
 *     security:
 *       - apiKey: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - document
 *               - certId
 *             properties:
 *               document:
 *                 type: string
 *                 format: binary
 *                 description: Arquivo PDF para assinar
 *               certId:
 *                 type: string
 *                 description: ID do certificado a ser usado
 *               signerName:
 *                 type: string
 *                 description: Nome do assinante
 *               reason:
 *                 type: string
 *                 description: Motivo da assinatura
 *               location:
 *                 type: string
 *                 description: Local da assinatura
 *               page:
 *                 type: integer
 *                 description: Página para adicionar a assinatura (0-indexada)
 *               x:
 *                 type: integer
 *                 description: Posição X da assinatura
 *               y:
 *                 type: integer
 *                 description: Posição Y da assinatura
 *               addTimestamp:
 *                 type: boolean
 *                 description: Se deve adicionar carimbo de tempo
 *     responses:
 *       200:
 *         description: Documento assinado com sucesso
 */
router.post('/sign', 
  authenticateApiKey, 
  upload.single('document'),
  validateRequest(['certId']),
  async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: 'Nenhum documento enviado'
        });
      }
      
      // Obter parâmetros da requisição
      const certId = req.body.certId;
      const addTimestamp = req.body.addTimestamp !== 'false';
      
      // Opções de assinatura
      const signatureOptions = {
        signerName: req.body.signerName,
        reason: req.body.reason || 'Assinatura digital de documento',
        location: req.body.location || 'Brasil',
        page: req.body.page ? parseInt(req.body.page) : undefined,
        x: req.body.x ? parseInt(req.body.x) : undefined,
        y: req.body.y ? parseInt(req.body.y) : undefined,
        showTimestamp: addTimestamp,
        originalFilename: req.file.originalname
      };
      
      // Ler o arquivo
      const pdfBuffer = fs.readFileSync(req.file.path);
      
      // Assinar o documento
      const signatureResult = await digitalSignatureService.signPDF(
        pdfBuffer,
        certId,
        signatureOptions,
        addTimestamp
      );
      
      // Gerar nome para o arquivo assinado
      const signedFilename = `${path.basename(req.file.path, '.pdf')}_signed.pdf`;
      const signedFilePath = path.join(path.dirname(req.file.path), signedFilename);
      
      // Salvar o arquivo assinado
      fs.writeFileSync(signedFilePath, signatureResult.signedPDF);
      
      // Criar URL para download
      const downloadUrl = `/api/signature/download/${signedFilename}`;
      
      res.json({
        success: true,
        message: 'Documento assinado com sucesso',
        signatureId: signatureResult.signatureData.id,
        filename: signedFilename,
        downloadUrl,
        documentInfo: {
          originalFilename: req.file.originalname,
          signedFilename,
          size: signatureResult.signedPDF.length
        },
        signatureInfo: {
          id: signatureResult.signatureData.id,
          signedAt: signatureResult.signatureData.signedAt,
          signedBy: signatureResult.signatureData.certificate.subject,
          hasTimestamp: !!signatureResult.signatureData.timestamp
        }
      });
      
      // Remover arquivo original após processamento
      fs.unlinkSync(req.file.path);
      
    } catch (error) {
      logger.error('Erro ao assinar documento:', error);
      
      // Limpar arquivo em caso de erro
      if (req.file && req.file.path && fs.existsSync(req.file.path)) {
        fs.unlinkSync(req.file.path);
      }
      
      res.status(500).json({
        success: false,
        message: 'Erro ao assinar documento',
        error: error.message
      });
    }
  }
);

/**
 * @swagger
 * /api/signature/verify:
 *   post:
 *     summary: Verificar assinatura de um documento PDF
 *     tags: [Assinaturas Digitais]
 *     security:
 *       - apiKey: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - document
 *               - signatureId
 *             properties:
 *               document:
 *                 type: string
 *                 format: binary
 *                 description: Documento PDF assinado
 *               signatureId:
 *                 type: string
 *                 description: ID da assinatura a verificar
 *     responses:
 *       200:
 *         description: Resultado da verificação da assinatura
 */
router.post('/verify',
  authenticateApiKey,
  upload.single('document'),
  validateRequest(['signatureId']),
  async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: 'Nenhum documento enviado'
        });
      }
      
      // Obter ID da assinatura
      const signatureId = req.body.signatureId;
      
      // Ler o arquivo
      const pdfBuffer = fs.readFileSync(req.file.path);
      
      // Verificar assinatura
      const verificationResult = await digitalSignatureService.verifySignature(
        pdfBuffer,
        signatureId
      );
      
      res.json({
        success: true,
        valid: verificationResult.valid,
        details: {
          documentIntegrity: verificationResult.hashMatch,
          signatureValid: verificationResult.signatureVerified,
          certificateValid: verificationResult.certificateValid,
          timestampVerified: verificationResult.timestampVerified
        },
        signature: verificationResult.signatureInfo
      });
      
      // Remover arquivo após processamento
      fs.unlinkSync(req.file.path);
      
    } catch (error) {
      logger.error('Erro ao verificar assinatura:', error);
      
      // Limpar arquivo em caso de erro
      if (req.file && req.file.path && fs.existsSync(req.file.path)) {
        fs.unlinkSync(req.file.path);
      }
      
      res.status(500).json({
        success: false,
        message: 'Erro ao verificar assinatura',
        error: error.message
      });
    }
  }
);

/**
 * @swagger
 * /api/signature/download/{filename}:
 *   get:
 *     summary: Download de um documento assinado
 *     tags: [Assinaturas Digitais]
 *     security:
 *       - apiKey: []
 *     parameters:
 *       - in: path
 *         name: filename
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Documento PDF assinado
 *         content:
 *           application/pdf:
 *             schema:
 *               type: string
 *               format: binary
 */
router.get('/download/:filename',
  authenticateApiKey,
  (req, res) => {
    try {
      const filename = req.params.filename;
      const filePath = path.join(__dirname, '../../storage/documents', filename);
      
      if (!fs.existsSync(filePath)) {
        return res.status(404).json({
          success: false,
          message: 'Arquivo não encontrado'
        });
      }
      
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename=${filename}`);
      
      fs.createReadStream(filePath).pipe(res);
      
    } catch (error) {
      logger.error('Erro ao fazer download de documento:', error);
      
      res.status(500).json({
        success: false,
        message: 'Erro ao fazer download do documento',
        error: error.message
      });
    }
  }
);

module.exports = router;
