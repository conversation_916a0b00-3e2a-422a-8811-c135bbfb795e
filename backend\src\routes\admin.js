const express = require('express');
const { body, validationResult } = require('express-validator');
const { requireAdmin } = require('../middleware/authMiddleware');
const { getDatabase } = require('../database');
const bcrypt = require('bcrypt');

const router = express.Router();

/**
 * @swagger
 * /api/admin/users:
 *   get:
 *     summary: Listar todos os usuários (Admin)
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Lista de usuários
 *       403:
 *         description: Acesso negado
 */
router.get('/users', requireAdmin, async (req, res) => {
  try {
    const db = getDatabase();
    
    const query = `
      SELECT 
        id, username, email, full_name, role, is_active, last_login, created_at,
        storage_quota_mb, storage_used_mb, storage_plan,
        (SELECT COUNT(*) FROM recordings WHERE user_id = users.id) as recordings_count
      FROM users 
      ORDER BY created_at DESC
    `;
    
    const result = await db.query(query);
    
    res.json({
      success: true,
      data: result.rows.map(user => ({
        id: user.id,
        username: user.username,
        email: user.email,
        fullName: user.full_name,
        role: user.role,
        isActive: user.is_active,
        lastLogin: user.last_login,
        createdAt: user.created_at,
        recordingsCount: parseInt(user.recordings_count),
        storage: {
          quotaMB: parseFloat(user.storage_quota_mb),
          usedMB: parseFloat(user.storage_used_mb),
          plan: user.storage_plan,
          usagePercent: Math.round((user.storage_used_mb / user.storage_quota_mb) * 100)
        }
      }))
    });
  } catch (error) {
    console.error('❌ Erro ao listar usuários:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor'
    });
  }
});

/**
 * @swagger
 * /api/admin/users:
 *   post:
 *     summary: Criar novo usuário (Admin)
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 */
router.post('/users', requireAdmin, [
  body('username').isLength({ min: 3, max: 50 }).withMessage('Username deve ter entre 3 e 50 caracteres'),
  body('email').isEmail().withMessage('Email inválido'),
  body('password').isLength({ min: 6 }).withMessage('Senha deve ter pelo menos 6 caracteres'),
  body('fullName').isLength({ min: 2, max: 255 }).withMessage('Nome completo deve ter entre 2 e 255 caracteres'),
  body('role').isIn(['admin', 'operador', 'visualizador', 'auditor']).withMessage('Role inválido'),
  body('storagePlan').optional().isIn(['basic', 'premium', 'enterprise']).withMessage('Plano de armazenamento inválido'),
  body('storageQuotaMB').optional().isInt({ min: 1, max: 10000 }).withMessage('Cota deve ser entre 1MB e 10GB')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const { username, email, password, fullName, role, storagePlan = 'basic', storageQuotaMB } = req.body;
    
    // Definir cota baseada no plano se não especificada
    let quota = storageQuotaMB;
    if (!quota) {
      switch (storagePlan) {
        case 'basic': quota = 50; break;
        case 'premium': quota = 150; break;
        case 'enterprise': quota = 500; break;
        default: quota = 50;
      }
    }

    const db = getDatabase();
    
    // Verificar se username ou email já existem
    const existingUser = await db.query(
      'SELECT id FROM users WHERE username = $1 OR email = $2',
      [username, email]
    );
    
    if (existingUser.rows.length > 0) {
      return res.status(400).json({
        success: false,
        error: 'Username ou email já existem'
      });
    }

    // Hash da senha
    const hashedPassword = await bcrypt.hash(password, 12);

    // Criar usuário
    const query = `
      INSERT INTO users (username, email, password_hash, full_name, role, storage_quota_mb, storage_plan)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING id, username, email, full_name, role, created_at, storage_quota_mb, storage_plan
    `;
    
    const result = await db.query(query, [
      username, email, hashedPassword, fullName, role, quota, storagePlan
    ]);
    
    const newUser = result.rows[0];
    
    console.log(`✅ Usuário criado: ${newUser.username} (${newUser.role})`);
    
    res.status(201).json({
      success: true,
      message: 'Usuário criado com sucesso',
      data: {
        id: newUser.id,
        username: newUser.username,
        email: newUser.email,
        fullName: newUser.full_name,
        role: newUser.role,
        createdAt: newUser.created_at,
        storage: {
          quotaMB: parseFloat(newUser.storage_quota_mb),
          usedMB: 0,
          plan: newUser.storage_plan,
          usagePercent: 0
        }
      }
    });
  } catch (error) {
    console.error('❌ Erro ao criar usuário:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor'
    });
  }
});

/**
 * @swagger
 * /api/admin/users/{id}:
 *   put:
 *     summary: Atualizar usuário (Admin)
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 */
router.put('/users/:id', requireAdmin, [
  body('username').optional().isLength({ min: 3, max: 50 }),
  body('email').optional().isEmail(),
  body('fullName').optional().isLength({ min: 2, max: 255 }),
  body('role').optional().isIn(['admin', 'operador', 'visualizador', 'auditor']),
  body('isActive').optional().isBoolean(),
  body('storagePlan').optional().isIn(['basic', 'premium', 'enterprise']),
  body('storageQuotaMB').optional().isInt({ min: 1, max: 10000 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const userId = req.params.id;
    const updates = req.body;
    
    const db = getDatabase();
    
    // Verificar se usuário existe
    const existingUser = await db.query('SELECT * FROM users WHERE id = $1', [userId]);
    if (existingUser.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Usuário não encontrado'
      });
    }

    // Construir query de atualização dinamicamente
    const updateFields = [];
    const values = [];
    let paramIndex = 1;

    Object.keys(updates).forEach(key => {
      switch (key) {
        case 'username':
          updateFields.push(`username = $${paramIndex}`);
          values.push(updates[key]);
          paramIndex++;
          break;
        case 'email':
          updateFields.push(`email = $${paramIndex}`);
          values.push(updates[key]);
          paramIndex++;
          break;
        case 'fullName':
          updateFields.push(`full_name = $${paramIndex}`);
          values.push(updates[key]);
          paramIndex++;
          break;
        case 'role':
          updateFields.push(`role = $${paramIndex}`);
          values.push(updates[key]);
          paramIndex++;
          break;
        case 'isActive':
          updateFields.push(`is_active = $${paramIndex}`);
          values.push(updates[key]);
          paramIndex++;
          break;
        case 'storagePlan':
          updateFields.push(`storage_plan = $${paramIndex}`);
          values.push(updates[key]);
          paramIndex++;
          break;
        case 'storageQuotaMB':
          updateFields.push(`storage_quota_mb = $${paramIndex}`);
          values.push(updates[key]);
          paramIndex++;
          break;
      }
    });

    if (updateFields.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Nenhum campo válido para atualizar'
      });
    }

    // Adicionar updated_at
    updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
    values.push(userId);

    const query = `
      UPDATE users 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING id, username, email, full_name, role, is_active, storage_quota_mb, storage_used_mb, storage_plan, updated_at
    `;

    const result = await db.query(query, values);
    const updatedUser = result.rows[0];

    console.log(`✅ Usuário atualizado: ${updatedUser.username}`);

    res.json({
      success: true,
      message: 'Usuário atualizado com sucesso',
      data: {
        id: updatedUser.id,
        username: updatedUser.username,
        email: updatedUser.email,
        fullName: updatedUser.full_name,
        role: updatedUser.role,
        isActive: updatedUser.is_active,
        updatedAt: updatedUser.updated_at,
        storage: {
          quotaMB: parseFloat(updatedUser.storage_quota_mb),
          usedMB: parseFloat(updatedUser.storage_used_mb),
          plan: updatedUser.storage_plan,
          usagePercent: Math.round((updatedUser.storage_used_mb / updatedUser.storage_quota_mb) * 100)
        }
      }
    });
  } catch (error) {
    console.error('❌ Erro ao atualizar usuário:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor'
    });
  }
});

module.exports = router;
