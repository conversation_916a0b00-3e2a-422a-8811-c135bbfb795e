import React, { useState, useEffect } from 'react';
import {
  Container,
  Typo<PERSON>,
  <PERSON>,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Tabs,
  Tab,
  LinearProgress,
  Badge,
  Avatar,
  List,
  ListItem,
  ListItemText,
  Switch
} from '@mui/material';
import {
  AdminPanelSettings,
  People,
  Storage,
  Security,
  Analytics,
  Edit,
  Delete,
  Add,
  CheckCircle,
  Error,
  Warning,
  Computer,
  Group,
  TrendingUp
} from '@mui/icons-material';
import { useToast } from '../components/Toast';

interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'operador' | 'visualizador' | 'auditor';
  status: 'active' | 'inactive';
  lastLogin: string;
  createdAt: string;
  recordingsCount: number;
}

interface SystemMetrics {
  totalUsers: number;
  activeUsers: number;
  totalRecordings: number;
  storageUsed: number;
  storageLimit: number;
  systemHealth: 'healthy' | 'warning' | 'error';
  uptime: string;
}

interface AuditLog {
  id: string;
  user: string;
  action: string;
  resource: string;
  timestamp: string;
  ipAddress: string;
  status: 'success' | 'warning' | 'error';
}

const AdminPage: React.FC = () => {
  const [currentTab, setCurrentTab] = useState(0);
  const [users, setUsers] = useState<User[]>([]);
  const [metrics, setMetrics] = useState<SystemMetrics | null>(null);
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    role: 'visualizador' as User['role'],
    status: 'active' as User['status']
  });
  const { showSuccess, showError } = useToast();

  // Mock data - Em produção, buscar da API
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      
      // Simular delay de API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock users
      setUsers([
        {
          id: '1',
          name: 'João Silva',
          email: '<EMAIL>',
          role: 'admin',
          status: 'active',
          lastLogin: '2024-01-17 10:30',
          createdAt: '2024-01-01',
          recordingsCount: 45
        },
        {
          id: '2',
          name: 'Maria Santos',
          email: '<EMAIL>',
          role: 'operador',
          status: 'active',
          lastLogin: '2024-01-17 09:15',
          createdAt: '2024-01-05',
          recordingsCount: 32
        },
        {
          id: '3',
          name: 'Pedro Costa',
          email: '<EMAIL>',
          role: 'visualizador',
          status: 'inactive',
          lastLogin: '2024-01-15 16:20',
          createdAt: '2024-01-10',
          recordingsCount: 8
        }
      ]);

      // Mock metrics
      setMetrics({
        totalUsers: 15,
        activeUsers: 12,
        totalRecordings: 1247,
        storageUsed: 45.6,
        storageLimit: 100,
        systemHealth: 'healthy',
        uptime: '15 dias, 6 horas'
      });

      // Mock audit logs
      setAuditLogs([
        {
          id: '1',
          user: 'João Silva',
          action: 'Login',
          resource: 'Sistema',
          timestamp: '2024-01-17 10:30:15',
          ipAddress: '*************',
          status: 'success'
        },
        {
          id: '2',
          user: 'Maria Santos',
          action: 'Criação de Gravação',
          resource: 'Gravação #1248',
          timestamp: '2024-01-17 10:25:32',
          ipAddress: '*************',
          status: 'success'
        },
        {
          id: '3',
          user: 'Sistema',
          action: 'Backup Automático',
          resource: 'Base de Dados',
          timestamp: '2024-01-17 02:00:00',
          ipAddress: 'localhost',
          status: 'warning'
        }
      ]);

      setLoading(false);
    };

    loadData();
  }, []);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };

  const handleOpenDialog = (user?: User) => {
    if (user) {
      setSelectedUser(user);
      setFormData({
        name: user.name,
        email: user.email,
        role: user.role,
        status: user.status
      });
    } else {
      setSelectedUser(null);
      setFormData({
        name: '',
        email: '',
        role: 'visualizador',
        status: 'active'
      });
    }
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setSelectedUser(null);
  };

  const handleSaveUser = async () => {
    try {
      if (selectedUser) {
        // Atualizar usuário
        setUsers(prev => prev.map(user => 
          user.id === selectedUser.id 
            ? { ...user, ...formData }
            : user
        ));
        showSuccess('Usuário atualizado com sucesso!');
      } else {
        // Criar novo usuário
        const newUser: User = {
          id: Date.now().toString(),
          ...formData,
          lastLogin: 'Nunca',
          createdAt: new Date().toISOString().split('T')[0],
          recordingsCount: 0
        };
        setUsers(prev => [...prev, newUser]);
        showSuccess('Usuário criado com sucesso!');
      }
      handleCloseDialog();
    } catch (error) {
      showError('Erro ao salvar usuário');
    }
  };

  const handleDeleteUser = async (userId: string) => {
    if (window.confirm('Tem certeza que deseja excluir este usuário?')) {
      try {
        setUsers(prev => prev.filter(user => user.id !== userId));
        showSuccess('Usuário excluído com sucesso!');
      } catch (error) {
        showError('Erro ao excluir usuário');
      }
    }
  };

  const getRoleColor = (role: User['role']) => {
    switch (role) {
      case 'admin': return 'error';
      case 'operador': return 'primary';
      case 'auditor': return 'warning';
      case 'visualizador': return 'default';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return <CheckCircle color="success" />;
      case 'warning': return <Warning color="warning" />;
      case 'error': return <Error color="error" />;
      default: return <CheckCircle color="info" />;
    }
  };

  if (loading) {
    return (
      <Container maxWidth="xl">
        <Box sx={{ py: 4 }}>
          <LinearProgress />
          <Typography variant="h6" sx={{ mt: 2, textAlign: 'center' }}>
            Carregando painel administrativo...
          </Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl">
      <Box sx={{ py: 4 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
          <AdminPanelSettings sx={{ fontSize: 40, mr: 2, color: 'primary.main' }} />
          <Box>
            <Typography variant="h4" component="h1">
              Painel Administrativo
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Gerencie usuários, monitore o sistema e visualize relatórios
            </Typography>
          </Box>
        </Box>

        {/* System Health Alert */}
        {metrics?.systemHealth !== 'healthy' && (
          <Alert 
            severity={metrics?.systemHealth === 'warning' ? 'warning' : 'error'} 
            sx={{ mb: 3 }}
          >
            Sistema com {metrics?.systemHealth === 'warning' ? 'avisos' : 'problemas'}. 
            Verifique os logs para mais detalhes.
          </Alert>
        )}

        {/* Metrics Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Group sx={{ fontSize: 40, color: 'primary.main', mr: 2 }} />
                  <Box>
                    <Typography variant="h4">
                      {metrics?.totalUsers}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total de Usuários
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <CheckCircle sx={{ fontSize: 40, color: 'success.main', mr: 2 }} />
                  <Box>
                    <Typography variant="h4">
                      {metrics?.activeUsers}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Usuários Ativos
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <TrendingUp sx={{ fontSize: 40, color: 'info.main', mr: 2 }} />
                  <Box>
                    <Typography variant="h4">
                      {metrics?.totalRecordings}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total de Gravações
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Storage sx={{ fontSize: 40, color: 'warning.main', mr: 2 }} />
                  <Box>
                    <Typography variant="h4">
                      {metrics?.storageUsed}GB
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Armazenamento Usado
                    </Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={(metrics?.storageUsed || 0) / (metrics?.storageLimit || 1) * 100}
                      sx={{ mt: 1 }}
                    />
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Tabs */}
        <Paper sx={{ mb: 3 }}>
          <Tabs value={currentTab} onChange={handleTabChange}>
            <Tab icon={<People />} label="Usuários" />
            <Tab icon={<Security />} label="Logs de Auditoria" />
            <Tab icon={<Computer />} label="Sistema" />
            <Tab icon={<Analytics />} label="Relatórios" />
          </Tabs>
        </Paper>

        {/* Tab Content */}
        {currentTab === 0 && (
          <Paper>
            <Box sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h6">
                  Gerenciamento de Usuários
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<Add />}
                  onClick={() => handleOpenDialog()}
                >
                  Novo Usuário
                </Button>
              </Box>

              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Nome</TableCell>
                      <TableCell>Email</TableCell>
                      <TableCell>Perfil</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Último Login</TableCell>
                      <TableCell>Gravações</TableCell>
                      <TableCell>Ações</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {users.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
                              {user.name.charAt(0)}
                            </Avatar>
                            {user.name}
                          </Box>
                        </TableCell>
                        <TableCell>{user.email}</TableCell>
                        <TableCell>
                          <Chip 
                            label={user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                            color={getRoleColor(user.role)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Chip 
                            label={user.status === 'active' ? 'Ativo' : 'Inativo'}
                            color={user.status === 'active' ? 'success' : 'default'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>{user.lastLogin}</TableCell>
                        <TableCell>
                          <Badge badgeContent={user.recordingsCount} color="primary">
                            <Computer />
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <IconButton onClick={() => handleOpenDialog(user)}>
                            <Edit />
                          </IconButton>
                          <IconButton 
                            onClick={() => handleDeleteUser(user.id)}
                            color="error"
                          >
                            <Delete />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          </Paper>
        )}

        {currentTab === 1 && (
          <Paper>
            <Box sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ mb: 3 }}>
                Logs de Auditoria
              </Typography>
              
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Status</TableCell>
                      <TableCell>Usuário</TableCell>
                      <TableCell>Ação</TableCell>
                      <TableCell>Recurso</TableCell>
                      <TableCell>Data/Hora</TableCell>
                      <TableCell>IP</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {auditLogs.map((log) => (
                      <TableRow key={log.id}>
                        <TableCell>
                          {getStatusIcon(log.status)}
                        </TableCell>
                        <TableCell>{log.user}</TableCell>
                        <TableCell>{log.action}</TableCell>
                        <TableCell>{log.resource}</TableCell>
                        <TableCell>{log.timestamp}</TableCell>
                        <TableCell>{log.ipAddress}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          </Paper>
        )}

        {currentTab === 2 && (
          <Paper>
            <Box sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ mb: 3 }}>
                Configurações do Sistema
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardHeader title="Status do Sistema" />
                    <CardContent>
                      <List>
                        <ListItem>
                          <ListItemText primary="Tempo de Atividade" secondary={metrics?.uptime} />
                          <CheckCircle color="success" />
                        </ListItem>
                        <ListItem>
                          <ListItemText primary="Backup Automático" secondary="Habilitado" />
                          <Switch checked />
                        </ListItem>
                        <ListItem>
                          <ListItemText primary="Monitoramento" secondary="Ativo" />
                          <CheckCircle color="success" />
                        </ListItem>
                      </List>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Card>
                    <CardHeader title="Configurações de Segurança" />
                    <CardContent>
                      <List>
                        <ListItem>
                          <ListItemText primary="SSL/TLS" secondary="Certificado válido" />
                          <CheckCircle color="success" />
                        </ListItem>
                        <ListItem>
                          <ListItemText primary="Firewall" secondary="Ativo" />
                          <CheckCircle color="success" />
                        </ListItem>
                        <ListItem>
                          <ListItemText primary="Logs de Auditoria" secondary="Habilitado" />
                          <CheckCircle color="success" />
                        </ListItem>
                      </List>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          </Paper>
        )}

        {currentTab === 3 && (
          <Paper>
            <Box sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ mb: 3 }}>
                Relatórios e Analytics
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardHeader title="Uso por Usuário" />
                    <CardContent>
                      <Typography variant="body2" color="text.secondary">
                        Relatório de utilização do sistema por usuário nos últimos 30 dias.
                      </Typography>
                      <Button variant="outlined" sx={{ mt: 2 }}>
                        Gerar Relatório
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Card>
                    <CardHeader title="Armazenamento" />
                    <CardContent>
                      <Typography variant="body2" color="text.secondary">
                        Análise de uso de armazenamento e projeções de crescimento.
                      </Typography>
                      <Button variant="outlined" sx={{ mt: 2 }}>
                        Ver Detalhes
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          </Paper>
        )}

        {/* User Dialog */}
        <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
          <DialogTitle>
            {selectedUser ? 'Editar Usuário' : 'Novo Usuário'}
          </DialogTitle>
          <DialogContent>
            <TextField
              autoFocus
              margin="dense"
              label="Nome Completo"
              fullWidth
              variant="outlined"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              sx={{ mb: 2 }}
            />
            <TextField
              margin="dense"
              label="Email"
              type="email"
              fullWidth
              variant="outlined"
              value={formData.email}
              onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
              sx={{ mb: 2 }}
            />
            <FormControl fullWidth variant="outlined" sx={{ mb: 2 }}>
              <InputLabel>Perfil</InputLabel>
              <Select
                value={formData.role}
                onChange={(e) => setFormData(prev => ({ ...prev, role: e.target.value as User['role'] }))}
                label="Perfil"
              >
                <MenuItem value="admin">Administrador</MenuItem>
                <MenuItem value="operador">Operador</MenuItem>
                <MenuItem value="auditor">Auditor</MenuItem>
                <MenuItem value="visualizador">Visualizador</MenuItem>
              </Select>
            </FormControl>
            <FormControl fullWidth variant="outlined">
              <InputLabel>Status</InputLabel>
              <Select
                value={formData.status}
                onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as User['status'] }))}
                label="Status"
              >
                <MenuItem value="active">Ativo</MenuItem>
                <MenuItem value="inactive">Inativo</MenuItem>
              </Select>
            </FormControl>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Cancelar</Button>
            <Button onClick={handleSaveUser} variant="contained">
              {selectedUser ? 'Atualizar' : 'Criar'}
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </Container>
  );
};

export default AdminPage;
