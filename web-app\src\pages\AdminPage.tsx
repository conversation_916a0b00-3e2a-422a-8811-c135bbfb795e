import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  <PERSON>,
  Grid,
  Card,
  Card<PERSON>ontent,
  CardHeader,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Tabs,
  Tab,
  LinearProgress,
  Badge,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Switch,
  FormControlLabel,
  Divider,
  AlertTitle
} from '@mui/material';
import {
  AdminPanelSettings,
  People,
  Storage,
  Security,
  Analytics,
  Edit,
  Delete,
  Add,
  CheckCircle,
  Error,
  Settings,
  Backup,
  CloudDownload,
  CloudUpload,
  Schedule,
  Notifications,
  VpnKey,
  Shield,
  Assessment,
  Timeline,
  Group,
  PersonAdd,
  Block,
  Restore,
  DeleteForever,
  Email,
  Sms,
  Phone,
  Language,
  Palette,
  DarkMode,
  LightMode,
  Update,
  SystemUpdate,
  BugReport,
  Speed,
  Memory,
  NetworkCheck,
  Warning,
  Computer,
  Group,
  TrendingUp
} from '@mui/icons-material';
import { useToast } from '../components/Toast';

interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'operador' | 'visualizador' | 'auditor';
  status: 'active' | 'inactive';
  lastLogin: string;
  createdAt: string;
  recordingsCount: number;
}

interface SystemMetrics {
  totalUsers: number;
  activeUsers: number;
  totalRecordings: number;
  storageUsed: number;
  storageLimit: number;
  systemHealth: 'healthy' | 'warning' | 'error';
  uptime: string;
}

interface AuditLog {
  id: string;
  user: string;
  action: string;
  resource: string;
  timestamp: string;
  ipAddress: string;
  status: 'success' | 'warning' | 'error';
}

const AdminPage: React.FC = () => {
  const [currentTab, setCurrentTab] = useState(0);
  const [users, setUsers] = useState<User[]>([]);
  const [metrics, setMetrics] = useState<SystemMetrics | null>(null);
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    role: 'visualizador' as User['role'],
    status: 'active' as User['status']
  });

  // Novos estados para funcionalidades expandidas
  const [systemSettings, setSystemSettings] = useState({
    maintenanceMode: false,
    autoBackup: true,
    backupFrequency: 'daily',
    maxFileSize: 100,
    retentionDays: 730,
    enableNotifications: true,
    enableAuditLog: true,
    enableEncryption: true,
    theme: 'light',
    language: 'pt-BR',
    timezone: 'America/Sao_Paulo'
  });

  const [backupStatus, setBackupStatus] = useState({
    lastBackup: null as string | null,
    nextBackup: null as string | null,
    backupSize: 0,
    status: 'idle' as 'idle' | 'running' | 'completed' | 'error'
  });

  const [securitySettings, setSecuritySettings] = useState({
    passwordPolicy: {
      minLength: 8,
      requireUppercase: true,
      requireNumbers: true,
      requireSymbols: true,
      maxAge: 90
    },
    sessionTimeout: 30,
    maxLoginAttempts: 5,
    enableTwoFactor: false,
    enableIpWhitelist: false,
    allowedIps: [] as string[]
  });

  const [notifications, setNotifications] = useState({
    email: {
      enabled: true,
      smtp: {
        host: '',
        port: 587,
        username: '',
        password: '',
        secure: true
      }
    },
    sms: {
      enabled: false,
      provider: 'twilio',
      apiKey: '',
      from: ''
    },
    webhook: {
      enabled: false,
      url: '',
      secret: ''
    }
  });

  const [systemHealth, setSystemHealth] = useState({
    cpu: 0,
    memory: 0,
    disk: 0,
    network: 'good' as 'good' | 'slow' | 'error',
    database: 'connected' as 'connected' | 'slow' | 'disconnected',
    services: [] as Array<{name: string, status: 'running' | 'stopped' | 'error'}>
  });
  const { showSuccess, showError } = useToast();

  // Mock data - Em produção, buscar da API
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      
      // Simular delay de API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock users
      setUsers([
        {
          id: '1',
          name: 'João Silva',
          email: '<EMAIL>',
          role: 'admin',
          status: 'active',
          lastLogin: '2024-01-17 10:30',
          createdAt: '2024-01-01',
          recordingsCount: 45
        },
        {
          id: '2',
          name: 'Maria Santos',
          email: '<EMAIL>',
          role: 'operador',
          status: 'active',
          lastLogin: '2024-01-17 09:15',
          createdAt: '2024-01-05',
          recordingsCount: 32
        },
        {
          id: '3',
          name: 'Pedro Costa',
          email: '<EMAIL>',
          role: 'visualizador',
          status: 'inactive',
          lastLogin: '2024-01-15 16:20',
          createdAt: '2024-01-10',
          recordingsCount: 8
        }
      ]);

      // Mock metrics
      setMetrics({
        totalUsers: 15,
        activeUsers: 12,
        totalRecordings: 1247,
        storageUsed: 45.6,
        storageLimit: 100,
        systemHealth: 'healthy',
        uptime: '15 dias, 6 horas'
      });

      // Mock audit logs
      setAuditLogs([
        {
          id: '1',
          user: 'João Silva',
          action: 'Login',
          resource: 'Sistema',
          timestamp: '2024-01-17 10:30:15',
          ipAddress: '*************',
          status: 'success'
        },
        {
          id: '2',
          user: 'Maria Santos',
          action: 'Criação de Gravação',
          resource: 'Gravação #1248',
          timestamp: '2024-01-17 10:25:32',
          ipAddress: '*************',
          status: 'success'
        },
        {
          id: '3',
          user: 'Sistema',
          action: 'Backup Automático',
          resource: 'Base de Dados',
          timestamp: '2024-01-17 02:00:00',
          ipAddress: 'localhost',
          status: 'warning'
        }
      ]);

      setLoading(false);
    };

    loadData();
  }, []);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };

  const handleOpenDialog = (user?: User) => {
    if (user) {
      setSelectedUser(user);
      setFormData({
        name: user.name,
        email: user.email,
        role: user.role,
        status: user.status
      });
    } else {
      setSelectedUser(null);
      setFormData({
        name: '',
        email: '',
        role: 'visualizador',
        status: 'active'
      });
    }
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setSelectedUser(null);
  };

  const handleSaveUser = async () => {
    try {
      if (selectedUser) {
        // Atualizar usuário
        setUsers(prev => prev.map(user => 
          user.id === selectedUser.id 
            ? { ...user, ...formData }
            : user
        ));
        showSuccess('Usuário atualizado com sucesso!');
      } else {
        // Criar novo usuário
        const newUser: User = {
          id: Date.now().toString(),
          ...formData,
          lastLogin: 'Nunca',
          createdAt: new Date().toISOString().split('T')[0],
          recordingsCount: 0
        };
        setUsers(prev => [...prev, newUser]);
        showSuccess('Usuário criado com sucesso!');
      }
      handleCloseDialog();
    } catch (error) {
      showError('Erro ao salvar usuário');
    }
  };

  const handleDeleteUser = async (userId: string) => {
    if (window.confirm('Tem certeza que deseja excluir este usuário?')) {
      try {
        setUsers(prev => prev.filter(user => user.id !== userId));
        showSuccess('Usuário excluído com sucesso!');
      } catch (error) {
        showError('Erro ao excluir usuário');
      }
    }
  };

  const getRoleColor = (role: User['role']) => {
    switch (role) {
      case 'admin': return 'error';
      case 'operador': return 'primary';
      case 'auditor': return 'warning';
      case 'visualizador': return 'default';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return <CheckCircle color="success" />;
      case 'warning': return <Warning color="warning" />;
      case 'error': return <Error color="error" />;
      default: return <CheckCircle color="info" />;
    }
  };

  if (loading) {
    return (
      <Container maxWidth="xl">
        <Box sx={{ py: 4 }}>
          <LinearProgress />
          <Typography variant="h6" sx={{ mt: 2, textAlign: 'center' }}>
            Carregando painel administrativo...
          </Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl">
      <Box sx={{ py: 4 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
          <AdminPanelSettings sx={{ fontSize: 40, mr: 2, color: 'primary.main' }} />
          <Box>
            <Typography variant="h4" component="h1">
              Painel Administrativo
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Gerencie usuários, monitore o sistema e visualize relatórios
            </Typography>
          </Box>
        </Box>

        {/* System Health Alert */}
        {metrics?.systemHealth !== 'healthy' && (
          <Alert 
            severity={metrics?.systemHealth === 'warning' ? 'warning' : 'error'} 
            sx={{ mb: 3 }}
          >
            Sistema com {metrics?.systemHealth === 'warning' ? 'avisos' : 'problemas'}. 
            Verifique os logs para mais detalhes.
          </Alert>
        )}

        {/* Metrics Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Group sx={{ fontSize: 40, color: 'primary.main', mr: 2 }} />
                  <Box>
                    <Typography variant="h4">
                      {metrics?.totalUsers}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total de Usuários
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <CheckCircle sx={{ fontSize: 40, color: 'success.main', mr: 2 }} />
                  <Box>
                    <Typography variant="h4">
                      {metrics?.activeUsers}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Usuários Ativos
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <TrendingUp sx={{ fontSize: 40, color: 'info.main', mr: 2 }} />
                  <Box>
                    <Typography variant="h4">
                      {metrics?.totalRecordings}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total de Gravações
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Storage sx={{ fontSize: 40, color: 'warning.main', mr: 2 }} />
                  <Box>
                    <Typography variant="h4">
                      {metrics?.storageUsed}GB
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Armazenamento Usado
                    </Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={(metrics?.storageUsed || 0) / (metrics?.storageLimit || 1) * 100}
                      sx={{ mt: 1 }}
                    />
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Tabs */}
        <Paper sx={{ mb: 3 }}>
          <Tabs
            value={currentTab}
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
          >
            <Tab icon={<People />} label="Usuários" />
            <Tab icon={<Security />} label="Logs de Auditoria" />
            <Tab icon={<Computer />} label="Sistema" />
            <Tab icon={<Analytics />} label="Relatórios" />
            <Tab icon={<Settings />} label="Configurações" />
            <Tab icon={<Backup />} label="Backup & Restore" />
            <Tab icon={<Shield />} label="Segurança" />
            <Tab icon={<Notifications />} label="Notificações" />
            <Tab icon={<Assessment />} label="Monitoramento" />
          </Tabs>
        </Paper>

        {/* Tab Content */}
        {currentTab === 0 && (
          <Paper>
            <Box sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h6">
                  Gerenciamento de Usuários
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<Add />}
                  onClick={() => handleOpenDialog()}
                >
                  Novo Usuário
                </Button>
              </Box>

              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Nome</TableCell>
                      <TableCell>Email</TableCell>
                      <TableCell>Perfil</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Último Login</TableCell>
                      <TableCell>Gravações</TableCell>
                      <TableCell>Ações</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {users.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
                              {user.name.charAt(0)}
                            </Avatar>
                            {user.name}
                          </Box>
                        </TableCell>
                        <TableCell>{user.email}</TableCell>
                        <TableCell>
                          <Chip 
                            label={user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                            color={getRoleColor(user.role)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Chip 
                            label={user.status === 'active' ? 'Ativo' : 'Inativo'}
                            color={user.status === 'active' ? 'success' : 'default'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>{user.lastLogin}</TableCell>
                        <TableCell>
                          <Badge badgeContent={user.recordingsCount} color="primary">
                            <Computer />
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <IconButton onClick={() => handleOpenDialog(user)}>
                            <Edit />
                          </IconButton>
                          <IconButton 
                            onClick={() => handleDeleteUser(user.id)}
                            color="error"
                          >
                            <Delete />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          </Paper>
        )}

        {currentTab === 1 && (
          <Paper>
            <Box sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ mb: 3 }}>
                Logs de Auditoria
              </Typography>
              
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Status</TableCell>
                      <TableCell>Usuário</TableCell>
                      <TableCell>Ação</TableCell>
                      <TableCell>Recurso</TableCell>
                      <TableCell>Data/Hora</TableCell>
                      <TableCell>IP</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {auditLogs.map((log) => (
                      <TableRow key={log.id}>
                        <TableCell>
                          {getStatusIcon(log.status)}
                        </TableCell>
                        <TableCell>{log.user}</TableCell>
                        <TableCell>{log.action}</TableCell>
                        <TableCell>{log.resource}</TableCell>
                        <TableCell>{log.timestamp}</TableCell>
                        <TableCell>{log.ipAddress}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          </Paper>
        )}

        {currentTab === 2 && (
          <Paper>
            <Box sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ mb: 3 }}>
                Configurações do Sistema
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardHeader title="Status do Sistema" />
                    <CardContent>
                      <List>
                        <ListItem>
                          <ListItemText primary="Tempo de Atividade" secondary={metrics?.uptime} />
                          <CheckCircle color="success" />
                        </ListItem>
                        <ListItem>
                          <ListItemText primary="Backup Automático" secondary="Habilitado" />
                          <Switch checked />
                        </ListItem>
                        <ListItem>
                          <ListItemText primary="Monitoramento" secondary="Ativo" />
                          <CheckCircle color="success" />
                        </ListItem>
                      </List>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Card>
                    <CardHeader title="Configurações de Segurança" />
                    <CardContent>
                      <List>
                        <ListItem>
                          <ListItemText primary="SSL/TLS" secondary="Certificado válido" />
                          <CheckCircle color="success" />
                        </ListItem>
                        <ListItem>
                          <ListItemText primary="Firewall" secondary="Ativo" />
                          <CheckCircle color="success" />
                        </ListItem>
                        <ListItem>
                          <ListItemText primary="Logs de Auditoria" secondary="Habilitado" />
                          <CheckCircle color="success" />
                        </ListItem>
                      </List>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          </Paper>
        )}

        {currentTab === 3 && (
          <Paper>
            <Box sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ mb: 3 }}>
                Relatórios e Analytics
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardHeader title="Uso por Usuário" />
                    <CardContent>
                      <Typography variant="body2" color="text.secondary">
                        Relatório de utilização do sistema por usuário nos últimos 30 dias.
                      </Typography>
                      <Button variant="outlined" sx={{ mt: 2 }}>
                        Gerar Relatório
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Card>
                    <CardHeader title="Armazenamento" />
                    <CardContent>
                      <Typography variant="body2" color="text.secondary">
                        Análise de uso de armazenamento e projeções de crescimento.
                      </Typography>
                      <Button variant="outlined" sx={{ mt: 2 }}>
                        Ver Detalhes
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          </Paper>
        )}

        {/* Aba 4: Configurações Gerais */}
        {currentTab === 4 && (
          <Paper>
            <Box sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ mb: 3 }}>
                Configurações do Sistema
              </Typography>

              <Grid container spacing={3}>
                {/* Configurações Gerais */}
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardHeader
                      title="Configurações Gerais"
                      avatar={<Settings color="primary" />}
                    />
                    <CardContent>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={systemSettings.maintenanceMode}
                            onChange={(e) => setSystemSettings(prev => ({
                              ...prev,
                              maintenanceMode: e.target.checked
                            }))}
                          />
                        }
                        label="Modo de Manutenção"
                      />

                      <FormControlLabel
                        control={
                          <Switch
                            checked={systemSettings.autoBackup}
                            onChange={(e) => setSystemSettings(prev => ({
                              ...prev,
                              autoBackup: e.target.checked
                            }))}
                          />
                        }
                        label="Backup Automático"
                      />

                      <FormControl fullWidth sx={{ mt: 2 }}>
                        <InputLabel>Frequência de Backup</InputLabel>
                        <Select
                          value={systemSettings.backupFrequency}
                          onChange={(e) => setSystemSettings(prev => ({
                            ...prev,
                            backupFrequency: e.target.value
                          }))}
                          label="Frequência de Backup"
                        >
                          <MenuItem value="hourly">A cada hora</MenuItem>
                          <MenuItem value="daily">Diário</MenuItem>
                          <MenuItem value="weekly">Semanal</MenuItem>
                          <MenuItem value="monthly">Mensal</MenuItem>
                        </Select>
                      </FormControl>

                      <TextField
                        fullWidth
                        label="Tamanho Máximo de Arquivo (MB)"
                        type="number"
                        value={systemSettings.maxFileSize}
                        onChange={(e) => setSystemSettings(prev => ({
                          ...prev,
                          maxFileSize: parseInt(e.target.value)
                        }))}
                        sx={{ mt: 2 }}
                      />

                      <TextField
                        fullWidth
                        label="Dias de Retenção"
                        type="number"
                        value={systemSettings.retentionDays}
                        onChange={(e) => setSystemSettings(prev => ({
                          ...prev,
                          retentionDays: parseInt(e.target.value)
                        }))}
                        sx={{ mt: 2 }}
                      />
                    </CardContent>
                  </Card>
                </Grid>

                {/* Configurações de Interface */}
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardHeader
                      title="Interface e Localização"
                      avatar={<Palette color="primary" />}
                    />
                    <CardContent>
                      <FormControl fullWidth sx={{ mb: 2 }}>
                        <InputLabel>Tema</InputLabel>
                        <Select
                          value={systemSettings.theme}
                          onChange={(e) => setSystemSettings(prev => ({
                            ...prev,
                            theme: e.target.value
                          }))}
                          label="Tema"
                        >
                          <MenuItem value="light">
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <LightMode /> Claro
                            </Box>
                          </MenuItem>
                          <MenuItem value="dark">
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <DarkMode /> Escuro
                            </Box>
                          </MenuItem>
                          <MenuItem value="auto">
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Settings /> Automático
                            </Box>
                          </MenuItem>
                        </Select>
                      </FormControl>

                      <FormControl fullWidth sx={{ mb: 2 }}>
                        <InputLabel>Idioma</InputLabel>
                        <Select
                          value={systemSettings.language}
                          onChange={(e) => setSystemSettings(prev => ({
                            ...prev,
                            language: e.target.value
                          }))}
                          label="Idioma"
                        >
                          <MenuItem value="pt-BR">Português (Brasil)</MenuItem>
                          <MenuItem value="en-US">English (US)</MenuItem>
                          <MenuItem value="es-ES">Español</MenuItem>
                        </Select>
                      </FormControl>

                      <FormControl fullWidth>
                        <InputLabel>Fuso Horário</InputLabel>
                        <Select
                          value={systemSettings.timezone}
                          onChange={(e) => setSystemSettings(prev => ({
                            ...prev,
                            timezone: e.target.value
                          }))}
                          label="Fuso Horário"
                        >
                          <MenuItem value="America/Sao_Paulo">São Paulo (GMT-3)</MenuItem>
                          <MenuItem value="America/New_York">New York (GMT-5)</MenuItem>
                          <MenuItem value="Europe/London">London (GMT+0)</MenuItem>
                        </Select>
                      </FormControl>

                      <Box sx={{ mt: 3 }}>
                        <Button variant="contained" startIcon={<Update />}>
                          Salvar Configurações
                        </Button>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          </Paper>
        )}

        {/* Aba 5: Backup & Restore */}
        {currentTab === 5 && (
          <Paper>
            <Box sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ mb: 3 }}>
                Backup e Restauração
              </Typography>

              <Grid container spacing={3}>
                {/* Status do Backup */}
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardHeader
                      title="Status do Backup"
                      avatar={<Backup color="primary" />}
                    />
                    <CardContent>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">
                          Último Backup
                        </Typography>
                        <Typography variant="h6">
                          {backupStatus.lastBackup || 'Nunca executado'}
                        </Typography>
                      </Box>

                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">
                          Próximo Backup
                        </Typography>
                        <Typography variant="h6">
                          {backupStatus.nextBackup || 'Não agendado'}
                        </Typography>
                      </Box>

                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary">
                          Tamanho do Backup
                        </Typography>
                        <Typography variant="h6">
                          {(backupStatus.backupSize / 1024 / 1024).toFixed(2)} MB
                        </Typography>
                      </Box>

                      <Chip
                        label={backupStatus.status.toUpperCase()}
                        color={
                          backupStatus.status === 'completed' ? 'success' :
                          backupStatus.status === 'running' ? 'warning' :
                          backupStatus.status === 'error' ? 'error' : 'default'
                        }
                      />
                    </CardContent>
                  </Card>
                </Grid>

                {/* Ações de Backup */}
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardHeader
                      title="Ações de Backup"
                      avatar={<CloudUpload color="primary" />}
                    />
                    <CardContent>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                        <Button
                          variant="contained"
                          startIcon={<Backup />}
                          fullWidth
                          disabled={backupStatus.status === 'running'}
                        >
                          {backupStatus.status === 'running' ? 'Executando...' : 'Executar Backup Agora'}
                        </Button>

                        <Button
                          variant="outlined"
                          startIcon={<CloudDownload />}
                          fullWidth
                        >
                          Download Último Backup
                        </Button>

                        <Button
                          variant="outlined"
                          startIcon={<Schedule />}
                          fullWidth
                        >
                          Configurar Agendamento
                        </Button>

                        <Divider sx={{ my: 1 }} />

                        <Typography variant="subtitle2" color="text.secondary">
                          Restauração
                        </Typography>

                        <Button
                          variant="outlined"
                          startIcon={<Restore />}
                          fullWidth
                          color="warning"
                        >
                          Restaurar do Backup
                        </Button>

                        <Alert severity="warning" sx={{ mt: 1 }}>
                          A restauração substituirá todos os dados atuais.
                        </Alert>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>

                {/* Histórico de Backups */}
                <Grid item xs={12}>
                  <Card>
                    <CardHeader title="Histórico de Backups" />
                    <CardContent>
                      <TableContainer>
                        <Table>
                          <TableHead>
                            <TableRow>
                              <TableCell>Data/Hora</TableCell>
                              <TableCell>Tipo</TableCell>
                              <TableCell>Tamanho</TableCell>
                              <TableCell>Status</TableCell>
                              <TableCell>Ações</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            <TableRow>
                              <TableCell>18/01/2025 02:00</TableCell>
                              <TableCell>Automático</TableCell>
                              <TableCell>245.8 MB</TableCell>
                              <TableCell>
                                <Chip label="Concluído" color="success" size="small" />
                              </TableCell>
                              <TableCell>
                                <IconButton size="small">
                                  <CloudDownload />
                                </IconButton>
                                <IconButton size="small" color="error">
                                  <DeleteForever />
                                </IconButton>
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell>17/01/2025 02:00</TableCell>
                              <TableCell>Automático</TableCell>
                              <TableCell>238.2 MB</TableCell>
                              <TableCell>
                                <Chip label="Concluído" color="success" size="small" />
                              </TableCell>
                              <TableCell>
                                <IconButton size="small">
                                  <CloudDownload />
                                </IconButton>
                                <IconButton size="small" color="error">
                                  <DeleteForever />
                                </IconButton>
                              </TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          </Paper>
        )}

        {/* Aba 6: Segurança */}
        {currentTab === 6 && (
          <Paper>
            <Box sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ mb: 3 }}>
                Configurações de Segurança
              </Typography>

              <Grid container spacing={3}>
                {/* Política de Senhas */}
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardHeader
                      title="Política de Senhas"
                      avatar={<VpnKey color="primary" />}
                    />
                    <CardContent>
                      <TextField
                        fullWidth
                        label="Comprimento Mínimo"
                        type="number"
                        value={securitySettings.passwordPolicy.minLength}
                        onChange={(e) => setSecuritySettings(prev => ({
                          ...prev,
                          passwordPolicy: {
                            ...prev.passwordPolicy,
                            minLength: parseInt(e.target.value)
                          }
                        }))}
                        sx={{ mb: 2 }}
                      />

                      <FormControlLabel
                        control={
                          <Switch
                            checked={securitySettings.passwordPolicy.requireUppercase}
                            onChange={(e) => setSecuritySettings(prev => ({
                              ...prev,
                              passwordPolicy: {
                                ...prev.passwordPolicy,
                                requireUppercase: e.target.checked
                              }
                            }))}
                          />
                        }
                        label="Exigir Maiúsculas"
                      />

                      <FormControlLabel
                        control={
                          <Switch
                            checked={securitySettings.passwordPolicy.requireNumbers}
                            onChange={(e) => setSecuritySettings(prev => ({
                              ...prev,
                              passwordPolicy: {
                                ...prev.passwordPolicy,
                                requireNumbers: e.target.checked
                              }
                            }))}
                          />
                        }
                        label="Exigir Números"
                      />

                      <FormControlLabel
                        control={
                          <Switch
                            checked={securitySettings.passwordPolicy.requireSymbols}
                            onChange={(e) => setSecuritySettings(prev => ({
                              ...prev,
                              passwordPolicy: {
                                ...prev.passwordPolicy,
                                requireSymbols: e.target.checked
                              }
                            }))}
                          />
                        }
                        label="Exigir Símbolos"
                      />

                      <TextField
                        fullWidth
                        label="Validade da Senha (dias)"
                        type="number"
                        value={securitySettings.passwordPolicy.maxAge}
                        onChange={(e) => setSecuritySettings(prev => ({
                          ...prev,
                          passwordPolicy: {
                            ...prev.passwordPolicy,
                            maxAge: parseInt(e.target.value)
                          }
                        }))}
                        sx={{ mt: 2 }}
                      />
                    </CardContent>
                  </Card>
                </Grid>

                {/* Configurações de Sessão */}
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardHeader
                      title="Sessões e Acesso"
                      avatar={<Shield color="primary" />}
                    />
                    <CardContent>
                      <TextField
                        fullWidth
                        label="Timeout de Sessão (minutos)"
                        type="number"
                        value={securitySettings.sessionTimeout}
                        onChange={(e) => setSecuritySettings(prev => ({
                          ...prev,
                          sessionTimeout: parseInt(e.target.value)
                        }))}
                        sx={{ mb: 2 }}
                      />

                      <TextField
                        fullWidth
                        label="Máximo de Tentativas de Login"
                        type="number"
                        value={securitySettings.maxLoginAttempts}
                        onChange={(e) => setSecuritySettings(prev => ({
                          ...prev,
                          maxLoginAttempts: parseInt(e.target.value)
                        }))}
                        sx={{ mb: 2 }}
                      />

                      <FormControlLabel
                        control={
                          <Switch
                            checked={securitySettings.enableTwoFactor}
                            onChange={(e) => setSecuritySettings(prev => ({
                              ...prev,
                              enableTwoFactor: e.target.checked
                            }))}
                          />
                        }
                        label="Autenticação de Dois Fatores"
                      />

                      <FormControlLabel
                        control={
                          <Switch
                            checked={securitySettings.enableIpWhitelist}
                            onChange={(e) => setSecuritySettings(prev => ({
                              ...prev,
                              enableIpWhitelist: e.target.checked
                            }))}
                          />
                        }
                        label="Lista Branca de IPs"
                      />

                      {securitySettings.enableIpWhitelist && (
                        <TextField
                          fullWidth
                          label="IPs Permitidos (separados por vírgula)"
                          multiline
                          rows={3}
                          value={securitySettings.allowedIps.join(', ')}
                          onChange={(e) => setSecuritySettings(prev => ({
                            ...prev,
                            allowedIps: e.target.value.split(',').map(ip => ip.trim())
                          }))}
                          sx={{ mt: 2 }}
                        />
                      )}
                    </CardContent>
                  </Card>
                </Grid>

                {/* Logs de Segurança */}
                <Grid item xs={12}>
                  <Card>
                    <CardHeader title="Eventos de Segurança Recentes" />
                    <CardContent>
                      <TableContainer>
                        <Table>
                          <TableHead>
                            <TableRow>
                              <TableCell>Data/Hora</TableCell>
                              <TableCell>Evento</TableCell>
                              <TableCell>Usuário</TableCell>
                              <TableCell>IP</TableCell>
                              <TableCell>Status</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            <TableRow>
                              <TableCell>18/01/2025 10:30</TableCell>
                              <TableCell>Login Bem-sucedido</TableCell>
                              <TableCell><EMAIL></TableCell>
                              <TableCell>*************</TableCell>
                              <TableCell>
                                <Chip label="Sucesso" color="success" size="small" />
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell>18/01/2025 09:45</TableCell>
                              <TableCell>Tentativa de Login Falhada</TableCell>
                              <TableCell><EMAIL></TableCell>
                              <TableCell>************</TableCell>
                              <TableCell>
                                <Chip label="Bloqueado" color="error" size="small" />
                              </TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          </Paper>
        )}

        {/* Aba 7: Notificações */}
        {currentTab === 7 && (
          <Paper>
            <Box sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ mb: 3 }}>
                Configurações de Notificações
              </Typography>

              <Grid container spacing={3}>
                {/* Email */}
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardHeader
                      title="Notificações por Email"
                      avatar={<Email color="primary" />}
                    />
                    <CardContent>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={notifications.email.enabled}
                            onChange={(e) => setNotifications(prev => ({
                              ...prev,
                              email: { ...prev.email, enabled: e.target.checked }
                            }))}
                          />
                        }
                        label="Habilitar Email"
                      />

                      {notifications.email.enabled && (
                        <Box sx={{ mt: 2 }}>
                          <TextField
                            fullWidth
                            label="Servidor SMTP"
                            value={notifications.email.smtp.host}
                            onChange={(e) => setNotifications(prev => ({
                              ...prev,
                              email: {
                                ...prev.email,
                                smtp: { ...prev.email.smtp, host: e.target.value }
                              }
                            }))}
                            sx={{ mb: 2 }}
                          />

                          <TextField
                            fullWidth
                            label="Porta"
                            type="number"
                            value={notifications.email.smtp.port}
                            onChange={(e) => setNotifications(prev => ({
                              ...prev,
                              email: {
                                ...prev.email,
                                smtp: { ...prev.email.smtp, port: parseInt(e.target.value) }
                              }
                            }))}
                            sx={{ mb: 2 }}
                          />

                          <TextField
                            fullWidth
                            label="Usuário"
                            value={notifications.email.smtp.username}
                            onChange={(e) => setNotifications(prev => ({
                              ...prev,
                              email: {
                                ...prev.email,
                                smtp: { ...prev.email.smtp, username: e.target.value }
                              }
                            }))}
                            sx={{ mb: 2 }}
                          />

                          <TextField
                            fullWidth
                            label="Senha"
                            type="password"
                            value={notifications.email.smtp.password}
                            onChange={(e) => setNotifications(prev => ({
                              ...prev,
                              email: {
                                ...prev.email,
                                smtp: { ...prev.email.smtp, password: e.target.value }
                              }
                            }))}
                            sx={{ mb: 2 }}
                          />

                          <FormControlLabel
                            control={
                              <Switch
                                checked={notifications.email.smtp.secure}
                                onChange={(e) => setNotifications(prev => ({
                                  ...prev,
                                  email: {
                                    ...prev.email,
                                    smtp: { ...prev.email.smtp, secure: e.target.checked }
                                  }
                                }))}
                              />
                            }
                            label="Conexão Segura (TLS)"
                          />

                          <Button variant="outlined" sx={{ mt: 2 }}>
                            Testar Configuração
                          </Button>
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </Grid>

                {/* SMS */}
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardHeader
                      title="Notificações por SMS"
                      avatar={<Sms color="primary" />}
                    />
                    <CardContent>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={notifications.sms.enabled}
                            onChange={(e) => setNotifications(prev => ({
                              ...prev,
                              sms: { ...prev.sms, enabled: e.target.checked }
                            }))}
                          />
                        }
                        label="Habilitar SMS"
                      />

                      {notifications.sms.enabled && (
                        <Box sx={{ mt: 2 }}>
                          <FormControl fullWidth sx={{ mb: 2 }}>
                            <InputLabel>Provedor</InputLabel>
                            <Select
                              value={notifications.sms.provider}
                              onChange={(e) => setNotifications(prev => ({
                                ...prev,
                                sms: { ...prev.sms, provider: e.target.value }
                              }))}
                              label="Provedor"
                            >
                              <MenuItem value="twilio">Twilio</MenuItem>
                              <MenuItem value="aws-sns">AWS SNS</MenuItem>
                              <MenuItem value="zenvia">Zenvia</MenuItem>
                            </Select>
                          </FormControl>

                          <TextField
                            fullWidth
                            label="API Key"
                            type="password"
                            value={notifications.sms.apiKey}
                            onChange={(e) => setNotifications(prev => ({
                              ...prev,
                              sms: { ...prev.sms, apiKey: e.target.value }
                            }))}
                            sx={{ mb: 2 }}
                          />

                          <TextField
                            fullWidth
                            label="Número de Origem"
                            value={notifications.sms.from}
                            onChange={(e) => setNotifications(prev => ({
                              ...prev,
                              sms: { ...prev.sms, from: e.target.value }
                            }))}
                            sx={{ mb: 2 }}
                          />

                          <Button variant="outlined">
                            Testar SMS
                          </Button>
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </Grid>

                {/* Webhook */}
                <Grid item xs={12}>
                  <Card>
                    <CardHeader
                      title="Webhook"
                      avatar={<NetworkCheck color="primary" />}
                    />
                    <CardContent>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={notifications.webhook.enabled}
                            onChange={(e) => setNotifications(prev => ({
                              ...prev,
                              webhook: { ...prev.webhook, enabled: e.target.checked }
                            }))}
                          />
                        }
                        label="Habilitar Webhook"
                      />

                      {notifications.webhook.enabled && (
                        <Box sx={{ mt: 2, display: 'flex', gap: 2 }}>
                          <TextField
                            fullWidth
                            label="URL do Webhook"
                            value={notifications.webhook.url}
                            onChange={(e) => setNotifications(prev => ({
                              ...prev,
                              webhook: { ...prev.webhook, url: e.target.value }
                            }))}
                          />

                          <TextField
                            fullWidth
                            label="Secret"
                            type="password"
                            value={notifications.webhook.secret}
                            onChange={(e) => setNotifications(prev => ({
                              ...prev,
                              webhook: { ...prev.webhook, secret: e.target.value }
                            }))}
                          />

                          <Button variant="outlined">
                            Testar
                          </Button>
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          </Paper>
        )}

        {/* Aba 8: Monitoramento */}
        {currentTab === 8 && (
          <Paper>
            <Box sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ mb: 3 }}>
                Monitoramento do Sistema
              </Typography>

              <Grid container spacing={3}>
                {/* Recursos do Sistema */}
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardHeader
                      title="Recursos do Sistema"
                      avatar={<Speed color="primary" />}
                    />
                    <CardContent>
                      <Box sx={{ mb: 3 }}>
                        <Typography variant="body2" color="text.secondary">
                          CPU
                        </Typography>
                        <LinearProgress
                          variant="determinate"
                          value={systemHealth.cpu}
                          sx={{ mb: 1 }}
                        />
                        <Typography variant="body2">
                          {systemHealth.cpu}%
                        </Typography>
                      </Box>

                      <Box sx={{ mb: 3 }}>
                        <Typography variant="body2" color="text.secondary">
                          Memória
                        </Typography>
                        <LinearProgress
                          variant="determinate"
                          value={systemHealth.memory}
                          sx={{ mb: 1 }}
                          color={systemHealth.memory > 80 ? 'error' : 'primary'}
                        />
                        <Typography variant="body2">
                          {systemHealth.memory}%
                        </Typography>
                      </Box>

                      <Box sx={{ mb: 3 }}>
                        <Typography variant="body2" color="text.secondary">
                          Disco
                        </Typography>
                        <LinearProgress
                          variant="determinate"
                          value={systemHealth.disk}
                          sx={{ mb: 1 }}
                          color={systemHealth.disk > 90 ? 'error' : 'primary'}
                        />
                        <Typography variant="body2">
                          {systemHealth.disk}%
                        </Typography>
                      </Box>

                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="body2" color="text.secondary">
                          Rede
                        </Typography>
                        <Chip
                          label={systemHealth.network.toUpperCase()}
                          color={
                            systemHealth.network === 'good' ? 'success' :
                            systemHealth.network === 'slow' ? 'warning' : 'error'
                          }
                          size="small"
                        />
                      </Box>

                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 1 }}>
                        <Typography variant="body2" color="text.secondary">
                          Banco de Dados
                        </Typography>
                        <Chip
                          label={systemHealth.database.toUpperCase()}
                          color={
                            systemHealth.database === 'connected' ? 'success' :
                            systemHealth.database === 'slow' ? 'warning' : 'error'
                          }
                          size="small"
                        />
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>

                {/* Serviços */}
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardHeader
                      title="Status dos Serviços"
                      avatar={<SystemUpdate color="primary" />}
                    />
                    <CardContent>
                      <List>
                        <ListItem>
                          <ListItemIcon>
                            <CheckCircle color="success" />
                          </ListItemIcon>
                          <ListItemText
                            primary="API Principal"
                            secondary="Funcionando normalmente"
                          />
                        </ListItem>

                        <ListItem>
                          <ListItemIcon>
                            <CheckCircle color="success" />
                          </ListItemIcon>
                          <ListItemText
                            primary="Banco de Dados"
                            secondary="Conectado"
                          />
                        </ListItem>

                        <ListItem>
                          <ListItemIcon>
                            <CheckCircle color="success" />
                          </ListItemIcon>
                          <ListItemText
                            primary="Sistema de Arquivos"
                            secondary="Operacional"
                          />
                        </ListItem>

                        <ListItem>
                          <ListItemIcon>
                            <Error color="warning" />
                          </ListItemIcon>
                          <ListItemText
                            primary="Serviço de Email"
                            secondary="Configuração pendente"
                          />
                        </ListItem>

                        <ListItem>
                          <ListItemIcon>
                            <CheckCircle color="success" />
                          </ListItemIcon>
                          <ListItemText
                            primary="Backup Automático"
                            secondary="Agendado para 02:00"
                          />
                        </ListItem>
                      </List>
                    </CardContent>
                  </Card>
                </Grid>

                {/* Logs do Sistema */}
                <Grid item xs={12}>
                  <Card>
                    <CardHeader
                      title="Logs do Sistema"
                      avatar={<BugReport color="primary" />}
                      action={
                        <Button variant="outlined" size="small">
                          Ver Todos os Logs
                        </Button>
                      }
                    />
                    <CardContent>
                      <TableContainer>
                        <Table>
                          <TableHead>
                            <TableRow>
                              <TableCell>Timestamp</TableCell>
                              <TableCell>Nível</TableCell>
                              <TableCell>Serviço</TableCell>
                              <TableCell>Mensagem</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            <TableRow>
                              <TableCell>18/01/2025 10:35:22</TableCell>
                              <TableCell>
                                <Chip label="INFO" color="info" size="small" />
                              </TableCell>
                              <TableCell>API</TableCell>
                              <TableCell>Usuário <EMAIL> fez login</TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell>18/01/2025 10:30:15</TableCell>
                              <TableCell>
                                <Chip label="SUCCESS" color="success" size="small" />
                              </TableCell>
                              <TableCell>BACKUP</TableCell>
                              <TableCell>Backup automático concluído com sucesso</TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell>18/01/2025 09:45:33</TableCell>
                              <TableCell>
                                <Chip label="WARNING" color="warning" size="small" />
                              </TableCell>
                              <TableCell>AUTH</TableCell>
                              <TableCell>Tentativa de login <NAME_EMAIL></TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell>18/01/2025 02:00:01</TableCell>
                              <TableCell>
                                <Chip label="INFO" color="info" size="small" />
                              </TableCell>
                              <TableCell>SYSTEM</TableCell>
                              <TableCell>Backup automático iniciado</TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </CardContent>
                  </Card>
                </Grid>

                {/* Alertas e Notificações */}
                <Grid item xs={12}>
                  <Card>
                    <CardHeader
                      title="Alertas Ativos"
                      avatar={<Notifications color="primary" />}
                    />
                    <CardContent>
                      <Alert severity="warning" sx={{ mb: 2 }}>
                        <AlertTitle>Uso de Disco Alto</AlertTitle>
                        O disco está com 85% de uso. Considere fazer limpeza ou expandir o armazenamento.
                      </Alert>

                      <Alert severity="info" sx={{ mb: 2 }}>
                        <AlertTitle>Atualização Disponível</AlertTitle>
                        Nova versão do sistema disponível. Clique aqui para atualizar.
                      </Alert>

                      <Alert severity="success">
                        <AlertTitle>Sistema Estável</AlertTitle>
                        Todos os serviços principais estão funcionando normalmente.
                      </Alert>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          </Paper>
        )}

        {/* User Dialog */}
        <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
          <DialogTitle>
            {selectedUser ? 'Editar Usuário' : 'Novo Usuário'}
          </DialogTitle>
          <DialogContent>
            <TextField
              autoFocus
              margin="dense"
              label="Nome Completo"
              fullWidth
              variant="outlined"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              sx={{ mb: 2 }}
            />
            <TextField
              margin="dense"
              label="Email"
              type="email"
              fullWidth
              variant="outlined"
              value={formData.email}
              onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
              sx={{ mb: 2 }}
            />
            <FormControl fullWidth variant="outlined" sx={{ mb: 2 }}>
              <InputLabel>Perfil</InputLabel>
              <Select
                value={formData.role}
                onChange={(e) => setFormData(prev => ({ ...prev, role: e.target.value as User['role'] }))}
                label="Perfil"
              >
                <MenuItem value="admin">Administrador</MenuItem>
                <MenuItem value="operador">Operador</MenuItem>
                <MenuItem value="auditor">Auditor</MenuItem>
                <MenuItem value="visualizador">Visualizador</MenuItem>
              </Select>
            </FormControl>
            <FormControl fullWidth variant="outlined">
              <InputLabel>Status</InputLabel>
              <Select
                value={formData.status}
                onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as User['status'] }))}
                label="Status"
              >
                <MenuItem value="active">Ativo</MenuItem>
                <MenuItem value="inactive">Inativo</MenuItem>
              </Select>
            </FormControl>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Cancelar</Button>
            <Button onClick={handleSaveUser} variant="contained">
              {selectedUser ? 'Atualizar' : 'Criar'}
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </Container>
  );
};

export default AdminPage;
