import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Step<PERSON>ontent,
  Button,
  Paper,
  Fade,
  Slide,
  Zoom,
  useTheme,
  useMediaQuery,
  LinearProgress,
  Chip,
  Alert,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  NavigateNext,
  NavigateBefore,
  CheckCircle,
  RadioButtonUnchecked,
  PlayArrow,
  Pause,
  Refresh,
  Info,
  Warning,
  Error as ErrorIcon,
  CheckCircle as SuccessIcon
} from '@mui/icons-material';

interface WizardStep {
  id: string;
  label: string;
  description?: string;
  icon?: React.ReactNode;
  content: React.ReactNode;
  isOptional?: boolean;
  canSkip?: boolean;
  validation?: () => boolean | Promise<boolean>;
}

interface ValidationWizardProps {
  steps: WizardStep[];
  onComplete?: (data: any) => void;
  onStepChange?: (step: number) => void;
  initialStep?: number;
  showProgress?: boolean;
  allowNonLinear?: boolean;
  orientation?: 'horizontal' | 'vertical';
}

const ValidationWizard: React.FC<ValidationWizardProps> = ({
  steps,
  onComplete,
  onStepChange,
  initialStep = 0,
  showProgress = true,
  allowNonLinear = false,
  orientation = 'horizontal'
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [activeStep, setActiveStep] = useState(initialStep);
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());
  const [skippedSteps, setSkippedSteps] = useState<Set<number>>(new Set());
  const [isValidating, setIsValidating] = useState(false);
  const [stepErrors, setStepErrors] = useState<Map<number, string>>(new Map());

  const currentStep = steps[activeStep];
  const isLastStep = activeStep === steps.length - 1;
  const isFirstStep = activeStep === 0;

  const handleNext = async () => {
    if (currentStep?.validation) {
      setIsValidating(true);
      try {
        const isValid = await currentStep.validation();
        if (!isValid) {
          setStepErrors(prev => new Map(prev.set(activeStep, 'Validação falhou')));
          setIsValidating(false);
          return;
        }
        setStepErrors(prev => {
          const newMap = new Map(prev);
          newMap.delete(activeStep);
          return newMap;
        });
      } catch (error) {
        setStepErrors(prev => new Map(prev.set(activeStep, 'Erro na validação')));
        setIsValidating(false);
        return;
      }
      setIsValidating(false);
    }

    setCompletedSteps(prev => new Set(prev.add(activeStep)));
    
    if (isLastStep) {
      onComplete?.(getWizardData());
    } else {
      const nextStep = activeStep + 1;
      setActiveStep(nextStep);
      onStepChange?.(nextStep);
    }
  };

  const handleBack = () => {
    if (!isFirstStep) {
      const prevStep = activeStep - 1;
      setActiveStep(prevStep);
      onStepChange?.(prevStep);
    }
  };

  const handleSkip = () => {
    if (currentStep?.canSkip) {
      setSkippedSteps(prev => new Set(prev.add(activeStep)));
      const nextStep = activeStep + 1;
      setActiveStep(nextStep);
      onStepChange?.(nextStep);
    }
  };

  const handleStepClick = (stepIndex: number) => {
    if (allowNonLinear || completedSteps.has(stepIndex) || stepIndex <= activeStep) {
      setActiveStep(stepIndex);
      onStepChange?.(stepIndex);
    }
  };

  const getWizardData = () => {
    return {
      completedSteps: Array.from(completedSteps),
      skippedSteps: Array.from(skippedSteps),
      currentStep: activeStep,
      totalSteps: steps.length
    };
  };

  const getStepStatus = (stepIndex: number) => {
    if (stepErrors.has(stepIndex)) return 'error';
    if (completedSteps.has(stepIndex)) return 'completed';
    if (skippedSteps.has(stepIndex)) return 'skipped';
    if (stepIndex === activeStep) return 'active';
    return 'pending';
  };

  const getStepIcon = (stepIndex: number): React.ReactElement => {
    const status = getStepStatus(stepIndex);
    const step = steps[stepIndex];
    
    switch (status) {
      case 'completed':
        return <CheckCircle color="success" />;
      case 'error':
        return <ErrorIcon color="error" />;
      case 'active':
        return React.isValidElement(step.icon) ? step.icon : <PlayArrow color="primary" />;
      case 'skipped':
        return <RadioButtonUnchecked color="disabled" />;
      default:
        return React.isValidElement(step.icon) ? step.icon : <RadioButtonUnchecked />;
    }
  };

  const progressPercentage = ((completedSteps.size + skippedSteps.size) / steps.length) * 100;

  const renderMobileStepper = () => (
    <Box sx={{ mb: 3 }}>
      {showProgress && (
        <Box sx={{ mb: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="body2" color="text.secondary">
              Progresso
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {Math.round(progressPercentage)}%
            </Typography>
          </Box>
          <LinearProgress 
            variant="determinate" 
            value={progressPercentage}
            sx={{ height: 8, borderRadius: 4 }}
          />
        </Box>
      )}
      
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
        <Chip
          icon={getStepIcon(activeStep)}
          label={`${activeStep + 1} de ${steps.length}`}
          color="primary"
          variant="outlined"
        />
        <Typography variant="h6" sx={{ flex: 1 }}>
          {currentStep?.label}
        </Typography>
      </Box>
      
      {currentStep?.description && (
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {currentStep.description}
        </Typography>
      )}
    </Box>
  );

  const renderDesktopStepper = () => (
    <Stepper 
      activeStep={activeStep} 
      orientation={orientation}
      sx={{ mb: 4 }}
    >
      {steps.map((step, index) => (
        <Step 
          key={step.id}
          completed={completedSteps.has(index)}
          disabled={!allowNonLinear && index > activeStep && !completedSteps.has(index)}
        >
          <StepLabel
            error={stepErrors.has(index)}
            icon={getStepIcon(index)}
            onClick={() => handleStepClick(index)}
            sx={{ 
              cursor: allowNonLinear || completedSteps.has(index) || index <= activeStep ? 'pointer' : 'default',
              '& .MuiStepLabel-label': {
                fontSize: '0.875rem',
                fontWeight: index === activeStep ? 600 : 400
              }
            }}
          >
            {step.label}
            {step.isOptional && (
              <Typography variant="caption" color="text.secondary">
                (Opcional)
              </Typography>
            )}
          </StepLabel>
          {orientation === 'vertical' && (
            <StepContent>
              <Box sx={{ pb: 2 }}>
                {step.description && (
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {step.description}
                  </Typography>
                )}
              </Box>
            </StepContent>
          )}
        </Step>
      ))}
    </Stepper>
  );

  return (
    <Card sx={{ 
      borderRadius: 3,
      boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
      overflow: 'hidden'
    }}>
      <CardContent sx={{ p: { xs: 2, sm: 3, md: 4 } }}>
        {/* Progress Bar (sempre visível no topo) */}
        {showProgress && !isMobile && (
          <Box sx={{ mb: 3 }}>
            <LinearProgress 
              variant="determinate" 
              value={progressPercentage}
              sx={{ height: 6, borderRadius: 3 }}
            />
            <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
              {Math.round(progressPercentage)}% concluído
            </Typography>
          </Box>
        )}

        {/* Stepper */}
        {isMobile ? renderMobileStepper() : renderDesktopStepper()}

        {/* Error Alert */}
        {stepErrors.has(activeStep) && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {stepErrors.get(activeStep)}
          </Alert>
        )}

        {/* Step Content */}
        <Fade in={true} timeout={300}>
          <Box sx={{ minHeight: 200 }}>
            {currentStep?.content}
          </Box>
        </Fade>

        {/* Navigation Buttons */}
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'space-between',
          alignItems: 'center',
          mt: 4,
          pt: 3,
          borderTop: '1px solid',
          borderColor: 'divider'
        }}>
          <Button
            onClick={handleBack}
            disabled={isFirstStep || isValidating}
            startIcon={<NavigateBefore />}
            sx={{ minWidth: 100 }}
          >
            Voltar
          </Button>

          <Box sx={{ display: 'flex', gap: 1 }}>
            {currentStep?.canSkip && !isLastStep && (
              <Button
                onClick={handleSkip}
                disabled={isValidating}
                color="inherit"
              >
                Pular
              </Button>
            )}
            
            <Button
              onClick={handleNext}
              disabled={isValidating}
              variant="contained"
              endIcon={isLastStep ? <CheckCircle /> : <NavigateNext />}
              sx={{ minWidth: 120 }}
            >
              {isValidating ? 'Validando...' : isLastStep ? 'Concluir' : 'Próximo'}
            </Button>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

export default ValidationWizard;
