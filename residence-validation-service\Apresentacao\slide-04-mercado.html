<!DOCTYPE html>
<html lang="pt-br">
<head>
  <meta charset="UTF-8">
  <title>ProvaSegura - Slide 4: Mercado</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');
    
    body {
      margin: 0;
      padding: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      font-family: 'Inter', 'Segoe UI', Arial, sans-serif;
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }
      .slide {
      width: 90vw;
      max-width: 1280px;
      height: 80vh;
      max-height: 720px;
      background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
      box-shadow: 0 20px 60px rgba(0,0,0,0.15), 0 8px 32px rgba(0,0,0,0.08);
      border-radius: 24px;
      display: flex;
      flex-direction: row;
      overflow: hidden;
      position: relative;
      border: 1px solid rgba(255,255,255,0.2);
    }
    
    .slide::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #f59e0b 0%, #d97706 50%, #b45309 100%);
      border-radius: 24px 24px 0 0;
    }
      .slide-content {
      padding: 40px 60px;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      position: relative;
      z-index: 2;
      overflow-y: auto;
    }
      .slide-title {
      font-size: 2.5rem;
      font-weight: 800;
      background: linear-gradient(135deg, #b45309 0%, #f59e0b 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-bottom: 24px;
      line-height: 1.1;
      letter-spacing: -0.02em;
    }
      .slide-text {
      font-size: 1rem;
      color: #334155;
      line-height: 1.6;
      font-weight: 400;
    }
    
    .slide-text strong {
      color: #92400e;
      font-weight: 700;
    }
      .slide-img {
      flex: 0 0 300px;
      background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      overflow: hidden;
      order: -1;
    }
    
    .slide-img::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 70% 30%, rgba(245, 158, 11, 0.1) 0%, transparent 50%);
    }
    
    .slide-number {
      position: absolute;
      top: 30px;
      right: 40px;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-weight: 700;
      font-size: 1.3rem;
      box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
    }    .bar-chart {
      width: 280px;
      height: 240px;
      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
      border-radius: 20px;
      box-shadow: 0 15px 50px rgba(245, 158, 11, 0.2);
      display: flex;
      align-items: flex-end;
      justify-content: space-around;
      padding: 30px 20px;
      margin: 0 auto;
      position: relative;
      border: 1px solid rgba(245, 158, 11, 0.1);
    }    .bar {
      width: 50px;
      border-radius: 15px 15px 0 0;
      margin: 0 8px;
      display: flex;
      align-items: flex-end;
      justify-content: center;
      color: #fff;
      font-size: 0.85rem;
      font-weight: 800;
      padding-bottom: 10px;
      box-shadow: 0 8px 25px rgba(0,0,0,0.15);
      position: relative;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      cursor: pointer;
    }

    .bar:hover {
      transform: translateY(-10px) scale(1.05);
      box-shadow: 0 15px 35px rgba(0,0,0,0.25);
    }    .bar-rh {
      height: 120px;
      background: linear-gradient(180deg, #3b82f6 0%, #1e40af 50%, #1d4ed8 100%);
      animation: growUp 1.5s ease-out 0.2s both;
    }

    .bar-bens {
      height: 70px;
      background: linear-gradient(180deg, #10b981 0%, #059669 50%, #047857 100%);
      animation: growUp 1.5s ease-out 0.4s both;
    }

    .bar-judicial {
      height: 90px;
      background: linear-gradient(180deg, #8b5cf6 0%, #7c3aed 50%, #6d28d9 100%);
      animation: growUp 1.5s ease-out 0.6s both;
    }    .bar-latam {
      height: 180px;
      background: linear-gradient(180deg, #f59e0b 0%, #d97706 50%, #b45309 100%);
      animation: growUp 1.5s ease-out 0.8s both, pulse 3s ease-in-out 2s infinite;
      position: relative;
      overflow: hidden;
    }

    .bar-latam::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.3) 50%, transparent 70%);
      animation: shine 2s ease-in-out 2.5s infinite;
    }

    .bar-latam::after {
      content: '🚀';
      position: absolute;
      top: -25px;
      left: 50%;
      transform: translateX(-50%);
      font-size: 1.2rem;
      animation: bounce 2s ease-in-out 3s infinite;
    }

    @keyframes pulse {
      0%, 100% {
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
      }
      50% {
        box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4), 0 0 30px rgba(245, 158, 11, 0.2);
      }
    }

    @keyframes bounce {
      0%, 100% {
        transform: translateX(-50%) translateY(0);
      }
      50% {
        transform: translateX(-50%) translateY(-10px);
      }
    }

    @keyframes growUp {
      0% {
        height: 0;
        opacity: 0;
        transform: scaleY(0);
      }
      100% {
        opacity: 1;
        transform: scaleY(1);
      }
    }

    @keyframes shine {
      0%, 100% {
        transform: translateX(-100%);
      }
      50% {
        transform: translateX(100%);
      }
    }    .bar-label {
      position: absolute;
      bottom: -35px;
      left: 50%;
      transform: translateX(-50%);
      font-size: 0.8rem;
      color: #475569;
      font-weight: 700;
      white-space: nowrap;
      background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
      padding: 4px 8px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .chart-title {
      position: absolute;
      top: 15px;
      left: 50%;
      transform: translateX(-50%);
      font-size: 1.1rem;
      font-weight: 800;
      color: #1e293b;
      background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .value-labels {
      position: absolute;
      left: -25px;
      top: 30px;
      height: 180px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      font-size: 0.7rem;
      color: #64748b;
      font-weight: 600;
    }

    .value-labels span {
      background: rgba(255,255,255,0.9);
      padding: 2px 6px;
      border-radius: 4px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }@media print {
      body { background: #fff; }
      .slide { box-shadow: none; border: 1px solid #e2e8f0; }
    }

    @media (max-width: 768px) {
      .slide {
        flex-direction: column;
        width: 95vw;
        height: 90vh;
      }
      
      .slide-content {
        padding: 30px 20px;
      }
      
      .slide-title {
        font-size: 2rem;
      }
      
      .slide-text {
        font-size: 0.9rem;
        line-height: 1.5;
      }
      
      .slide-img {
        flex: 0 0 200px;
      }
        .bar-chart {
        width: 260px;
        height: 200px;
        padding: 25px 15px;
      }
      
      .bar {
        width: 40px;
        font-size: 0.75rem;
        margin: 0 4px;
      }
      
      .bar-rh { height: 90px; }
      .bar-bens { height: 55px; }
      .bar-judicial { height: 70px; }
      .bar-latam { height: 140px; }
      
      .chart-title {
        font-size: 0.95rem;
      }
      
      .bar-label {
        bottom: -30px;
        font-size: 0.7rem;
      }
      
      .value-labels {
        left: -20px;
        height: 140px;
      }
    }

    .navigation {
      position: fixed;
      bottom: 30px;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      gap: 15px;
      z-index: 1000;
    }

    .nav-button {
      background: rgba(255, 255, 255, 0.9);
      border: none;
      padding: 12px 24px;
      border-radius: 25px;
      color: #1e293b;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      text-decoration: none;
      display: inline-block;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .nav-button:hover {
      background: white;
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }
  </style>
</head>
<body>

<div class="slide">
  <div class="slide-number">4</div>
  <div class="slide-content">
    <div class="slide-title">Um Mercado de Bilhões</div>
    <div class="slide-text">
      <strong>Brasil:</strong><br>
      <strong>RH:</strong> 5 milhões de contratações/ano exigem comprovação de residência (R$ 5-60 milhões/ano).<br>
      <strong>Bens caros:</strong> 1 milhão de financiamentos de carros e imóveis/ano (R$ 5-20 milhões/ano).<br>
      <strong>Judicial:</strong> 80.000 casos/ano precisam de provas de localização (R$ 4-16 milhões/ano).<br><br>
      <strong>América Latina:</strong> Potencial de dobrar a receita em mercados como México e Colômbia.<br><br>
      <strong style="font-size: 1.3rem; background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); background-clip: text; -webkit-background-clip: text; -webkit-text-fill-color: transparent;">Total:</strong> R$ 20-100 milhões/ano no Brasil, escalando para R$ 200-500 milhões com logística e seguros.
    </div>
  </div>
  <div class="slide-img">    <div class="bar-chart">
      <div class="chart-title">💰 Potencial de Mercado (R$ Milhões)</div>
      <div class="value-labels">
        <span>500M</span>
        <span>400M</span>
        <span>300M</span>
        <span>200M</span>
        <span>100M</span>
        <span>0</span>
      </div>
      <div class="bar bar-rh" title="Recursos Humanos: R$ 60 milhões/ano">
        <span>60M</span>
        <div class="bar-label">👥 RH</div>
      </div>
      <div class="bar bar-bens" title="Financiamento de Bens: R$ 20 milhões/ano">
        <span>20M</span>
        <div class="bar-label">🏠 Bens</div>
      </div>
      <div class="bar bar-judicial" title="Processos Judiciais: R$ 16 milhões/ano">
        <span>16M</span>
        <div class="bar-label">⚖️ Judicial</div>
      </div>
      <div class="bar bar-latam" title="Expansão América Latina: R$ 500 milhões/ano">
        <span>500M</span>
        <div class="bar-label">🌎 LatAm</div>
      </div>
    </div></div>
</div>

<div class="navigation">
  <a href="slide-03-solucao.html" class="nav-button">← Anterior</a>
  <a href="index.html" class="nav-button">📋 Índice</a>
  <a href="slide-05-aplicacoes.html" class="nav-button">Próximo →</a>
</div>

<script>
document.addEventListener('keydown', function(e) {
  switch(e.key) {
    case 'ArrowLeft':
      window.location.href = 'slide-03-solucao.html';
      break;
    case 'ArrowRight':
      window.location.href = 'slide-05-aplicacoes.html';
      break;
    case 'Home':
      window.location.href = 'index.html';
      break;
    case 'F11':
      e.preventDefault();
      if (document.fullscreenElement) {
        document.exitFullscreen();
      } else {
        document.documentElement.requestFullscreen();
      }
      break;
  }
});
</script>

</body>
</html>
