import React, { useState, useRef, useCallback } from 'react';
import {
  Container,
  Typo<PERSON>,
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  Alert,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  Chip,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  VideoCall,
  Stop,
  Download,
  Description,
  Security,
  CheckCircle
} from '@mui/icons-material';
import { useToast } from '../components/Toast';
import { LoadingOverlay } from '../components/LoadingComponents';

interface RecordingQuality {
  label: string;
  width: number;
  height: number;
  bitrate?: number;
}

interface RecordingData {
  id: string;
  filename: string;
  duration: number;
  size: number;
  hash: string;
  timestamp: string;
}

const RecordPage: React.FC = () => {
  const [isRecording, setIsRecording] = useState(false);
  const [recordingData, setRecordingData] = useState<RecordingData | null>(null);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [selectedQuality, setSelectedQuality] = useState<RecordingQuality>({
    label: 'HD (1280x720)',
    width: 1280,
    height: 720,
    bitrate: 2500000
  });
  const [enableCompression, setEnableCompression] = useState(true);

  const qualityOptions: RecordingQuality[] = [
    { label: 'Baixa (640x360)', width: 640, height: 360, bitrate: 500000 },
    { label: 'Média (854x480)', width: 854, height: 480, bitrate: 1000000 },
    { label: 'HD (1280x720)', width: 1280, height: 720, bitrate: 2500000 },
    { label: 'Full HD (1920x1080)', width: 1920, height: 1080, bitrate: 5000000 },
    { label: 'Ultra HD (3840x2160)', width: 3840, height: 2160, bitrate: 15000000 }
  ];
  const [showConsentDialog, setShowConsentDialog] = useState(false);
  const [consentData, setConsentData] = useState({
    name: '',
    document: '',
    purpose: ''
  });
  
  const getEstimatedFileSize = (durationInSeconds: number): string => {
    const bitrate = selectedQuality.bitrate || 2500000;
    const sizeInBytes = (bitrate / 8) * durationInSeconds;
    const sizeInMB = sizeInBytes / (1024 * 1024);
    
    if (sizeInMB < 1) {
      return `${(sizeInMB * 1024).toFixed(0)} KB`;
    } else if (sizeInMB < 1024) {
      return `${sizeInMB.toFixed(1)} MB`;
    } else {
      return `${(sizeInMB / 1024).toFixed(2)} GB`;
    }
  };

  const { showSuccess, showError, showInfo } = useToast();
  
  const videoRef = useRef<HTMLVideoElement>(null);
  const chunksRef = useRef<Blob[]>([]);
  const streamRef = useRef<MediaStream | null>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const startRecording = useCallback(async () => {
    try {
      setError(null);
      
      // Solicitar permissões de tela e áudio com qualidade selecionada
      const stream = await navigator.mediaDevices.getDisplayMedia({
        video: {
          width: { ideal: selectedQuality.width },
          height: { ideal: selectedQuality.height }
        },
        audio: true
      });

      streamRef.current = stream;

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }

      // Configurar MediaRecorder com bitrate apropriado
      const options: MediaRecorderOptions = {
        mimeType: 'video/webm;codecs=vp9'
      };

      if (selectedQuality.bitrate) {
        options.videoBitsPerSecond = selectedQuality.bitrate;
      }

      const recorder = new MediaRecorder(stream, options);

      chunksRef.current = [];

      recorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data);
        }
      };

      recorder.onstop = async () => {
        setIsProcessing(true);
        
        const blob = new Blob(chunksRef.current, { type: 'video/webm' });
        
        // Gerar dados da gravação
        const recordingInfo: RecordingData = {
          id: `rec_${Date.now()}`,
          filename: `gravacao_${new Date().toISOString().replace(/[:.]/g, '-')}.webm`,
          duration: recordingTime,
          size: blob.size,
          hash: await generateHash(blob),
          timestamp: new Date().toISOString()
        };

        // Preparar dados completos para o backend
        const completeData = {
          ...recordingInfo,
          // Campos de consentimento obrigatórios
          consent_timestamp: new Date().toISOString(),
          consent_ip: '127.0.0.1', // IP local por padrão
          consent_user_agent: navigator.userAgent,
          // Informações do dispositivo
          device_info: JSON.stringify({
            platform: navigator.platform,
            userAgent: navigator.userAgent,
            screen: {
              width: window.screen.width,
              height: window.screen.height
            },
            viewport: {
              width: window.innerWidth,
              height: window.innerHeight
            }
          }),
          // Orientação baseada nas dimensões da tela
          orientation: window.innerWidth > window.innerHeight ? 'horizontal' : 'vertical',
          // Dados do consentimento do usuário
          consent_name: consentData.name,
          consent_document: consentData.document,
          consent_purpose: consentData.purpose
        };

        // Enviar para o backend
        const formData = new FormData();
        formData.append('video', blob, recordingInfo.filename);
        
        // Adicionar todos os campos de dados como campos separados do FormData
        Object.entries(completeData).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            formData.append(key, typeof value === 'object' ? JSON.stringify(value) : String(value));
          }
        });

        try {
          showInfo('Enviando gravação para o servidor...');
          
          // Tentar diferentes endpoints
          const endpoints = ['/api/recordings', '/backend/api/recordings', 'http://localhost:3001/api/recordings'];
          let response;
          let lastError;
          
          for (const endpoint of endpoints) {
            try {
              response = await fetch(endpoint, {
                method: 'POST',
                body: formData
              });
              
              if (response.ok) {
                break; // Se deu certo, sair do loop
              }
              
              lastError = `Endpoint ${endpoint}: ${response.status} ${response.statusText}`;
            } catch (fetchError) {
              lastError = `Endpoint ${endpoint}: ${fetchError}`;
              continue; // Tentar próximo endpoint
            }
          }

          if (!response || !response.ok) {
            throw new Error(`Falha em todos os endpoints. Último erro: ${lastError}`);
          }

          const result = await response.json();
          setRecordingData({ ...recordingInfo, id: result.id });
          showSuccess('Gravação salva com sucesso!');
          
        } catch (err) {
          const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
          setError(`Erro ao salvar gravação: ${errorMessage}`);
          
          // Opção de download local como fallback
          const downloadUrl = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = downloadUrl;
          a.download = recordingInfo.filename;
          a.click();
          URL.revokeObjectURL(downloadUrl);
          
          showInfo('Arquivo baixado localmente como backup.');
          showError(`Erro ao salvar gravação: ${errorMessage}`);
          console.error('Erro detalhado:', err);
        }

        setIsProcessing(false);
      };

      setMediaRecorder(recorder);
      recorder.start(1000); // Capturar dados a cada segundo
      setIsRecording(true);
      setRecordingTime(0);
      showInfo('Gravação iniciada com sucesso!');

      // Timer para contagem
      timerRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);

    } catch (err) {
      setError('Erro ao acessar a tela. Verifique as permissões.');
      showError('Erro ao acessar a tela. Verifique as permissões.');
      console.error(err);
    }
  }, [recordingTime, consentData]);

  const stopRecording = useCallback(() => {
    if (mediaRecorder && isRecording) {
      mediaRecorder.stop();
      setIsRecording(false);
      showInfo('Gravação finalizada. Processando...');
      
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }

      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
    }
  }, [mediaRecorder, isRecording, showInfo]);

  const generateHash = async (blob: Blob): Promise<string> => {
    const arrayBuffer = await blob.arrayBuffer();
    const hashBuffer = await crypto.subtle.digest('SHA-256', arrayBuffer);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  };

  const formatTime = (seconds: number): string => {
    const hrs = Math.floor(seconds / 3600);
    const mins = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hrs.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const formatFileSize = (bytes: number): string => {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(2)} ${units[unitIndex]}`;
  };

  const handleConsentSubmit = () => {
    if (consentData.name && consentData.document && consentData.purpose) {
      setShowConsentDialog(false);
      startRecording();
    }
  };

  const downloadRecording = () => {
    if (recordingData) {
      // Em um ambiente real, isso faria o download do arquivo do servidor
      window.open(`/api/recordings/${recordingData.id}/download`);
    }
  };

  const generateReport = async () => {
    if (recordingData) {
      try {
        const response = await fetch(`http://localhost:3002/generate-report/${recordingData.id}`, {
          method: 'POST'
        });
        
        if (response.ok) {
          const result = await response.json();
          window.open(`/api/reports/${recordingData.id}/pdf`);
        } else {
          setError('Erro ao gerar relatório');
        }
      } catch (err) {
        setError('Erro ao gerar relatório');
        console.error(err);
      }
    }
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ mt: 4, mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          <VideoCall sx={{ mr: 1, verticalAlign: 'middle' }} />
          Sistema de Gravação de Tela para Cartório
        </Typography>
        
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Grid container spacing={3}>
          {/* Controles de Gravação */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Controles de Gravação
                </Typography>
                
                {!isRecording && (
                  <Box sx={{ mb: 3 }}>
                    <FormControl fullWidth sx={{ mb: 2 }}>
                      <InputLabel id="quality-select-label">Qualidade da Gravação</InputLabel>
                      <Select
                        labelId="quality-select-label"
                        value={selectedQuality.label}
                        label="Qualidade da Gravação"
                        onChange={(e) => {
                          const quality = qualityOptions.find(q => q.label === e.target.value);
                          if (quality) setSelectedQuality(quality);
                        }}
                      >
                        {qualityOptions.map((quality) => (
                          <MenuItem key={quality.label} value={quality.label}>
                            <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                              <Typography variant="body2">{quality.label}</Typography>
                              <Typography variant="caption" color="text.secondary">
                                {quality.width}x{quality.height} - {Math.round((quality.bitrate || 0) / 1000000)}Mbps
                              </Typography>
                            </Box>
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                    
                    <Alert severity="info" sx={{ mb: 2 }}>
                      <Typography variant="body2">
                        <strong>Dica:</strong> Qualidades menores resultam em arquivos menores e upload mais rápido.
                        Para documentos, recomendamos HD (1280x720).
                      </Typography>
                    </Alert>
                    
                    <FormControlLabel
                      control={
                        <Switch
                          checked={enableCompression}
                          onChange={(e) => setEnableCompression(e.target.checked)}
                          color="primary"
                        />
                      }
                      label={
                        <Box>
                          <Typography variant="body2">Otimização automática</Typography>
                          <Typography variant="caption" color="text.secondary">
                            Reduz o tamanho do arquivo automaticamente
                          </Typography>
                        </Box>
                      }
                    />
                  </Box>
                )}
                
                {!isRecording && !recordingData && (
                  <Button
                    variant="contained"
                    color="primary"
                    size="large"
                    startIcon={<VideoCall />}
                    onClick={() => setShowConsentDialog(true)}
                    fullWidth
                    sx={{ mb: 2 }}
                  >
                    Iniciar Gravação
                  </Button>
                )}

                {isRecording && (
                  <Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Chip 
                        label="GRAVANDO" 
                        color="error" 
                        variant="filled"
                        sx={{ mr: 2 }}
                      />
                      <Typography variant="h6">
                        {formatTime(recordingTime)}
                      </Typography>
                    </Box>
                    
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        Tamanho estimado: {getEstimatedFileSize(recordingTime)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Qualidade: {selectedQuality.label}
                      </Typography>
                    </Box>
                    
                    <Button
                      variant="contained"
                      color="error"
                      size="large"
                      startIcon={<Stop />}
                      onClick={stopRecording}
                      fullWidth
                    >
                      Parar Gravação
                    </Button>
                  </Box>
                )}

                {isProcessing && (
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="body2" gutterBottom>
                      Processando gravação...
                    </Typography>
                    <LinearProgress />
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Preview da Gravação */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Visualização
                </Typography>
                <Box sx={{ 
                  width: '100%', 
                  height: 300, 
                  bgcolor: 'grey.100',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  borderRadius: 1
                }}>
                  <video
                    ref={videoRef}
                    autoPlay
                    muted
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'contain'
                    }}
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Informações da Gravação */}
          {recordingData && (
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    <CheckCircle sx={{ mr: 1, color: 'success.main' }} />
                    Gravação Concluída
                  </Typography>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <Paper sx={{ p: 2 }}>
                        <Typography variant="subtitle2" color="textSecondary">
                          ID da Gravação
                        </Typography>
                        <Typography variant="body1" sx={{ fontFamily: 'monospace' }}>
                          {recordingData.id}
                        </Typography>
                      </Paper>
                    </Grid>
                    
                    <Grid item xs={12} md={6}>
                      <Paper sx={{ p: 2 }}>
                        <Typography variant="subtitle2" color="textSecondary">
                          Duração
                        </Typography>
                        <Typography variant="body1">
                          {formatTime(recordingData.duration)}
                        </Typography>
                      </Paper>
                    </Grid>
                    
                    <Grid item xs={12} md={6}>
                      <Paper sx={{ p: 2 }}>
                        <Typography variant="subtitle2" color="textSecondary">
                          Tamanho do Arquivo
                        </Typography>
                        <Typography variant="body1">
                          {formatFileSize(recordingData.size)}
                        </Typography>
                      </Paper>
                    </Grid>
                    
                    <Grid item xs={12} md={6}>
                      <Paper sx={{ p: 2 }}>
                        <Typography variant="subtitle2" color="textSecondary">
                          Hash SHA-256
                        </Typography>
                        <Typography variant="body2" sx={{ 
                          fontFamily: 'monospace',
                          wordBreak: 'break-all'
                        }}>
                          {recordingData.hash}
                        </Typography>
                      </Paper>
                    </Grid>
                  </Grid>

                  <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
                    <Button
                      variant="contained"
                      startIcon={<Download />}
                      onClick={downloadRecording}
                    >
                      Baixar Gravação
                    </Button>
                    
                    <Button
                      variant="outlined"
                      startIcon={<Description />}
                      onClick={generateReport}
                    >
                      Gerar Relatório PDF
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          )}
        </Grid>

        {/* Dialog de Consentimento LGPD */}
        <Dialog open={showConsentDialog} maxWidth="sm" fullWidth>
          <DialogTitle>
            <Security sx={{ mr: 1 }} />
            Consentimento LGPD
          </DialogTitle>
          <DialogContent>
            <Typography variant="body2" color="textSecondary" sx={{ mb: 3 }}>
              Para prosseguir com a gravação, precisamos do seu consentimento conforme a Lei Geral de Proteção de Dados (LGPD).
            </Typography>
            
            <TextField
              fullWidth
              label="Nome Completo"
              value={consentData.name}
              onChange={(e) => setConsentData(prev => ({ ...prev, name: e.target.value }))}
              sx={{ mb: 2 }}
              required
            />
            
            <TextField
              fullWidth
              label="CPF/CNPJ"
              value={consentData.document}
              onChange={(e) => setConsentData(prev => ({ ...prev, document: e.target.value }))}
              sx={{ mb: 2 }}
              required
            />
            
            <TextField
              fullWidth
              label="Finalidade da Gravação"
              value={consentData.purpose}
              onChange={(e) => setConsentData(prev => ({ ...prev, purpose: e.target.value }))}
              multiline
              rows={3}
              required
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowConsentDialog(false)}>
              Cancelar
            </Button>
            <Button 
              onClick={handleConsentSubmit}
              variant="contained"
              disabled={!consentData.name || !consentData.document || !consentData.purpose}
            >
              Aceitar e Iniciar Gravação
            </Button>
          </DialogActions>
        </Dialog>

        {/* Loading Overlay */}
        <LoadingOverlay 
          open={isProcessing} 
          message="Processando gravação..." 
        />
      </Box>
    </Container>
  );
};

export default RecordPage;
