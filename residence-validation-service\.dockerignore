# ==============================================================================
# Docker Ignore - Sistema de Validação de Residência
# ==============================================================================

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Dependências
node_modules/
npm-debug.log*
.npm

# Build
.next/
out/
build/
dist/

# Arquivos de ambiente (mantém apenas exemplo)
.env.local
.env.development.local
.env.test.local
.env.production.local

# Cache
.cache/
.parcel-cache/

# Sistema operacional
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Editor
.vscode/
.idea/
*.swp
*.swo
*~

# Testing
coverage/
.coverage
.nyc_output

# Misc
*.tgz
*.tar.gz

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Git
.git/
.gitignore

# README e documentação de desenvolvimento
README.md
CONTRIBUTING.md
CHANGELOG.md
docs/

# Scripts de desenvolvimento
scripts/
.husky/

# Backups e temporários
backups/
tmp/
temp/

# SSL (será montado como volume)
ssl/

# Arquivos de configuração local
.env*
!.env.example
