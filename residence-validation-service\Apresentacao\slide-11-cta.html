<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ProvaSegura - Slide 11 - Call to Action</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
        }

        .slide {
            width: 90vw;
            max-width: 1200px;
            height: 80vh;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 60px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .slide::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
            pointer-events: none;
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .slide-number {
            position: absolute;
            top: 30px;
            right: 40px;
            background: rgba(255, 255, 255, 0.2);
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            backdrop-filter: blur(5px);
        }

        .slide-content {
            position: relative;
            z-index: 1;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .main-title {
            font-size: 4rem;
            font-weight: 700;
            margin-bottom: 30px;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { text-shadow: 0 0 30px rgba(255, 215, 0, 0.5); }
            to { text-shadow: 0 0 40px rgba(255, 215, 0, 0.8); }
        }

        .subtitle {
            font-size: 1.8rem;
            font-weight: 300;
            margin-bottom: 50px;
            color: rgba(255, 255, 255, 0.9);
            line-height: 1.4;
        }

        .cta-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 40px;
            margin-bottom: 50px;
        }

        .primary-cta {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
            padding: 20px 50px;
            border: none;
            border-radius: 50px;
            font-size: 1.4rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
            text-decoration: none;
            display: inline-block;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .primary-cta:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 15px 40px rgba(255, 107, 107, 0.5);
        }

        .contact-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            width: 100%;
            max-width: 800px;
            margin-top: 40px;
        }

        .contact-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .contact-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.15);
        }

        .contact-icon {
            width: 60px;
            height: 60px;
            margin: 0 auto 20px;
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .contact-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: #FFD700;
        }

        .contact-details {
            font-size: 1rem;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.8);
        }

        .urgency-badge {
            background: linear-gradient(45deg, #FF4757, #FF6B6B);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 30px;
            animation: blink 1.5s ease-in-out infinite;
        }

        @keyframes blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .navigation {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
        }

        .nav-button {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
            text-decoration: none;
            display: inline-block;
        }

        .nav-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .slide {
                padding: 40px 30px;
                width: 95vw;
                height: 90vh;
            }

            .main-title {
                font-size: 2.8rem;
            }

            .subtitle {
                font-size: 1.4rem;
            }

            .primary-cta {
                padding: 18px 40px;
                font-size: 1.2rem;
            }

            .contact-info {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="slide-number">11/11</div>
        
        <div class="slide-content">
            <div class="urgency-badge">⚡ OPORTUNIDADE LIMITADA</div>
            
            <h1 class="main-title">Junte-se à Revolução</h1>
            
            <p class="subtitle">
                Seja parte da transformação digital que irá<br>
                impactar milhões de vidas na América Latina
            </p>

            <div class="cta-container">
                <a href="mailto:<EMAIL>" class="primary-cta">
                    🚀 INVESTIR AGORA
                </a>
                
                <div class="contact-info">
                    <div class="contact-card">
                        <div class="contact-icon">
                            <svg width="30" height="30" viewBox="0 0 24 24" fill="white">
                                <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                            </svg>
                        </div>
                        <div class="contact-title">E-mail</div>
                        <div class="contact-details">
                            <EMAIL><br>
                            <EMAIL>
                        </div>
                    </div>
                    
                    <div class="contact-card">
                        <div class="contact-icon">
                            <svg width="30" height="30" viewBox="0 0 24 24" fill="white">
                                <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"/>
                            </svg>
                        </div>
                        <div class="contact-title">Telefone</div>
                        <div class="contact-details">
                            +55 (11) 9 9999-9999<br>
                            WhatsApp: +55 (11) 9 8888-8888
                        </div>
                    </div>
                    
                    <div class="contact-card">
                        <div class="contact-icon">
                            <svg width="30" height="30" viewBox="0 0 24 24" fill="white">
                                <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                            </svg>
                        </div>
                        <div class="contact-title">Escritório</div>
                        <div class="contact-details">
                            São Paulo - SP<br>
                            Av. Paulista, 1000 - Conj. 101
                        </div>
                    </div>
                    
                    <div class="contact-card">
                        <div class="contact-icon">
                            <svg width="30" height="30" viewBox="0 0 24 24" fill="white">
                                <path d="M19 3a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14m-.5 15.5v-5.3a3.26 3.26 0 0 0-3.26-3.26c-.85 0-1.84.52-2.32 1.3v-1.11h-2.79v8.37h2.79v-4.93c0-.77.62-1.4 1.39-1.4a1.4 1.4 0 0 1 1.4 1.4v4.93h2.79M6.88 8.56a1.68 1.68 0 0 0 1.68-1.68c0-.93-.75-1.69-1.68-1.69a1.69 1.69 0 0 0-1.69 1.69c0 .93.76 1.68 1.69 1.68m1.39 9.94v-8.37H5.5v8.37h2.77z"/>
                            </svg>
                        </div>
                        <div class="contact-title">LinkedIn</div>
                        <div class="contact-details">
                            /company/provasegura<br>
                            /in/ceo-provasegura
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="navigation">
            <a href="slide-10-equipe.html" class="nav-button">← Anterior</a>
            <a href="index.html" class="nav-button">📋 Índice</a>
            <a href="slide-01-capa.html" class="nav-button">🔁 Reiniciar</a>
        </div>
    </div>

    <script>
        // Adicionar efeitos de interação
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.contact-card');
            
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Navegação por teclado
            document.addEventListener('keydown', function(e) {
                switch(e.key) {
                    case 'ArrowLeft':
                        window.location.href = 'slide-10-equipe.html';
                        break;
                    case 'ArrowRight':
                        window.location.href = 'slide-01-capa.html';
                        break;
                    case 'Home':
                        window.location.href = 'index.html';
                        break;
                    case 'F11':
                        e.preventDefault();
                        if (document.fullscreenElement) {
                            document.exitFullscreen();
                        } else {
                            document.documentElement.requestFullscreen();
                        }
                        break;
                }
            });
        });
    </script>
</body>
</html>
