// Compatibilidade layer para conectar PostgreSQL adapter com as rotas existentes
const postgresModule = require('./database/postgresql');
const crypto = require('crypto');
const fs = require('fs');

let postgresAdapter = null;

/**
 * Inicializa a camada de compatibilidade do banco de dados.
 * @throws {Error} Se a inicialização do adaptador PostgreSQL falhar.
 */
async function initializeDatabase() {
  console.log('🔄 Inicializando camada de compatibilidade do banco de dados...');
  try {
    postgresAdapter = await postgresModule.initialize();
    if (!postgresAdapter || typeof postgresAdapter !== 'object') {
      throw new Error('postgresAdapter não foi inicializado corretamente');
    }
    // Verifica se os métodos esperados estão disponíveis
    const requiredMethods = [
      'saveRecording',
      'getRecordingById',
      'getRecordings',
      'deleteRecording',
      'saveReport',
      'updateNotaryInfo',
      'getStats',
      'getRecordingsByPeriod'
    ];
    for (const method of requiredMethods) {
      if (typeof postgresAdapter[method] !== 'function') {
        throw new Error(`Método ${method} não está definido no postgresAdapter`);
      }
    }
    console.log('✅ Camada de compatibilidade do banco de dados inicializada com sucesso');
  } catch (error) {
    console.error('❌ Erro ao inicializar camada de compatibilidade:', error.message);
    throw error;
  }
}

/**
 * Salva uma gravação no banco de dados.
 * @param {Object} recordingData - Dados da gravação.
 * @returns {Promise<Object>} Dados da gravação salva.
 * @throws {Error} Se os dados forem inválidos ou a operação falhar.
 */
async function saveRecording(recordingData) {
  try {
    if (!postgresAdapter) {
      throw new Error('postgresAdapter não está inicializado');
    }
    if (!recordingData || typeof recordingData !== 'object') {
      throw new Error('Dados da gravação inválidos');
    }
    console.log('💾 Salvando gravação via camada de compatibilidade...');
    const result = await postgresAdapter.saveRecording(recordingData);
    console.log('✅ Gravação salva com sucesso:', result.id);
    return result;
  } catch (error) {
    console.error('❌ Erro ao salvar gravação:', error.message);
    throw error;
  }
}

/**
 * Busca uma gravação por ID.
 * @param {string|number} id - ID da gravação.
 * @returns {Promise<Object|null>} Dados da gravação ou null se não encontrada.
 * @throws {Error} Se a busca falhar.
 */
async function getRecordingById(id) {
  try {
    if (!postgresAdapter) {
      throw new Error('postgresAdapter não está inicializado');
    }
    if (!id || (typeof id !== 'string' && typeof id !== 'number')) {
      throw new Error('ID da gravação inválido');
    }
    console.log(`🔍 Buscando gravação ${id} via camada de compatibilidade...`);
    const result = await postgresAdapter.getRecordingById(id);
    if (result) {
      console.log('✅ Gravação encontrada:', result.id);
    } else {
      console.log('⚠️ Gravação não encontrada');
    }
    return result;
  } catch (error) {
    console.error(`❌ Erro ao buscar gravação ${id}:`, error.message);
    throw error;
  }
}

/**
 * Busca todas as gravações com opções de filtragem.
 * @param {Object} [options={}] - Opções de filtragem (ex.: limit, offset).
 * @returns {Promise<Object|Array>} Lista de gravações ou objeto com metadados.
 * @throws {Error} Se a busca falhar.
 */
async function getAllRecordings(options = {}) {
  try {
    if (!postgresAdapter) {
      throw new Error('postgresAdapter não está inicializado');
    }
    console.log('📋 Buscando todas as gravações via camada de compatibilidade...');
    const results = await postgresAdapter.getRecordings(options);
    const recordings = Array.isArray(results) ? results : results.recordings || [];
    console.log(`✅ Encontradas ${recordings.length} gravações`);
    return Array.isArray(results) ? { recordings } : results;
  } catch (error) {
    console.error('❌ Erro ao buscar gravações:', error.message);
    throw error;
  }
}

/**
 * Deleta uma gravação por ID.
 * @param {string|number} id - ID da gravação.
 * @returns {Promise<Object>} Resultado da operação.
 * @throws {Error} Se a deleção falhar.
 */
async function deleteRecording(id) {
  try {
    if (!postgresAdapter) {
      throw new Error('postgresAdapter não está inicializado');
    }
    if (!id || (typeof id !== 'string' && typeof id !== 'number')) {
      throw new Error('ID da gravação inválido');
    }
    console.log(`🗑️ Deletando gravação ${id} via camada de compatibilidade...`);
    const result = await postgresAdapter.deleteRecording(id);
    console.log('✅ Gravação deletada com sucesso');
    return result;
  } catch (error) {
    console.error(`❌ Erro ao deletar gravação ${id}:`, error.message);
    throw error;
  }
}

/**
 * Obtém gravações por período.
 * @param {string} period - Período para filtrar gravações ('day', 'week', 'month', 'year').
 * @returns {Promise<Array>} Lista de gravações.
 * @throws {Error} Se a consulta ao banco falhar.
 */
async function getRecordingsByPeriod(period = 'month') {
  try {
    if (!postgresAdapter) {
      throw new Error('postgresAdapter não está inicializado');
    }
    if (!['day', 'week', 'month', 'year'].includes(period)) {
      throw new Error(`Período inválido: ${period}. Use: day, week, month, year`);
    }
    console.log(`📊 Obtendo gravações do período: ${period}...`);
    const recordings = await postgresAdapter.getRecordingsByPeriod(period);
    console.log(`✅ Encontradas ${recordings.length} gravações no período`);
    return recordings;
  } catch (error) {
    console.error(`❌ Erro ao obter gravações por período ${period}:`, error.message);
    throw error;
  }
}

/**
 * Obtém estatísticas do sistema.
 * @returns {Promise<Object>} Estatísticas do sistema.
 * @throws {Error} Se a consulta falhar.
 */
async function getSystemStats() {
  try {
    if (!postgresAdapter) {
      throw new Error('postgresAdapter não está inicializado');
    }
    console.log('📊 Obtendo estatísticas do sistema...');
    const stats = await postgresAdapter.getStats();
    const enhancedStats = {
      total_recordings: stats.totalRecordings || 0,
      total_size: stats.totalSize || 0,
      avg_duration: stats.avgDuration || 0,
      completed_recordings: stats.completedRecordings || 0,
      processing_recordings: stats.processingRecordings || 0,
      failed_recordings: stats.failedRecordings || 0,
      archived_recordings: stats.archivedRecordings || 0,
      unique_users: stats.totalUsers || 0,
      first_recording: stats.firstRecording || null,
      last_recording: stats.lastRecording || null,
    };
    console.log('✅ Estatísticas obtidas:', enhancedStats);
    return enhancedStats;
  } catch (error) {
    console.error('❌ Erro ao obter estatísticas:', error.message);
    throw error;
  }
}

/**
 * Obtém o número de usuários únicos.
 * @returns {Promise<number>} Número de usuários únicos.
 * @throws {Error} Se a consulta falhar.
 */
async function getUniqueUsers() {
  try {
    if (!postgresAdapter) {
      throw new Error('postgresAdapter não está inicializado');
    }
    console.log('👥 Obtendo usuários únicos...');
    const stats = await postgresAdapter.getStats();
    const totalUsers = stats.totalUsers || 0;
    console.log(`✅ Encontrados ${totalUsers} usuários únicos`);
    return totalUsers;
  } catch (error) {
    console.error('❌ Erro ao obter usuários únicos:', error.message);
    throw error;
  }
}

/**
 * Salva um relatório no banco de dados.
 * @param {Object} reportData - Dados do relatório.
 * @returns {Promise<Object>} Dados do relatório salvo.
 * @throws {Error} Se os dados forem inválidos ou a operação falhar.
 */
async function saveReport(reportData) {
  try {
    if (!postgresAdapter) {
      throw new Error('postgresAdapter não está inicializado');
    }
    if (!reportData || typeof reportData !== 'object') {
      throw new Error('Dados do relatório inválidos');
    }
    console.log('📄 Salvando relatório via camada de compatibilidade...');
    const result = await postgresAdapter.saveReport(reportData);
    console.log('✅ Relatório salvo com sucesso:', result.id);
    return result;
  } catch (error) {
    console.error('❌ Erro ao salvar relatório:', error.message);
    throw error;
  }
}

/**
 * Atualiza informações notariais de um relatório.
 * @param {string|number} reportId - ID do relatório.
 * @param {Object} notaryInfo - Informações notariais.
 * @returns {Promise<Object>} Resultado da operação.
 * @throws {Error} Se os dados forem inválidos ou a operação falhar.
 */
async function updateNotaryInfo(reportId, notaryInfo) {
  try {
    if (!postgresAdapter) {
      throw new Error('postgresAdapter não está inicializado');
    }
    if (!reportId || (typeof reportId !== 'string' && typeof reportId !== 'number')) {
      throw new Error('ID do relatório inválido');
    }
    if (!notaryInfo || typeof notaryInfo !== 'object') {
      throw new Error('Informações notariais inválidas');
    }
    console.log(`📝 Atualizando informações notariais para relatório ${reportId}...`);
    const result = await postgresAdapter.updateNotaryInfo(reportId, notaryInfo);
    console.log('✅ Informações notariais atualizadas com sucesso');
    return result;
  } catch (error) {
    console.error(`❌ Erro ao atualizar informações notariais para relatório ${reportId}:`, error.message);
    throw error;
  }
}

/**
 * Criptografa dados usando AES-256-CBC.
 * @param {Buffer|string} data - Dados a serem criptografados.
 * @returns {Buffer} Dados criptografados (salt + iv + dados).
 * @throws {Error} Se a chave de criptografia não estiver definida.
 */
function encryptData(data) {
  try {
    if (!process.env.ENCRYPTION_KEY) {
      throw new Error('Chave de criptografia ENCRYPTION_KEY não definida');
    }
    const salt = crypto.randomBytes(16);
    const key = crypto.pbkdf2Sync(process.env.ENCRYPTION_KEY, salt, 100000, 32, 'sha256');
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);
    let encrypted = cipher.update(data);
    encrypted = Buffer.concat([encrypted, cipher.final()]);
    return Buffer.concat([salt, iv, encrypted]);
  } catch (error) {
    console.error('❌ Erro ao criptografar dados:', error.message);
    throw error;
  }
}

/**
 * Descriptografa dados usando AES-256-CBC.
 * @param {Buffer} encryptedData - Dados criptografados (salt + iv + dados).
 * @returns {Buffer} Dados descriptografados.
 * @throws {Error} Se a chave de criptografia não estiver definida ou os dados forem inválidos.
 */
function decryptData(encryptedData) {
  try {
    if (!process.env.ENCRYPTION_KEY) {
      throw new Error('Chave de criptografia ENCRYPTION_KEY não definida');
    }
    if (!Buffer.isBuffer(encryptedData) || encryptedData.length < 32) {
      throw new Error('Dados criptografados inválidos');
    }
    const salt = encryptedData.slice(0, 16);
    const iv = encryptedData.slice(16, 32);
    const encrypted = encryptedData.slice(32);
    const key = crypto.pbkdf2Sync(process.env.ENCRYPTION_KEY, salt, 100000, 32, 'sha256');
    const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);
    let decrypted = decipher.update(encrypted);
    decrypted = Buffer.concat([decrypted, decipher.final()]);
    return decrypted;
  } catch (error) {
    console.error('❌ Erro ao descriptografar dados:', error.message);
    throw error;
  }
}

/**
 * Calcula o hash SHA-256 de um arquivo.
 * @param {string} filePath - Caminho do arquivo.
 * @returns {string} Hash em formato hexadecimal.
 * @throws {Error} Se o arquivo não puder ser lido.
 */
function calculateFileHash(filePath) {
  try {
    if (!filePath || typeof filePath !== 'string') {
      throw new Error('Caminho do arquivo inválido');
    }
    console.log('🔐 Calculando hash do arquivo...');
    const hash = crypto.createHash('sha256');
    const data = fs.readFileSync(filePath);
    hash.update(data);
    const result = hash.digest('hex');
    console.log('✅ Hash calculado com sucesso');
    return result;
  } catch (error) {
    console.error('❌ Erro ao calcular hash:', error.message);
    throw error;
  }
}

/**
 * Obtém a instância do adaptador de banco de dados.
 * @returns {Object|null} Instância do postgresAdapter.
 * @throws {Error} Se o adaptador não estiver inicializado.
 */
function getDatabase() {
  if (!postgresAdapter) {
    throw new Error('postgresAdapter não está inicializado');
  }
  return postgresAdapter;
}

module.exports = {
  initializeDatabase,
  saveRecording,
  getRecordingById,
  getAllRecordings,
  deleteRecording,
  getSystemStats,
  getUniqueUsers,
  getRecordingsByPeriod,
  saveReport,
  updateNotaryInfo,
  encryptData,
  decryptData,
  calculateFileHash,
  getDatabase,
};