const crypto = require('crypto');
const { Pool } = require('pg');

class AuditService {
  constructor() {
    // Pool de conexões PostgreSQL
    this.pool = new Pool({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 5432,
      database: process.env.DB_NAME || 'cartorio_db',
      user: process.env.DB_USER || 'cartorio_user',
      password: process.env.DB_PASSWORD || 'cartorio_secure_pass',
    });
    
    // Configurações de auditoria
    this.hashAlgorithm = 'sha256';
    this.auditEnabled = process.env.AUDIT_ENABLED !== 'false';
    this.immutableLogging = process.env.IMMUTABLE_LOGGING !== 'false';
  }

  /**
   * Gerar hash imutável para log de auditoria
   */
  generateLogHash(logData) {
    const dataString = JSON.stringify(logData, Object.keys(logData).sort());
    return crypto.createHash(this.hashAlgorithm).update(dataString).digest('hex');
  }

  /**
   * Registrar ação de auditoria
   */
  async logAction(actionData) {
    if (!this.auditEnabled) {
      return { logged: false, reason: 'Auditoria desabilitada' };
    }

    try {
      const {
        recordingId,
        action,
        userId,
        userName,
        userRole,
        ipAddress,
        userAgent,
        details = {},
        severity = 'INFO',
        category = 'GENERAL'
      } = actionData;

      // Preparar dados do log
      const logEntry = {
        recordingId,
        action,
        userId: userId || 'anonymous',
        userName: userName || 'Unknown',
        userRole: userRole || 'guest',
        ipAddress: ipAddress || 'unknown',
        userAgent: userAgent || 'unknown',
        details,
        severity,
        category,
        timestamp: new Date().toISOString(),
        sessionId: this.generateSessionId(userId, ipAddress)
      };

      // Gerar hash imutável
      const logHash = this.generateLogHash(logEntry);

      // Inserir no banco
      const query = `
        INSERT INTO audit_logs 
        (recording_id, action, user_id, user_name, user_role, ip_address, user_agent, 
         details, severity, category, session_id, log_hash, timestamp) 
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
        RETURNING id
      `;

      const values = [
        recordingId,
        action,
        logEntry.userId,
        logEntry.userName,
        logEntry.userRole,
        logEntry.ipAddress,
        logEntry.userAgent,
        JSON.stringify(logEntry.details),
        logEntry.severity,
        logEntry.category,
        logEntry.sessionId,
        logHash,
        logEntry.timestamp
      ];

      const result = await this.pool.query(query, values);
      const logId = result.rows[0].id;

      console.log(`[Audit] Ação registrada: ${action} - ID: ${logId}`);

      return {
        logged: true,
        logId,
        hash: logHash,
        timestamp: logEntry.timestamp
      };
    } catch (error) {
      console.error('[Audit] Erro ao registrar ação:', error);
      throw new Error(`Erro no log de auditoria: ${error.message}`);
    }
  }

  /**
   * Gerar ID de sessão único
   */
  generateSessionId(userId, ipAddress) {
    const sessionData = `${userId || 'anonymous'}_${ipAddress}_${Date.now()}`;
    return crypto.createHash('md5').update(sessionData).digest('hex').substring(0, 16);
  }

  /**
   * Verificar integridade de logs
   */
  async verifyLogIntegrity(logId) {
    try {
      const query = 'SELECT * FROM audit_logs WHERE id = $1';
      const result = await this.pool.query(query, [logId]);

      if (result.rows.length === 0) {
        return {
          valid: false,
          reason: 'LOG_NOT_FOUND',
          logId
        };
      }

      const log = result.rows[0];
      
      // Reconstruir dados para verificação
      const logData = {
        recordingId: log.recording_id,
        action: log.action,
        userId: log.user_id,
        userName: log.user_name,
        userRole: log.user_role,
        ipAddress: log.ip_address,
        userAgent: log.user_agent,
        details: log.details,
        severity: log.severity,
        category: log.category,
        timestamp: log.timestamp.toISOString(),
        sessionId: log.session_id
      };

      // Calcular hash atual
      const currentHash = this.generateLogHash(logData);
      const storedHash = log.log_hash;

      const isValid = currentHash === storedHash;

      return {
        valid: isValid,
        reason: isValid ? 'HASH_MATCH' : 'HASH_MISMATCH',
        logId,
        currentHash,
        storedHash,
        verifiedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('[Audit] Erro na verificação de integridade:', error);
      return {
        valid: false,
        reason: 'VERIFICATION_ERROR',
        error: error.message,
        logId
      };
    }
  }

  /**
   * Verificar integridade de múltiplos logs
   */
  async verifyMultipleLogsIntegrity(logIds) {
    const results = {
      total: logIds.length,
      valid: 0,
      invalid: 0,
      details: []
    };

    for (const logId of logIds) {
      try {
        const verification = await this.verifyLogIntegrity(logId);
        
        if (verification.valid) {
          results.valid++;
        } else {
          results.invalid++;
        }
        
        results.details.push(verification);
      } catch (error) {
        results.invalid++;
        results.details.push({
          valid: false,
          reason: 'VERIFICATION_ERROR',
          error: error.message,
          logId
        });
      }
    }

    return results;
  }

  /**
   * Obter logs de auditoria com filtros
   */
  async getAuditLogs(filters = {}) {
    try {
      const {
        recordingId,
        userId,
        action,
        severity,
        category,
        startDate,
        endDate,
        limit = 100,
        offset = 0,
        orderBy = 'timestamp',
        orderDirection = 'DESC'
      } = filters;

      let query = 'SELECT * FROM audit_logs WHERE 1=1';
      const params = [];
      let paramIndex = 1;

      // Aplicar filtros
      if (recordingId) {
        query += ` AND recording_id = $${paramIndex}`;
        params.push(recordingId);
        paramIndex++;
      }

      if (userId) {
        query += ` AND user_id = $${paramIndex}`;
        params.push(userId);
        paramIndex++;
      }

      if (action) {
        query += ` AND action = $${paramIndex}`;
        params.push(action);
        paramIndex++;
      }

      if (severity) {
        query += ` AND severity = $${paramIndex}`;
        params.push(severity);
        paramIndex++;
      }

      if (category) {
        query += ` AND category = $${paramIndex}`;
        params.push(category);
        paramIndex++;
      }

      if (startDate) {
        query += ` AND timestamp >= $${paramIndex}`;
        params.push(startDate);
        paramIndex++;
      }

      if (endDate) {
        query += ` AND timestamp <= $${paramIndex}`;
        params.push(endDate);
        paramIndex++;
      }

      // Ordenação
      const validOrderBy = ['timestamp', 'action', 'severity', 'user_id'];
      const validDirection = ['ASC', 'DESC'];
      
      if (validOrderBy.includes(orderBy) && validDirection.includes(orderDirection.toUpperCase())) {
        query += ` ORDER BY ${orderBy} ${orderDirection.toUpperCase()}`;
      } else {
        query += ' ORDER BY timestamp DESC';
      }

      // Paginação
      query += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
      params.push(limit, offset);

      const result = await this.pool.query(query, params);

      // Contar total de registros
      let countQuery = 'SELECT COUNT(*) as total FROM audit_logs WHERE 1=1';
      const countParams = params.slice(0, -2); // Remove limit e offset
      
      // Reaplicar filtros para contagem
      let countParamIndex = 1;
      if (recordingId) {
        countQuery += ` AND recording_id = $${countParamIndex++}`;
      }
      if (userId) {
        countQuery += ` AND user_id = $${countParamIndex++}`;
      }
      if (action) {
        countQuery += ` AND action = $${countParamIndex++}`;
      }
      if (severity) {
        countQuery += ` AND severity = $${countParamIndex++}`;
      }
      if (category) {
        countQuery += ` AND category = $${countParamIndex++}`;
      }
      if (startDate) {
        countQuery += ` AND timestamp >= $${countParamIndex++}`;
      }
      if (endDate) {
        countQuery += ` AND timestamp <= $${countParamIndex++}`;
      }

      const countResult = await this.pool.query(countQuery, countParams);
      const totalRecords = parseInt(countResult.rows[0].total);

      return {
        logs: result.rows,
        pagination: {
          total: totalRecords,
          limit,
          offset,
          pages: Math.ceil(totalRecords / limit),
          currentPage: Math.floor(offset / limit) + 1
        },
        filters,
        retrievedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('[Audit] Erro ao obter logs:', error);
      throw new Error(`Erro ao obter logs de auditoria: ${error.message}`);
    }
  }

  /**
   * Gerar relatório de auditoria
   */
  async generateAuditReport(filters = {}) {
    try {
      const logs = await this.getAuditLogs(filters);
      
      // Estatísticas por ação
      const actionStats = await this.pool.query(`
        SELECT action, COUNT(*) as count, 
               COUNT(CASE WHEN severity = 'ERROR' THEN 1 END) as errors,
               COUNT(CASE WHEN severity = 'WARNING' THEN 1 END) as warnings
        FROM audit_logs 
        WHERE timestamp >= COALESCE($1, timestamp) 
        AND timestamp <= COALESCE($2, timestamp)
        GROUP BY action 
        ORDER BY count DESC
      `, [filters.startDate, filters.endDate]);

      // Estatísticas por usuário
      const userStats = await this.pool.query(`
        SELECT user_id, user_name, COUNT(*) as actions,
               COUNT(DISTINCT recording_id) as recordings_accessed,
               MAX(timestamp) as last_activity
        FROM audit_logs 
        WHERE timestamp >= COALESCE($1, timestamp) 
        AND timestamp <= COALESCE($2, timestamp)
        GROUP BY user_id, user_name 
        ORDER BY actions DESC 
        LIMIT 20
      `, [filters.startDate, filters.endDate]);

      // Estatísticas por gravação
      const recordingStats = await this.pool.query(`
        SELECT recording_id, COUNT(*) as actions,
               COUNT(DISTINCT user_id) as users_accessed,
               MAX(timestamp) as last_access
        FROM audit_logs 
        WHERE recording_id IS NOT NULL
        AND timestamp >= COALESCE($1, timestamp) 
        AND timestamp <= COALESCE($2, timestamp)
        GROUP BY recording_id 
        ORDER BY actions DESC 
        LIMIT 20
      `, [filters.startDate, filters.endDate]);

      return {
        summary: {
          totalLogs: logs.pagination.total,
          period: {
            start: filters.startDate || 'início',
            end: filters.endDate || 'agora'
          }
        },
        statistics: {
          byAction: actionStats.rows,
          byUser: userStats.rows,
          byRecording: recordingStats.rows
        },
        recentLogs: logs.logs.slice(0, 10),
        generatedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('[Audit] Erro ao gerar relatório:', error);
      throw new Error(`Erro ao gerar relatório de auditoria: ${error.message}`);
    }
  }

  /**
   * Middleware para auditoria automática
   */
  createAuditMiddleware() {
    return (req, res, next) => {
      // Interceptar resposta para log automático
      const originalSend = res.send;
      
      res.send = function(data) {
        // Log da ação após resposta
        setImmediate(async () => {
          try {
            const actionData = {
              action: `${req.method} ${req.path}`,
              userId: req.user?.id,
              userName: req.user?.name,
              userRole: req.user?.role,
              ipAddress: req.ip || req.connection.remoteAddress,
              userAgent: req.get('User-Agent'),
              details: {
                method: req.method,
                path: req.path,
                query: req.query,
                statusCode: res.statusCode,
                responseTime: Date.now() - req.startTime
              },
              severity: res.statusCode >= 400 ? 'ERROR' : 'INFO',
              category: 'API_ACCESS'
            };

            await this.logAction(actionData);
          } catch (error) {
            console.error('[Audit] Erro no middleware de auditoria:', error);
          }
        });

        return originalSend.call(this, data);
      }.bind(this);

      req.startTime = Date.now();
      next();
    };
  }
}

module.exports = AuditService;
