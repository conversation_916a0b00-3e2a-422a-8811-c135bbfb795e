{"name": "sistema-residencia-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@types/bcryptjs": "^2.4.6", "@types/crypto-js": "^4.2.2", "@types/jsonwebtoken": "^9.0.10", "bcryptjs": "^3.0.2", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "google-auth-library": "^10.1.0", "googleapis": "^150.0.1", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "lottie-react": "^2.4.1", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}