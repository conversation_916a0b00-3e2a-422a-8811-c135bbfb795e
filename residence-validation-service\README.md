# Sistema de Validação de Residência

Sistema integrador que utiliza dados da Linha do Tempo do Google Maps para validar comprovantes de residência de forma automática e segura.

## 🌟 Funcionalidades Principais

- **Autorização OAuth2**: Integração segura com Google Maps Timeline API
- **Coleta Automática**: Processamento de dados de localização do usuário
- **Validação Inteligente**: Algoritmos que verificam permanência noturna (18h-6h) por 15+ dias consecutivos
- **Geração de Certificados**: PDFs preliminares com QR Code para verificação
- **⚖️ Autenticação Cartorária**: Orientação completa para validação legal em cartórios
- **Compliance LGPD**: Segurança e privacidade de dados garantidas
- **Interface Responsiva**: Design moderno, acessível e mobile-first
- **🎨 Microanimações**: Experiência do usuário fluida e profissional
- **📋 Modal de Consentimento**: Transparência total sobre coleta e uso de dados

## ⚖️ Validade Legal

> **IMPORTANTE**: Os comprovantes gerados pelo sistema são **PRELIMINARES** e só possuem **validade legal completa após autenticação em cartório**.

### 🏛️ Processo de Autenticação Cartorária

1. **Gere seu certificado preliminar** no sistema
2. **Reúna a documentação**: PDF gerado + RG/CNH + comprovante tradicional
3. **Vá ao cartório** (qualquer cartório brasileiro pode autenticar)
4. **Receba o documento autenticado** com validade legal

### 💰 Custos
- **Sistema**: Gratuito
- **Cartório**: R$ 8-15 (varia por estado)

### 📍 Onde Autenticar
- Qualquer cartório brasileiro
- Recomendamos ligar antes para confirmar procedimentos
- Processo leva de 1-3 dias úteis

## 🏗️ Arquitetura

### Frontend
- **Next.js 15**: Framework React com App Router
- **TypeScript**: Type safety e melhor experiência de desenvolvimento
- **Tailwind CSS**: Estilização utilitária e responsiva
- **Componentes React**: Interface modular e reutilizável

### Backend
- **Next.js API Routes**: APIs serverless integradas
- **Google APIs**: Timeline e OAuth2 para coleta de dados
- **JWT**: Autenticação segura de usuários
- **bcrypt**: Hash de senhas e dados sensíveis

### Integração
- **Google Maps Timeline API**: Coleta de dados de localização
- **jsPDF**: Geração de certificados em PDF
- **Algoritmos Personalizados**: Validação de residência por geolocalização

## 🚀 Como Executar

### Pré-requisitos
- Node.js 18+ 
- npm ou yarn
- Conta Google Cloud Platform
- Credenciais Google Maps API

### 1. Instalação
```bash
npm install
# ou
yarn install
```

### 2. Configuração
1. Copie o arquivo `.env.local` e configure suas variáveis:
```bash
cp .env.example .env.local
```

2. Configure as credenciais do Google:
   - Acesse [Google Cloud Console](https://console.cloud.google.com)
   - Crie um novo projeto ou selecione um existente
   - Ative as APIs: Maps Timeline API, OAuth2
   - Crie credenciais OAuth2 para aplicação web
   - Configure as URLs de redirect

3. Atualize o arquivo `.env.local`:
```env
GOOGLE_CLIENT_ID=seu_client_id_aqui
GOOGLE_CLIENT_SECRET=seu_client_secret_aqui
JWT_SECRET=sua_chave_jwt_super_secreta
ENCRYPTION_KEY=sua_chave_de_32_caracteres_aqui!!
```

### 3. Executar em Desenvolvimento
```bash
npm run dev
# ou
yarn dev
```

Acesse [http://localhost:3000](http://localhost:3000) no seu navegador.

### 4. **🔧 Configuração para Produção (Docker)**

#### Verificação Automática de Configuração
```powershell
# Windows (PowerShell)
.\check-env.ps1

# Linux/Mac
./check-env.sh
```

#### Configuração Manual
1. **Copie o arquivo de exemplo:**
```bash
cp .env.production.example .env.production
```

2. **Configure suas credenciais reais** no arquivo `.env.production`

3. **Execute com Docker:**
```bash
# Verificar configuração primeiro
.\check-env.ps1

# Executar em produção
docker-compose -f docker-compose.prod.yml up -d

# Verificar se está funcionando
curl http://localhost:3000/api/debug/env
```

#### 🚨 **Troubleshooting OAuth**
Se encontrar erro "OAuth credentials not configured":
- Consulte: `TROUBLESHOOTING_OAUTH.md`
- Execute: `.\check-env.ps1` para diagnóstico automático
- Veja: `OAUTH_SETUP_GUIDE.md` para configuração completa

### 4. Build para Produção
```bash
npm run build
npm start
# ou
yarn build
yarn start
```

## 📁 Estrutura do Projeto

```
src/
├── app/                     # App Router (Next.js 13+)
│   ├── api/                 # API Routes
│   │   ├── auth/           # Autenticação OAuth2
│   │   ├── timeline/       # Coleta de dados
│   │   └── certificate/    # Geração de certificados
│   ├── dashboard/          # Painel do usuário
│   ├── globals.css         # Estilos globais
│   ├── layout.tsx          # Layout principal
│   └── page.tsx            # Página inicial
├── components/             # Componentes React
├── lib/                    # Configurações e serviços
│   ├── config.ts          # Configurações da app
│   └── google-auth.ts     # Serviço Google OAuth
├── types/                  # Definições TypeScript
├── utils/                  # Utilitários e algoritmos
│   └── validation.ts      # Algoritmos de validação
└── styles/                # Estilos adicionais
```

## 🔒 Segurança e Privacidade

### LGPD Compliance
- ✅ Consentimento explícito do usuário
- ✅ Criptografia de dados sensíveis
- ✅ Retenção limitada de dados (90 dias)
- ✅ Direito de exclusão e portabilidade
- ✅ Transparência no processamento

### Medidas de Segurança
- 🔐 Autenticação OAuth2 com Google
- 🔐 Tokens JWT com expiração
- 🔐 HTTPS obrigatório em produção
- 🔐 Rate limiting nas APIs
- 🔐 Validação de entrada em todas as APIs
- 🔐 Logs de auditoria e monitoramento

## 🤖 Algoritmo de Validação

### Critérios de Validação
1. **Período Noturno**: Das 18h às 6h do dia seguinte
2. **Duração Mínima**: 15 dias consecutivos
3. **Raio de Tolerância**: Máximo 100 metros do endereço
4. **Confiabilidade**: Score mínimo de 70%
5. **Pontos de Dados**: Mínimo de dados para análise estatística

### Processo de Validação
1. **Coleta**: Dados da Timeline via API autorizada
2. **Filtragem**: Apenas período noturno (18h-6h)
3. **Geolocalização**: Cálculo de distância do endereço
4. **Análise Temporal**: Verificação de consecutividade
5. **Score**: Cálculo de confiabilidade
6. **Certificação**: Geração de comprovante se aprovado

### 📊 APIs Disponíveis

### Autenticação
- `GET /api/auth/google` - Inicia OAuth2
- `GET /api/auth/callback` - Callback OAuth2

### Validação
- `POST /api/timeline/collect` - Coleta e valida dados

### Certificados
- `POST /api/certificate/generate` - Gera certificado preliminar
- `GET /api/certificate/generate?id=X` - Download PDF

### Monitoramento
- `GET /api/health` - Health check do sistema

## 🎨 Interface do Usuário

### Página Inicial
- Apresentação do sistema com avisos legais claros
- **Modal de consentimento** antes da autorização Google
- Botão de autorização com microanimações
- **FAQ detalhado** sobre autenticação cartorária
- Informações de segurança/LGPD

### Dashboard
- **Aviso destacado** sobre validade legal
- Formulário de nova validação
- **Aba de Autenticação Cartorária** com guia completo
- Histórico de validações
- Lista de certificados com lembretes de autenticação
- Download de PDFs

### Páginas Legais
- **Termos de Uso** atualizados com avisos sobre autenticação
- **Política de Privacidade** detalhada e conforme LGPD

## 🎯 Melhorias de Apresentação Implementadas

### ✅ Melhorias Menos Invasivas (Concluídas)

#### Ajustes na Interface (UI/UX)
- ✅ **Avisos legais claros** na página inicial e certificados
- ✅ **Botão CTA "Autenticar em Cartório"** no dashboard
- ✅ **Microanimações** com componentes React animados
- ✅ **Seção FAQ detalhada** sobre processo cartorário
- ✅ **Melhorias de acessibilidade** (contraste, foco, alt text)

#### Mensagens de Conformidade Legal
- ✅ **Termos de Uso** atualizados com validade legal
- ✅ **Política de Privacidade** reforçando LGPD
- ✅ **Modal de consentimento** antes da autorização Google
- ✅ **Avisos em todos os certificados** sobre autenticação

#### Experiência do Usuário
- ✅ **Componentes animados** para feedback visual
- ✅ **Cards com hover effects** 
- ✅ **Botões com animações de clique**
- ✅ **Fade-in sections** para conteúdo
- ✅ **Indicadores de progresso** animados

### 🔄 Próximas Melhorias (Planejadas)

#### Melhorias Moderadamente Invasivas
- 🔲 **Integração com APIs de cartórios** para consulta
- 🔲 **Sistema de notificações** por email/SMS
- 🔲 **Dashboard de status** de autenticação
- 🔲 **Upload de documentos** autenticados

#### Melhorias Mais Invasivas
- 🔲 **Parceria direta com cartórios** para processo integrado
- 🔲 **Assinatura digital** com certificados ICP-Brasil
- 🔲 **API pública** para terceiros consultarem
- 🔲 **Blockchain** para verificação de autenticidade

## 🔧 Configurações Avançadas

### Personalização de Validação
```typescript
const CONFIG = {
  VALIDATION: {
    MINIMUM_DAYS: 15,           // Dias mínimos
    NIGHT_START_TIME: '18:00',  // Início noturno
    NIGHT_END_TIME: '06:00',    // Fim noturno
    MAX_DISTANCE_FROM_HOME: 100, // Metros
    CONFIDENCE_THRESHOLD: 0.7,   // 70% confiança
  }
};
```

### Configuração de Certificados
```typescript
const CERTIFICATE = {
  VALID_FOR_MONTHS: 6,      // Validade em meses
  PDF_TEMPLATE: {           // Configurações PDF
    pageSize: 'A4',
    margins: { top: 50, right: 50, bottom: 50, left: 50 },
    fontSize: 12,
  },
};
```

## 🚀 Deploy

### Vercel (Recomendado)
```bash
npm install -g vercel
vercel --prod
```

### Docker
```bash
docker build -t sistema-residencia .
docker run -p 3000:3000 sistema-residencia
```

### Outras Plataformas
- Heroku
- AWS Amplify
- Netlify
- Railway

## 🤝 Contribuição

1. Fork o repositório
2. Crie uma branch para sua feature (`git checkout -b feature/nova-feature`)
3. Commit suas mudanças (`git commit -am 'Add nova feature'`)
4. Push para a branch (`git push origin feature/nova-feature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está licenciado sob a MIT License. Veja o arquivo [LICENSE](LICENSE) para detalhes.

## 🆘 Suporte

Para suporte técnico ou dúvidas:
- 📧 Email: <EMAIL>
- 📞 Telefone: (11) 99999-9999
- 💬 Chat: [Discord/Slack]

## 🔄 Changelog

### v1.0.0 (2024-12-22)
- ✨ Implementação inicial
- 🔐 Integração OAuth2 Google
- 🤖 Algoritmos de validação
- 📄 Geração de certificados PDF
- 🎨 Interface responsiva
- 🔒 Compliance LGPD

---

**Desenvolvido com ❤️ para simplificar a validação de comprovantes de residência**
