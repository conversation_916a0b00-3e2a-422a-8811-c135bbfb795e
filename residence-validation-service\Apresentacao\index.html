<!DOCTYPE html>
<html lang="pt-br">
<head>
  <meta charset="UTF-8">
  <title>ProvaSegura - Apresentação Completa</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');
    
    body {
      margin: 0;
      padding: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      font-family: 'Inter', 'Segoe UI', Arial, sans-serif;
      min-height: 100vh;
      overflow-x: hidden;
    }
    
    .header {
      text-align: center;
      padding: 40px 20px;
      color: white;
    }
    
    .header h1 {
      font-size: 3rem;
      font-weight: 800;
      margin-bottom: 16px;
    }
    
    .header p {
      font-size: 1.2rem;
      opacity: 0.9;
    }
    
    .slides-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 30px;
      max-width: 1200px;
      margin: 0 auto;
      padding: 40px 20px;
    }
    
    .slide-card {
      background: white;
      border-radius: 20px;
      padding: 30px;
      text-align: center;
      box-shadow: 0 10px 30px rgba(0,0,0,0.1);
      transition: all 0.3s ease;
      text-decoration: none;
      color: inherit;
      border: 2px solid transparent;
    }
    
    .slide-card:hover {
      transform: translateY(-8px);
      box-shadow: 0 20px 40px rgba(0,0,0,0.15);
      border-color: rgba(102, 126, 234, 0.3);
    }
    
    .slide-number {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 700;
      font-size: 1.5rem;
      margin: 0 auto 20px;
    }
    
    .slide-title {
      font-size: 1.3rem;
      font-weight: 700;
      color: #1e293b;
      margin-bottom: 12px;
    }
    
    .slide-description {
      font-size: 1rem;
      color: #64748b;
      line-height: 1.5;
    }
    
    .navigation {
      position: fixed;
      bottom: 30px;
      right: 30px;
      display: flex;
      gap: 15px;
    }
    
    .nav-btn {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      cursor: pointer;
      font-size: 1.2rem;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
      transition: transform 0.2s ease;
    }
    
    .nav-btn:hover {
      transform: scale(1.1);
    }
    
    .fullscreen-btn {
      position: fixed;
      top: 30px;
      right: 30px;
      background: rgba(255,255,255,0.9);
      color: #1e293b;
      border: none;
      padding: 12px 20px;
      border-radius: 25px;
      cursor: pointer;
      font-weight: 600;
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;
    }
    
    .fullscreen-btn:hover {
      background: white;
      transform: translateY(-2px);
    }
  </style>
</head>
<body>

<div class="header">
  <h1>ProvaSegura</h1>
  <p>Apresentação para Investidores - Navegação entre Slides</p>
</div>

<button class="fullscreen-btn" onclick="toggleFullscreen()">Tela Cheia</button>

<div class="slides-grid">
  <a href="slide-01-capa.html" class="slide-card">
    <div class="slide-number">1</div>
    <div class="slide-title">Capa</div>
    <div class="slide-description">ProvaSegura: A Solução Definitiva para Comprovação de Residência e Localização</div>
  </a>
  
  <a href="slide-02-problema.html" class="slide-card">
    <div class="slide-number">2</div>
    <div class="slide-title">O Problema</div>
    <div class="slide-description">Fraudes e Ineficiências na Comprovação de Localização</div>
  </a>
  
  <a href="slide-03-solucao.html" class="slide-card">
    <div class="slide-number">3</div>
    <div class="slide-title">A Solução</div>
    <div class="slide-description">Uma Plataforma Digital Confiável</div>
  </a>
  
  <a href="slide-04-mercado.html" class="slide-card">
    <div class="slide-number">4</div>
    <div class="slide-title">Mercado</div>
    <div class="slide-description">Um Mercado de Bilhões</div>
  </a>
  
  <a href="slide-05-aplicacoes.html" class="slide-card">
    <div class="slide-number">5</div>
    <div class="slide-title">Aplicações</div>
    <div class="slide-description">Onde ProvaSegura Faz a Diferença</div>
  </a>
  
  <a href="slide-06-diferencial.html" class="slide-card">
    <div class="slide-number">6</div>
    <div class="slide-title">Diferencial</div>
    <div class="slide-description">Nosso Diferencial Competitivo</div>
  </a>
  
  <a href="slide-07-modelo.html" class="slide-card">
    <div class="slide-number">7</div>
    <div class="slide-title">Modelo de Negócio</div>
    <div class="slide-description">Como Ganhamos Dinheiro</div>
  </a>
  
  <a href="slide-08-tracao.html" class="slide-card">
    <div class="slide-number">8</div>
    <div class="slide-title">Tração</div>
    <div class="slide-description">Estamos Prontos para Crescer</div>
  </a>
  
  <a href="slide-09-investimento.html" class="slide-card">
    <div class="slide-number">9</div>
    <div class="slide-title">Investimento</div>
    <div class="slide-description">Junte-se à Revolução da Comprovação Digital</div>
  </a>
  
  <a href="slide-10-equipe.html" class="slide-card">
    <div class="slide-number">10</div>
    <div class="slide-title">Equipe e Visão</div>
    <div class="slide-description">Quem Somos e Para Onde Vamos</div>
  </a>
  
  <a href="slide-11-cta.html" class="slide-card">
    <div class="slide-number">11</div>
    <div class="slide-title">Call to Action</div>
    <div class="slide-description">Invista no Futuro da Validação Digital</div>
  </a>
</div>

<div class="navigation">
  <button class="nav-btn" onclick="goToSlide(1)" title="Primeiro Slide">⏮</button>
  <button class="nav-btn" onclick="goToSlide(11)" title="Último Slide">⏭</button>
</div>

<script>
function toggleFullscreen() {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen();
  } else {
    document.exitFullscreen();
  }
}

function goToSlide(number) {
  const slideFiles = {
    1: 'slide-01-capa.html',
    2: 'slide-02-problema.html',
    3: 'slide-03-solucao.html',
    4: 'slide-04-mercado.html',
    5: 'slide-05-aplicacoes.html',
    6: 'slide-06-diferencial.html',
    7: 'slide-07-modelo.html',
    8: 'slide-08-tracao.html',
    9: 'slide-09-investimento.html',
    10: 'slide-10-equipe.html',
    11: 'slide-11-cta.html'
  };
  
  window.open(slideFiles[number], '_blank');
}

// Atalhos de teclado
document.addEventListener('keydown', function(e) {
  if (e.key >= '1' && e.key <= '9') {
    goToSlide(parseInt(e.key));
  } else if (e.key === '0') {
    goToSlide(10);
  } else if (e.key === 'F11' || e.key === 'f') {
    e.preventDefault();
    toggleFullscreen();
  }
});
</script>

</body>
</html>
