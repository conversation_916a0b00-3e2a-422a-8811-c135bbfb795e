#!/usr/bin/env node

/**
 * Script de teste para verificar se as rotas de analytics estão funcionando
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3001';

async function testAnalyticsEndpoints() {
  console.log('🧪 Testando endpoints de analytics...\n');

  const endpoints = [
    { name: 'Dashboard', url: '/api/analytics/dashboard' },
    { name: 'Trends', url: '/api/analytics/trends?period=month' },
    { name: 'Users', url: '/api/analytics/users' },
    { name: 'Compliance', url: '/api/analytics/compliance' }
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`📊 Testando ${endpoint.name}...`);
      
      const response = await axios.get(`${BASE_URL}${endpoint.url}`, {
        timeout: 5000,
        validateStatus: () => true // Aceitar qualquer status
      });

      if (response.status === 200 && response.data.success) {
        console.log(`✅ ${endpoint.name}: OK`);
        console.log(`   Status: ${response.status}`);
        console.log(`   Success: ${response.data.success}`);
        
        if (response.data.warning) {
          console.log(`   ⚠️  Warning: ${response.data.warning}`);
        }
        
        // Mostrar alguns dados para verificação
        if (endpoint.name === 'Dashboard' && response.data.data) {
          console.log(`   Total Recordings: ${response.data.data.totalRecordings}`);
          console.log(`   Total Users: ${response.data.data.totalUsers}`);
        }
        
        if (endpoint.name === 'Trends' && response.data.data) {
          console.log(`   Trends Count: ${response.data.data.length}`);
        }
        
        if (endpoint.name === 'Users' && response.data.data) {
          console.log(`   Users Count: ${response.data.data.length}`);
        }
        
        if (endpoint.name === 'Compliance' && response.data.data) {
          console.log(`   Compliance Rate: ${response.data.data.complianceRate}%`);
        }
        
      } else {
        console.log(`❌ ${endpoint.name}: FALHOU`);
        console.log(`   Status: ${response.status}`);
        console.log(`   Success: ${response.data?.success || false}`);
        console.log(`   Error: ${response.data?.error || 'Erro desconhecido'}`);
      }
      
    } catch (error) {
      console.log(`💥 ${endpoint.name}: ERRO DE CONEXÃO`);
      console.log(`   Erro: ${error.message}`);
      
      if (error.code === 'ECONNREFUSED') {
        console.log(`   💡 Dica: Verifique se o servidor está rodando em ${BASE_URL}`);
      }
    }
    
    console.log(''); // Linha em branco
  }

  console.log('🏁 Teste de analytics concluído!\n');
}

// Função para testar conectividade básica
async function testServerConnection() {
  try {
    console.log('🔌 Testando conectividade com o servidor...');
    const response = await axios.get(`${BASE_URL}/health`, {
      timeout: 3000,
      validateStatus: () => true
    });
    
    if (response.status === 200) {
      console.log('✅ Servidor está respondendo\n');
      return true;
    } else {
      console.log(`⚠️  Servidor respondeu com status ${response.status}\n`);
      return false;
    }
  } catch (error) {
    console.log('❌ Servidor não está respondendo');
    console.log(`   Erro: ${error.message}`);
    console.log(`   💡 Dica: Execute 'npm start' no diretório backend\n`);
    return false;
  }
}

// Executar testes
async function main() {
  console.log('🚀 Iniciando testes de analytics do CartorioTech\n');
  
  const serverOnline = await testServerConnection();
  
  if (serverOnline) {
    await testAnalyticsEndpoints();
  } else {
    console.log('❌ Não é possível executar testes - servidor offline');
    process.exit(1);
  }
  
  console.log('✨ Testes concluídos!');
}

// Executar se chamado diretamente
if (require.main === module) {
  main().catch(error => {
    console.error('💥 Erro fatal nos testes:', error.message);
    process.exit(1);
  });
}

module.exports = { testAnalyticsEndpoints, testServerConnection };
