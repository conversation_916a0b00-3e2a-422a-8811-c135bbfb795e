const express = require('express');
const ComplianceService = require('../services/complianceService');
const queueManager = require('../services/queueManager');

const router = express.Router();
const complianceService = new ComplianceService();

/**
 * GET /api/ai/compliance
 * Informações sobre o serviço de compliance
 */
router.get('/', (req, res) => {
  res.json({
    success: true,
    service: 'Serviço de Análise de Compliance',
    version: '1.0.0',
    availableEndpoints: [
      'POST /api/ai/compliance/analyze - Análise de compliance LGPD',
      'POST /api/ai/compliance/consent - Verificar consentimento',
      'POST /api/ai/compliance/privacy - Análise de privacidade',
      'GET /api/ai/compliance/report/:id - Relatório de compliance'
    ],
    regulations: [
      'LGPD (Lei Geral de Proteção de Dados)',
      'Marco Civil da Internet',
      'Código de Defesa do Consumidor'
    ],
    features: [
      'Detecção de dados pessoais',
      'Verificação de consentimento',
      'Análise de finalidade',
      'Auditoria de privacidade',
      'Relatórios detalhados',
      'Recomendações de adequação'
    ],
    timestamp: new Date().toISOString()
  });
});

/**
 * POST /api/ai/compliance/analyze
 * Analisar compliance LGPD de uma transcrição
 */
router.post('/analyze', async (req, res) => {
  try {
    const { transcription, consentData, metadata } = req.body;

    if (!transcription) {
      return res.status(400).json({
        success: false,
        message: 'Transcrição é obrigatória'
      });
    }

    const result = await complianceService.analyzeLGPDCompliance(
      transcription,
      consentData,
      metadata
    );

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erro na análise de compliance',
      error: error.message
    });
  }
});

/**
 * POST /api/ai/compliance/recording/:recordingId
 * Analisar compliance de uma gravação específica
 */
router.post('/recording/:recordingId', async (req, res) => {
  try {
    const { recordingId } = req.params;
    
    // Buscar dados da gravação e transcrição
    const axios = require('axios');
    const backendUrl = process.env.BACKEND_URL || 'http://backend:3001';
    
    const [recordingResponse, transcriptionResponse] = await Promise.all([
      axios.get(`${backendUrl}/api/recordings/${recordingId}`),
      axios.get(`http://localhost:3003/api/ai/transcription/recording/${recordingId}`)
    ]);

    const recording = recordingResponse.data;
    const transcriptionData = transcriptionResponse.data?.data;

    if (!recording) {
      return res.status(404).json({
        success: false,
        message: 'Gravação não encontrada'
      });
    }

    if (!transcriptionData) {
      return res.status(400).json({
        success: false,
        message: 'Transcrição não encontrada. Execute a transcrição primeiro.'
      });
    }

    const result = await complianceService.analyzeLGPDCompliance(
      transcriptionData.text,
      {
        name: recording.user_name,
        purpose: recording.purpose,
        timestamp: recording.consent_timestamp,
        ip: recording.consent_ip,
        explicit_consent: recording.consent_given
      },
      {
        created_at: recording.created_at,
        user_name: recording.user_name,
        recording_type: recording.additional_data?.type
      }
    );

    // Salvar resultado no banco de dados
    const aiDatabase = require('../database/aiDatabase');
    await aiDatabase.saveComplianceAnalysis(recordingId, result);

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erro na análise de compliance da gravação',
      error: error.message
    });
  }
});

/**
 * POST /api/ai/compliance/batch
 * Analisar compliance de múltiplas gravações
 */
router.post('/batch', async (req, res) => {
  try {
    const { recordingIds } = req.body;

    if (!recordingIds || !Array.isArray(recordingIds)) {
      return res.status(400).json({
        success: false,
        message: 'Lista de IDs de gravações é obrigatória'
      });
    }

    // Adicionar trabalhos à fila
    const jobs = [];
    for (const recordingId of recordingIds) {
      const job = await queueManager.addComplianceJob(recordingId);
      jobs.push({
        recordingId,
        jobId: job.id,
        status: 'queued'
      });
    }

    res.json({
      success: true,
      message: `${jobs.length} trabalhos de análise de compliance adicionados à fila`,
      data: jobs
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erro ao processar lote de análises',
      error: error.message
    });
  }
});

/**
 * GET /api/ai/compliance/recording/:recordingId
 * Obter análise de compliance de uma gravação
 */
router.get('/recording/:recordingId', async (req, res) => {
  try {
    const { recordingId } = req.params;
    
    const aiDatabase = require('../database/aiDatabase');
    const analysis = await aiDatabase.getComplianceAnalysis(recordingId);

    if (!analysis) {
      return res.status(404).json({
        success: false,
        message: 'Análise de compliance não encontrada'
      });
    }

    res.json({
      success: true,
      data: analysis
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erro ao buscar análise de compliance',
      error: error.message
    });
  }
});

/**
 * GET /api/ai/compliance/report
 * Gerar relatório de compliance
 */
router.get('/report', async (req, res) => {
  try {
    const { 
      recordingIds, 
      period = '30d',
      startDate,
      endDate 
    } = req.query;

    let ids = recordingIds;
    if (typeof recordingIds === 'string') {
      ids = recordingIds.split(',');
    }

    const report = await complianceService.generateComplianceReport(ids, period);

    res.json({
      success: true,
      data: report
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erro ao gerar relatório de compliance',
      error: error.message
    });
  }
});

/**
 * GET /api/ai/compliance/dashboard
 * Dashboard de compliance com métricas principais
 */
router.get('/dashboard', async (req, res) => {
  try {
    const aiDatabase = require('../database/aiDatabase');
    
    const [
      totalAnalyses,
      recentAnalyses,
      riskDistribution,
      averageScore
    ] = await Promise.all([
      aiDatabase.getTotalComplianceAnalyses(),
      aiDatabase.getRecentComplianceAnalyses(7), // últimos 7 dias
      aiDatabase.getComplianceRiskDistribution(),
      aiDatabase.getAverageComplianceScore()
    ]);

    const dashboard = {
      summary: {
        totalAnalyses,
        recentAnalyses: recentAnalyses.length,
        averageScore: Math.round(averageScore * 100) / 100,
        complianceRate: recentAnalyses.filter(a => a.compliance_score >= 80).length / Math.max(recentAnalyses.length, 1)
      },
      riskDistribution,
      trends: {
        last7Days: recentAnalyses.map(a => ({
          date: a.created_at,
          score: a.compliance_score,
          riskLevel: a.risk_level
        }))
      },
      alerts: recentAnalyses
        .filter(a => a.risk_level === 'critico' || a.risk_level === 'alto')
        .slice(0, 5)
        .map(a => ({
          recordingId: a.recording_id,
          riskLevel: a.risk_level,
          score: a.compliance_score,
          date: a.created_at
        }))
    };

    res.json({
      success: true,
      data: dashboard
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erro ao carregar dashboard de compliance',
      error: error.message
    });
  }
});

/**
 * POST /api/ai/compliance/remediate
 * Sugerir ações de remediação para problemas de compliance
 */
router.post('/remediate', async (req, res) => {
  try {
    const { recordingId, issues } = req.body;

    if (!recordingId || !issues) {
      return res.status(400).json({
        success: false,
        message: 'ID da gravação e lista de problemas são obrigatórios'
      });
    }

    // Buscar análise existente
    const aiDatabase = require('../database/aiDatabase');
    const analysis = await aiDatabase.getComplianceAnalysis(recordingId);

    if (!analysis) {
      return res.status(404).json({
        success: false,
        message: 'Análise de compliance não encontrada'
      });
    }

    // Gerar plano de remediação usando IA
    const remediationPlan = await complianceService.generateRemediationPlan(analysis, issues);

    res.json({
      success: true,
      data: remediationPlan
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erro ao gerar plano de remediação',
      error: error.message
    });
  }
});

module.exports = router;
