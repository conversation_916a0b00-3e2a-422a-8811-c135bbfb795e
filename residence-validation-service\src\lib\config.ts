// Configurações da aplicação
export const CONFIG = {
  // Configurações de validação de residência
  VALIDATION: {
    MINIMUM_DAYS: 15,
    NIGHT_START_TIME: '18:00',
    NIGHT_END_TIME: '06:00',
    MAX_DISTANCE_FROM_HOME: 100, // metros
    MINIMUM_PRESENCE_HOURS: 8,
    CONFIDENCE_THRESHOLD: 0.7,
    CONSECUTIVE_DAYS_REQUIRED: true,
  },
  // Configurações do Google Maps Timeline
  GOOGLE: {
    TIMELINE_API_URL: 'https://timeline.googleapis.com/v1',    SCOPES: [
      'https://www.googleapis.com/auth/userinfo.email',
      'https://www.googleapis.com/auth/userinfo.profile',
      // Escopo específico para acessar dados do Timeline (histórico de localização)
      'https://www.googleapis.com/auth/timeline.readonly',
      // Alternativa caso o escopo acima não funcione:
      // 'https://www.googleapis.com/auth/location.history.read'
    ] as string[],
    REDIRECT_URI: process.env.NEXT_PUBLIC_GOOGLE_REDIRECT_URI || 'http://localhost:3000/api/auth/callback',
  },

  // Configurações de segurança
  SECURITY: {
    JWT_EXPIRES_IN: '7d',
    BCRYPT_SALT_ROUNDS: 12,
    RATE_LIMIT: {
      windowMs: 15 * 60 * 1000, // 15 minutos
      max: 10, // máximo 10 tentativas por IP
    },
  },

  // Configurações do certificado
  CERTIFICATE: {
    VALID_FOR_MONTHS: 6,
    PDF_TEMPLATE: {
      pageSize: 'A4',
      margins: { top: 50, right: 50, bottom: 50, left: 50 },
      fontSize: 12,
      fontFamily: 'Arial',
    },
  },

  // URLs da aplicação
  URLS: {
    BASE_URL: process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000',
    API_BASE: '/api',
    FRONTEND_BASE: '/',
  },

  // Configurações LGPD
  LGPD: {
    DATA_RETENTION_DAYS: 90,
    CONSENT_REQUIRED: true,
    ALLOW_DATA_EXPORT: true,
    ALLOW_DATA_DELETION: true,
  },
} as const;

// Configurações de ambiente
export const ENV = {
  NODE_ENV: process.env.NODE_ENV || 'development',
  GOOGLE_CLIENT_ID: process.env.GOOGLE_CLIENT_ID || '',
  GOOGLE_CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET || '',
  JWT_SECRET: process.env.JWT_SECRET || 'fallback-secret-key',
  DATABASE_URL: process.env.DATABASE_URL || '',
  ENCRYPTION_KEY: process.env.ENCRYPTION_KEY || '',
} as const;

// Validação de variáveis de ambiente obrigatórias
export const validateEnvVars = () => {
  const required = [
    'GOOGLE_CLIENT_ID',
    'GOOGLE_CLIENT_SECRET',
    'JWT_SECRET',
    'ENCRYPTION_KEY',
  ];

  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    throw new Error(`Variáveis de ambiente obrigatórias não definidas: ${missing.join(', ')}`);
  }
};

// Configurações de desenvolvimento
export const DEV_CONFIG = {
  MOCK_DATA: ENV.NODE_ENV === 'development',
  DEBUG_LOGS: ENV.NODE_ENV === 'development',
  SKIP_VALIDATION: false, // Para testes
};
