{"name": "cartorio-web-app", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.16", "@mui/material": "^5.14.16", "@mui/x-date-pickers": "^6.18.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.60", "@types/react": "^18.2.34", "@types/react-dom": "^18.2.14", "axios": "^1.6.0", "crypto-js": "^4.1.1", "date-fns": "^2.30.0", "dayjs": "^1.11.10", "jwt-decode": "^4.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.18.0", "react-scripts": "5.0.1", "react-dropzone": "^14.2.3", "recharts": "^2.8.0", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "workbox-background-sync": "^7.0.0", "yup": "^1.3.3", "workbox-broadcast-update": "^7.0.0", "workbox-cacheable-response": "^7.0.0", "workbox-core": "^7.0.0", "workbox-expiration": "^7.0.0", "workbox-google-analytics": "^7.0.0", "workbox-navigation-preload": "^7.0.0", "workbox-precaching": "^7.0.0", "workbox-range-requests": "^7.0.0", "workbox-routing": "^7.0.0", "workbox-strategies": "^7.0.0", "workbox-streams": "^7.0.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/crypto-js": "^4.1.3"}}