<!DOCTYPE html>
<html lang="pt-br">
<head>
  <meta charset="UTF-8">
  <title>ProvaSegura - Slide 3: A Solução</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');
    
    body {
      margin: 0;
      padding: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      font-family: 'Inter', 'Segoe UI', Arial, sans-serif;
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }
      .slide {
      width: 90vw;
      max-width: 1280px;
      height: 80vh;
      max-height: 720px;
      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
      box-shadow: 0 20px 60px rgba(0,0,0,0.15), 0 8px 32px rgba(0,0,0,0.08);
      border-radius: 24px;
      display: flex;
      flex-direction: row;
      overflow: hidden;
      position: relative;
      border: 1px solid rgba(255,255,255,0.2);
    }
    
    .slide::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #10b981 0%, #059669 50%, #047857 100%);
      border-radius: 24px 24px 0 0;
    }
      .slide-content {
      padding: 40px 60px;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      position: relative;
      z-index: 2;
      overflow-y: auto;
    }
      .slide-title {
      font-size: 2.5rem;
      font-weight: 800;
      background: linear-gradient(135deg, #059669 0%, #10b981 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-bottom: 24px;
      line-height: 1.1;
      letter-spacing: -0.02em;
    }
      .slide-text {
      font-size: 1rem;
      color: #334155;
      line-height: 1.6;
      font-weight: 400;
    }
    
    .slide-text ul {
      padding-left: 0;
      list-style: none;
    }
      .slide-text li {
      position: relative;
      margin-bottom: 12px;
      padding: 12px 15px 12px 40px;
      background: rgba(16, 185, 129, 0.05);
      border-left: 4px solid #10b981;
      border-radius: 8px;
    }
      .slide-text li::before {
      content: '✓';
      position: absolute;
      left: 15px;
      top: 12px;
      font-size: 1rem;
      color: #10b981;
      font-weight: bold;
    }
    
    .slide-text b {
      color: #065f46;
      font-weight: 700;
    }
      .highlight-box {
      background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
      padding: 18px;
      border-radius: 12px;
      margin-top: 18px;
      border: 2px solid rgba(16, 185, 129, 0.2);
    }
      .slide-img {
      flex: 0 0 300px;
      background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      overflow: hidden;
    }
    
    .slide-img::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 30% 70%, rgba(102, 126, 234, 0.1) 0%, transparent 50%);
    }
    
    .slide-img svg {
      filter: drop-shadow(0 15px 35px rgba(16, 185, 129, 0.2));
      z-index: 1;
      position: relative;
    }
    
    .slide-number {
      position: absolute;
      top: 30px;
      right: 40px;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-weight: 700;
      font-size: 1.3rem;
      box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
    }    @media print {
      body { background: #fff; }
      .slide { box-shadow: none; border: 1px solid #e2e8f0; }
    }

    @media (max-width: 768px) {
      .slide {
        flex-direction: column;
        width: 95vw;
        height: 90vh;
      }
      
      .slide-content {
        padding: 30px 20px;
      }
      
      .slide-title {
        font-size: 2rem;
      }
      
      .slide-img {
        flex: 0 0 200px;
      }
      
      .slide-img svg {
        width: 200px;
        height: 280px;
      }
      
      .highlight-box {
        padding: 15px;
        margin-top: 15px;
      }
    }

    .navigation {
      position: fixed;
      bottom: 30px;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      gap: 15px;
      z-index: 1000;
    }

    .nav-button {
      background: rgba(255, 255, 255, 0.9);
      border: none;
      padding: 12px 24px;
      border-radius: 25px;
      color: #1e293b;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      text-decoration: none;
      display: inline-block;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .nav-button:hover {
      background: white;
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }
  </style>
</head>
<body>

<div class="slide">
  <div class="slide-number">3</div>
  <div class="slide-content">
    <div class="slide-title">Uma Plataforma Digital Confiável</div>
    <div class="slide-text">
      <b>O que é?</b> ProvaSegura usa a linha do tempo do Google e autenticação biométrica (digital ou facial) para comprovar onde uma pessoa estava, com segurança e rapidez.<br><br>
      <b>Como funciona?</b>
      <ul>
        <li>O usuário faz login com biometria no app.</li>
        <li>Dados do Google Maps confirmam a localização (ex.: residência, farmácia).</li>
        <li>Relatórios digitais são gerados, aceitos por bancos, tribunais e empresas.</li>
      </ul>
      
      <div class="highlight-box">
        <b>Diferencial:</b> Combina biometria e geolocalização para eliminar fraudes e agilizar processos.
      </div>
    </div>
  </div>
  <div class="slide-img">
    <!-- App ProvaSegura melhorado -->
    <svg width="280" height="400" viewBox="0 0 280 400">
      <!-- Smartphone principal -->
      <rect x="40" y="40" width="200" height="320" rx="40" fill="#fff" stroke="#10b981" stroke-width="8"/>
      
      <!-- Tela do app -->
      <rect x="60" y="80" width="160" height="240" rx="20" fill="#f0fdf4"/>
      
      <!-- Header com logo -->
      <rect x="70" y="100" width="140" height="40" rx="12" fill="#10b981"/>
      <text x="140" y="125" text-anchor="middle" font-size="16" fill="#fff" font-weight="bold">ProvaSegura</text>
      
      <!-- Seção de relatório -->
      <rect x="80" y="160" width="120" height="80" rx="12" fill="#fff" stroke="#d1fae5" stroke-width="2"/>
      
      <!-- Ícone de localização -->
      <circle cx="110" cy="180" r="12" fill="#10b981"/>
      <path d="M110 170 L118 185 L110 200 L102 185 Z" fill="#fff"/>
      
      <!-- Dados do relatório -->
      <rect x="130" y="175" width="60" height="8" rx="4" fill="#065f46"/>
      <rect x="130" y="190" width="50" height="6" rx="3" fill="#16a34a"/>
      <rect x="130" y="205" width="45" height="6" rx="3" fill="#22c55e"/>
      
      <!-- Status verificado -->
      <circle cx="190" cy="210" r="15" fill="#10b981"/>
      <path d="M185 210 L188 213 L195 206" stroke="#fff" stroke-width="3" fill="none" stroke-linecap="round"/>
      
      <!-- Seção biometria -->
      <rect x="80" y="260" width="120" height="40" rx="12" fill="#f0fdf4" stroke="#a7f3d0" stroke-width="2"/>
      <circle cx="140" cy="280" r="12" fill="#fff" stroke="#10b981" stroke-width="3"/>
      <circle cx="140" cy="280" r="6" fill="#10b981"/>
      
      <!-- Elementos de segurança -->
      <circle cx="20" cy="60" r="15" fill="#3b82f6"/>
      <text x="20" y="68" text-anchor="middle" font-size="16" fill="#fff">🔒</text>
      
      <circle cx="260" cy="100" r="15" fill="#f59e0b"/>
      <text x="260" y="108" text-anchor="middle" font-size="16" fill="#fff">🛡️</text>
      
      <!-- Dados fluindo -->
      <path d="M40 200 Q20 220 40 240" stroke="#10b981" stroke-width="4" fill="none" opacity="0.6"/>
      <path d="M240 200 Q260 220 240 240" stroke="#10b981" stroke-width="4" fill="none" opacity="0.6"/>
      
      <!-- Partículas de dados -->
      <circle cx="30" cy="180" r="3" fill="#22c55e" opacity="0.8"/>
      <circle cx="35" cy="200" r="2" fill="#16a34a" opacity="0.6"/>
      <circle cx="25" cy="220" r="4" fill="#10b981" opacity="0.7"/>
      
      <circle cx="250" cy="180" r="3" fill="#22c55e" opacity="0.8"/>
      <circle cx="245" cy="200" r="2" fill="#16a34a" opacity="0.6"/>
      <circle cx="255" cy="220" r="4" fill="#10b981" opacity="0.7"/>
      
      <!-- Ondas de conectividade -->
      <path d="M140 340 Q160 330 180 340" stroke="#10b981" stroke-width="3" fill="none" opacity="0.5"/>
      <path d="M130 350 Q160 320 190 350" stroke="#10b981" stroke-width="2" fill="none" opacity="0.4"/>
      <path d="M120 360 Q160 310 200 360" stroke="#10b981" stroke-width="1" fill="none" opacity="0.3"/>
    </svg>  </div>
</div>

<div class="navigation">
  <a href="slide-02-problema.html" class="nav-button">← Anterior</a>
  <a href="index.html" class="nav-button">📋 Índice</a>
  <a href="slide-04-mercado.html" class="nav-button">Próximo →</a>
</div>

<script>
document.addEventListener('keydown', function(e) {
  switch(e.key) {
    case 'ArrowLeft':
      window.location.href = 'slide-02-problema.html';
      break;
    case 'ArrowRight':
      window.location.href = 'slide-04-mercado.html';
      break;
    case 'Home':
      window.location.href = 'index.html';
      break;
    case 'F11':
      e.preventDefault();
      if (document.fullscreenElement) {
        document.exitFullscreen();
      } else {
        document.documentElement.requestFullscreen();
      }
      break;
  }
});
</script>

</body>
</html>
