'use client';

import { useState } from 'react';
import { AnimatedButton } from './AnimatedIcons';

interface ConsentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAccept: () => void;
}

export const ConsentModal = ({ isOpen, onClose, onAccept }: ConsentModalProps) => {
  const [hasReadTerms, setHasReadTerms] = useState(false);
  const [hasReadPrivacy, setHasReadPrivacy] = useState(false);

  if (!isOpen) return null;

  const canProceed = hasReadTerms && hasReadPrivacy;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-gray-900">
              🔒 Consentimento para Coleta de Dados
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Aviso Legal Principal */}
          <div className="bg-amber-50 border-l-4 border-amber-400 p-4 rounded-r-lg">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-amber-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-amber-800">⚖️ Importante - Validade Legal</h3>
                <div className="mt-2 text-sm text-amber-700">
                  <p className="font-semibold">
                    O comprovante gerado é PRELIMINAR e só possui validade legal após autenticação em cartório.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Explicação dos dados */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-3">
              📍 Dados que Vamos Acessar
            </h3>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <ul className="space-y-2 text-sm text-blue-800">
                <li className="flex items-start">
                  <svg className="w-4 h-4 text-blue-600 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span><strong>Histórico de localização</strong> do Google Timeline (onde você esteve e quando)</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-4 h-4 text-blue-600 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span><strong>Informações básicas</strong> da sua conta Google (nome e email)</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-4 h-4 text-blue-600 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span><strong>Endereço informado</strong> para validação de residência</span>
                </li>
              </ul>
            </div>
          </div>

          {/* Como usamos os dados */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-3">
              🎯 Como Usamos seus Dados
            </h3>
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <ul className="space-y-2 text-sm text-green-800">
                <li className="flex items-start">
                  <span className="font-medium mr-2">✅</span>
                  <span>Analisar padrões de permanência no endereço (18h às 6h)</span>
                </li>
                <li className="flex items-start">
                  <span className="font-medium mr-2">✅</span>
                  <span>Calcular score de confiabilidade da residência</span>
                </li>
                <li className="flex items-start">
                  <span className="font-medium mr-2">✅</span>
                  <span>Gerar certificado preliminar em PDF</span>
                </li>
                <li className="flex items-start">
                  <span className="font-medium mr-2">✅</span>
                  <span>Facilitar processo de autenticação cartorária</span>
                </li>
              </ul>
            </div>
          </div>

          {/* O que NÃO fazemos */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-3">
              🚫 O que NÃO Fazemos
            </h3>
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <ul className="space-y-2 text-sm text-red-800">
                <li className="flex items-start">
                  <span className="font-medium mr-2">❌</span>
                  <span>Não vendemos seus dados para terceiros</span>
                </li>
                <li className="flex items-start">
                  <span className="font-medium mr-2">❌</span>
                  <span>Não usamos para publicidade ou marketing</span>
                </li>
                <li className="flex items-start">
                  <span className="font-medium mr-2">❌</span>
                  <span>Não compartilhamos com outras empresas</span>
                </li>
                <li className="flex items-start">
                  <span className="font-medium mr-2">❌</span>
                  <span>Não armazenamos além do necessário (máximo 30 dias)</span>
                </li>
              </ul>
            </div>
          </div>

          {/* Seus Direitos */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-3">
              👤 Seus Direitos (LGPD)
            </h3>
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
              <div className="grid md:grid-cols-2 gap-3 text-sm text-purple-800">
                <div>
                  <span className="font-medium">🔍 Transparência:</span> Saber como processamos seus dados
                </div>
                <div>
                  <span className="font-medium">✏️ Correção:</span> Corrigir informações incorretas
                </div>
                <div>
                  <span className="font-medium">🗑️ Exclusão:</span> Deletar seus dados a qualquer momento
                </div>
                <div>
                  <span className="font-medium">📱 Portabilidade:</span> Exportar seus dados
                </div>
              </div>
            </div>
          </div>

          {/* Checkboxes de confirmação */}
          <div className="space-y-4">
            <div className="flex items-start">
              <input
                id="terms-check"
                type="checkbox"
                checked={hasReadTerms}
                onChange={(e) => setHasReadTerms(e.target.checked)}
                className="mt-1 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <label htmlFor="terms-check" className="ml-3 text-sm text-gray-700">
                Li e concordo com os{' '}
                <a 
                  href="/termos" 
                  target="_blank" 
                  className="text-blue-600 hover:underline font-medium"
                >
                  Termos de Uso
                </a>
                , incluindo a necessidade de autenticação cartorária para validade legal
              </label>
            </div>

            <div className="flex items-start">
              <input
                id="privacy-check"
                type="checkbox"
                checked={hasReadPrivacy}
                onChange={(e) => setHasReadPrivacy(e.target.checked)}
                className="mt-1 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <label htmlFor="privacy-check" className="ml-3 text-sm text-gray-700">
                Li e concordo com a{' '}
                <a 
                  href="/privacidade" 
                  target="_blank" 
                  className="text-blue-600 hover:underline font-medium"
                >
                  Política de Privacidade
                </a>
                {' '}e autorizo o processamento dos meus dados conforme descrito
              </label>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 flex space-x-4">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancelar
          </button>
          <AnimatedButton
            onClick={onAccept}
            disabled={!canProceed}
            className={`flex-1 px-4 py-2 text-white rounded-lg font-medium transition-colors ${
              canProceed 
                ? 'bg-blue-600 hover:bg-blue-700' 
                : 'bg-gray-400 cursor-not-allowed'
            }`}
          >
            {canProceed ? '✅ Aceitar e Continuar' : '📋 Leia os documentos acima'}
          </AnimatedButton>
        </div>
      </div>
    </div>
  );
};
